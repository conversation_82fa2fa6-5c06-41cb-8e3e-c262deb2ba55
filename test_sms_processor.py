#!/usr/bin/env python3
"""
Test script for SMS Processor
"""
import asyncio
import csv
import os
import tempfile
from app.customers.services.sms_processor import SMSProcessor

async def test_sms_processor():
    """Test the SMS processor with sample data"""
    
    # Create sample CSV data with more diverse financial SMS
    sample_data = [
        {
            'senderAddress': 'HDFCBANK',
            'text': 'Rs.1000 debited from A/c XX1234 on 01-01-2024. UPI/*********. Avl Bal: Rs.50000',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T10:00:00',
            'id': '1'
        },
        {
            'senderAddress': 'AMAZON',
            'text': 'Your OTP for login is 123456. Valid for 10 minutes.',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T11:00:00',
            'id': '2'
        },
        {
            'senderAddress': 'ICICIBANK',
            'text': 'Rs.5000 credited to A/c XX5678 on 01-01-2024. Ref: SALARY123',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T12:00:00',
            'id': '3'
        },
        {
            'senderAddress': 'FLIPKART',
            'text': 'Get 50% off on electronics! Limited time offer. Shop now.',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T13:00:00',
            'id': '4'
        },
        {
            'senderAddress': 'SBI',
            'text': 'Your SBI Credit Card XX5678 has been activated. Credit limit: Rs.100000',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T14:00:00',
            'id': '5'
        },
        {
            'senderAddress': 'PAYTM',
            'text': 'Rs.200 added to your Paytm wallet. Current balance: Rs.1500',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T15:00:00',
            'id': '6'
        },
        {
            'senderAddress': 'HDFC',
            'text': 'Loan EMI of Rs.15000 due on 15-01-2024. Outstanding: Rs.500000',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T16:00:00',
            'id': '7'
        }
    ]
    
    # Create temporary CSV file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as temp_file:
        writer = csv.DictWriter(temp_file, fieldnames=['senderAddress', 'text', 'phoneNumber', 'updateAt', 'id'])
        writer.writeheader()
        writer.writerows(sample_data)
        temp_file_path = temp_file.name
    
    try:
        # Initialize processor
        processor = SMSProcessor()
        customer_id = "test-customer-123"
        
        print("Testing SMS classification...")
        
        # Test individual SMS classification
        for sms in sample_data:
            sms_type = processor.classify_sms(sms['senderAddress'], sms['text'])
            print(f"SMS {sms['id']}: {sms_type} - {sms['text'][:50]}...")
        
        print("\nTesting CSV processing...")
        
        # Test CSV processing
        result = processor.process_csv_file(temp_file_path, customer_id)
        
        print(f"Marketing SMS count: {result['marketing_count']}")
        print(f"Financial SMS count: {result['financial_count']}")
        print(f"Marketing CSV saved to: {result['marketing_csv_path']}")
        
        print("\nTesting chunking...")
        
        # Test chunking
        if result['financial_sms']:
            chunks = processor.chunk_sms_list(result['financial_sms'])
            print(f"Created {len(chunks)} chunks of size {processor.chunk_size}")
            
            for i, chunk in enumerate(chunks):
                print(f"Chunk {i+1}: {len(chunk)} SMS messages")
        
        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

if __name__ == "__main__":
    asyncio.run(test_sms_processor()) 