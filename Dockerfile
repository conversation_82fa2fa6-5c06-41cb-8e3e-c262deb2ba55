# Use the official Python 3.12.2 slim image based on Debian Bookworm
FROM python:3.12.2-slim-bookworm

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    # Prevents Python from writing pyc files to disc
    PYTHONDONTWRITEBYTECODE=1

# Install system dependencies required for building Python packages
# libpq-dev is needed for psycopg2
# Added WeasyPrint dependencies: libglib2.0-0, libcairo2, etc.
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    git \
    libglib2.0-0 \
    libcairo2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    shared-mime-info \
    fonts-liberation \
    && \
    # Clean up APT cache to reduce image size
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /app

# Copy the requirements file first to leverage Docker cache
COPY requirements_pankaj.txt /app/

# Install Python dependencies from the requirements file
# Upgrade pip first
# Use --no-cache-dir to reduce image size
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements_pankaj.txt

# Copy .env file
COPY .env /app/

# Copy the rest of the application code
COPY . /app/

# Expose the application port
EXPOSE 8000

# Command to run the application using uvicorn with 2 workers
# Use --host 0.0.0.0 to make it accessible from outside the container
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "3"]