#!/usr/bin/env python3
"""
Test script for enhanced SMS parser with loan and credit card repayment detection
"""

import sys
import os
import traceback

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from app.customers.services.sms_parser_pipeline import SMSParserPipeline
    print("✅ Successfully imported SMSParserPipeline")
except Exception as e:
    print(f"❌ Failed to import SMSParserPipeline: {e}")
    traceback.print_exc()
    sys.exit(1)

def test_enhanced_parser():
    """Test the enhanced SMS parser with user's example messages"""
    
    parser = SMSParserPipeline()
    
    # Test messages from user request
    test_messages = [
        {
            'id': 1,
            'senderAddress': 'RBL-BANK',
            'phoneNumber': '+************',
            'text': 'Cash payment of Rs.2670 received on 30-04-2024 for Loan/Card (XX1772) Ref: ********************, will reflect in your account in 2 working days-RBL Bank',
            'updateAt': '2024-04-30T10:30:00Z'
        },
        {
            'id': 2,
            'senderAddress': 'RBL-BANK',
            'phoneNumber': '+************',
            'text': 'Your RBL Bank Loan A/C ****1772 has an overdue amt of Rs. 2661 for the past 24 days. We request you to clear the total overdue amount at the earliest. Please note delayed payments are reported to credit bureaus. For assistance, call 022 ********. -RBL bank',
            'updateAt': '2024-04-30T10:30:00Z'
        },
        {
            'id': 3,
            'senderAddress': 'SBI-CARD',
            'phoneNumber': '+************',
            'text': 'E-statement of SBI Credit Card ending XX65 dated 16/05/2024 has been mailed. If not received, SMS ENRS to 5676791. Total Amt Due Rs 13293; Min Amt Due Rs 6994; Payable by 05/06/2024. Click https://sbicard.com/quickpaynet to pay your bill',
            'updateAt': '2024-05-16T10:30:00Z'
        },
        {
            'id': 4,
            'senderAddress': 'SBI-CARD',
            'phoneNumber': '+************',
            'text': 'We have received payment of Rs.13,293.00 via BBPS & the same has been credited to your SBI Credit Card. Your available limit is Rs.68,963.54.',
            'updateAt': '2024-06-05T10:30:00Z'
        },
        {
            'id': 5,
            'senderAddress': 'RBL-BANK',
            'phoneNumber': '+************',
            'text': 'Your RBL Bank Loan A/C ****1772 has an overdue amt of Rs. 2661 for the past 9 days. We request you to clear the total overdue amount at the earliest. Please note delayed payments are reported to credit bureaus. For assistance, call 022 ********. -RBL bank',
            'updateAt': '2024-05-01T10:30:00Z'
        },
        {
            'id': 6,
            'senderAddress': 'SBI-CARD',
            'phoneNumber': '+************',
            'text': 'We have received payment of Rs.58,711.00 via BBPS & the same has been credited to your SBI Credit Card. Your available limit is Rs.124,977.03.',
            'updateAt': '2024-06-10T10:30:00Z'
        }
    ]
    
    print("Testing Enhanced SMS Parser")
    print("=" * 50)
    
    expected_results = [
        ("Payment", "Loan Repayment"),
        ("Accounts", "Account Status"),
        ("Accounts", "Account Status"),
        ("Payment", "Credit Card Repayment"),
        ("Accounts", "Account Status"),
        ("Payment", "Credit Card Repayment")
    ]
    
    for i, test_msg in enumerate(test_messages):
        print(f"\nTest {i+1}:")
        print(f"Message: {test_msg['text'][:100]}...")
        
        result = parser.parse_sms(test_msg)
        
        print(f"Event Type: {result.get('event_type')}")
        print(f"Transaction Sub-Type: {result.get('txn_sub_type')}")
        print(f"Information Type: {result.get('information_type')}")
        print(f"Account Type: {result.get('account_type')}")
        print(f"Amount: {result.get('amount')}")
        print(f"Bank Name: {result.get('bank_name')}")
        print(f"Is Loan Repayment: {result.get('is_loan_repayment')}")
        print(f"Is CC Repayment: {result.get('is_cc_repayment')}")
        print(f"Is Loan Delayed: {result.get('is_loan_delayed')}")
        print(f"Lender Name: {result.get('lender_name')}")
        print(f"Card Number: {result.get('card_number')}")
        print(f"Loan ID: {result.get('loan_id')}")
        print(f"Default Status: {result.get('default_status')}")
        print(f"Due Date: {result.get('due_date')}")
        print(f"Total Due: {result.get('total_due')}")
        print(f"Minimum Due: {result.get('minimum_due')}")
        
        expected_event, expected_sub = expected_results[i]
        actual_event = result.get('event_type')
        actual_sub = result.get('txn_sub_type')
        
        if actual_event == expected_event:
            print(f"✅ Event Type PASSED: Expected {expected_event}, Got {actual_event}")
        else:
            print(f"❌ Event Type FAILED: Expected {expected_event}, Got {actual_event}")
        
        # For Accounts messages, txn_sub_type will be None, so only check for Payment messages
        if expected_event == "Payment":
            if actual_sub == expected_sub:
                print(f"✅ Transaction Sub-Type PASSED: Expected {expected_sub}, Got {actual_sub}")
            else:
                print(f"❌ Transaction Sub-Type FAILED: Expected {expected_sub}, Got {actual_sub}")
        
        print("-" * 40)

if __name__ == "__main__":
    test_enhanced_parser()
