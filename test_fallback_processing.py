#!/usr/bin/env python3
"""
Test script for SMS Processor Fallback Processing
"""
import asyncio
import csv
import os
import tempfile
from app.customers.services.sms_processor import SMSProcessor

async def test_fallback_processing():
    """Test the SMS processor with OpenAI disabled"""
    
    # Create sample CSV data
    sample_data = [
        {
            'senderAddress': 'HDFCBANK',
            'text': 'Rs.1000 debited from A/c XX1234 on 01-01-2024. UPI/*********. Avl Bal: Rs.50000',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T10:00:00',
            'id': '1'
        },
        {
            'senderAddress': 'SBI',
            'text': 'Your SBI Credit Card XX5678 has been activated. Credit limit: Rs.100000',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T14:00:00',
            'id': '2'
        },
        {
            'senderAddress': 'PAYTM',
            'text': 'Rs.200 added to your Paytm wallet. Current balance: Rs.1500',
            'phoneNumber': '**********',
            'updateAt': '2024-01-01T15:00:00',
            'id': '3'
        }
    ]
    
    # Create temporary CSV file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as temp_file:
        writer = csv.DictWriter(temp_file, fieldnames=['senderAddress', 'text', 'phoneNumber', 'updateAt', 'id'])
        writer.writeheader()
        writer.writerows(sample_data)
        temp_file_path = temp_file.name
    
    try:
        # Initialize processor with OpenAI disabled
        processor = SMSProcessor(use_openai=False)
        customer_id = "test-customer-fallback"
        
        print("Testing SMS classification...")
        
        # Test individual SMS classification
        for sms in sample_data:
            sms_type = processor.classify_sms(sms['senderAddress'], sms['text'])
            print(f"SMS {sms['id']}: {sms_type} - {sms['text'][:50]}...")
        
        print("\nTesting CSV processing...")
        
        # Test CSV processing
        result = processor.process_csv_file(temp_file_path, customer_id)
        
        print(f"Marketing SMS count: {result['marketing_count']}")
        print(f"Financial SMS count: {result['financial_count']}")
        print(f"Marketing CSV saved to: {result['marketing_csv_path']}")
        
        print("\nTesting fallback processing...")
        
        # Test fallback processing
        if result['financial_sms']:
            processed_data = await processor.process_financial_sms_parallel(result['financial_sms'])
            
            print(f"Fallback processing results:")
            for table_name, records in processed_data.items():
                if records:
                    print(f"  {table_name}: {len(records)} records")
                    for record in records[:2]:  # Show first 2 records
                        print(f"    - {record}")
        
        print("\nFallback processing test completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

if __name__ == "__main__":
    asyncio.run(test_fallback_processing()) 