from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey, JSON, Date, Numeric, Text, UUID, DECIMAL, ARRAY, Enum, Index, VARCHAR
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from uuid import uuid4
from ..database import Base
from pydantic import BaseModel
from .probe_model import *
from .chat_model import *

# Import IPO models directly from their files
from ..ipo.models.listing_models import (
    ListingCompanies, IPOOfferings, Financials, PeerGroup,
    BookRunningLeadManagers, Registrar, LegalCounsel,
    StatutoryAuditor, IndependentChartedAccountant, ManagementPromoters,
    OfferDocumentReview, Regulations, ComplianceClauses,
    IssuerComplianceStatus, IPOExternalInsights
)

from ..ipo.models.document_models import (
    CompanyDocumentUploaded, CompanyDocumentRawData, Job, CompanyDocumentProcessedData
)

from ..customers.models.customer_models import (
    Customer,
    CustomerPartnerAssociation,
    BankAccount,
    CreditCard,
    Loan,
    UPIAccount,
    Wallet,
    BrokerageAccount,
    FixedDeposit,
    CustomerTransaction,
    CustomerEvent,
    SMSList,
    ServiceProviderList,
    ServiceAreaList,
    ServiceEntities
)

class documents_from_email(Base):
    __tablename__ = 'documents_from_email'
    __table_args__ = {'schema': 'public'}
    document_id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    email_id = Column(UUID(as_uuid=True))
    filename = Column(String)
    path = Column(String)
    size = Column(Integer)
    content_type = Column(String)
    download_url = Column(String)
    created_at = Column(DateTime, default=datetime.now)

class GraphSpecification(Base):
    __tablename__ = "graph_specifications"
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    chat_id = Column(UUID(as_uuid=True))
    title = Column(String)
    graph_type = Column(String)
    timeframe = Column(String)
    specifications = Column(JSON)
    created_at = Column(DateTime, default=datetime.now)

class ChatHistory(Base):
    __tablename__ = 'chat_history'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    chat_id = Column(UUID(as_uuid=True))
    message_id = Column(UUID(as_uuid=True))
    writer = Column(String)
    message = Column(String)
    created_at = Column(DateTime, default=datetime.now)

class SummaryContext(Base):
    __tablename__ = 'conversation_summary_context'
    __table_args__ = {'schema': 'public'}
    chat_id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    summary = Column(String)
    message_count = Column(Integer)
    message_history = Column(String)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

class Rules(Base):
    __tablename__ = 'rules'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    rule_code = Column(String)
    rule_name = Column(String)
    rule_description = Column(String)
    rule_status = Column(Boolean)
    rule_type =  Column(String)
    rule_severity = Column(String)
    fraud_type = Column(String)
    metric_equation = Column(JSON)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

class Metrics_numeric(Base):
    __tablename__ = 'metrics_numeric'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    metric_name = Column(String)
    metric_description = Column(String)
    metric_status = Column(Boolean)
    metric_value = Column(Float)
    metric_operation = Column(String)
    rule_code = Column(String)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

class Metrics_string(Base):
    __tablename__ = 'metrics_string'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    metric_name = Column(String)
    metric_description = Column(String)
    metric_status = Column(Boolean)
    metric_value = Column(String)
    metric_operation = Column(String)
    rule_code = Column(String)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

class Metrics_boolean(Base):
    __tablename__ = 'metrics_boolean'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    metric_name = Column(String)
    metric_description = Column(String)
    metric_status = Column(Boolean)
    metric_value = Column(Boolean)
    metric_operation = Column(String)
    rule_code = Column(String)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)


class LLM_metrics(Base):
    __tablename__ = 'LLM_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    metric_name = Column(String)
    metric_description = Column(String)
    severity = Column(String)
    active_status = Column(Boolean)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

class RulesList(Base):
    __tablename__ = 'rules_list'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    rule_number = Column(Integer)
    rule_name = Column(String(255))
    rule_description = Column(String(255))
    rule_status = Column(Boolean)
    rule_type = Column(String(255))
    rule_threshold = Column(Float)
    rule_severity = Column(String(255))
    last_updated = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)


class merchant_mertric_results(Base):
    __tablename__ = 'merchant_mertric_results'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_result = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)


class merchant_metric_value_numeric(Base):
    __tablename__ = 'merchant_metric_value_numeric'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

class merchant_metric_value_string(Base):
    __tablename__ = 'merchant_metric_value_string'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)

class merchant_metric_value_boolean(Base):
    __tablename__ = 'merchant_metric_value_boolean'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

class LLMredFlagsStatus(Base):
    __tablename__ = 'llm_red_flags_status'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    rule_number = Column(Integer)
    rule_name = Column(String(255))
    rule_description = Column(String(255))
    status = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

class rule_metric_transactions(Base):
    __tablename__ = 'rule_metric_transactions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    ip_density_7d = Column(Integer)
    cx_city = Column(String)
    pct_failed_txn_amt_7d = Column(String)

class internal_analysis(Base):
    __tablename__ = 'internal_analysis'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    neg_intent_score = Column(Float)
    created_at = Column(DateTime, default=datetime.now)


class rule_metric_documents(Base):
    __tablename__ = 'rule_metric_documents'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    is_forged_doc = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

class rule_metric_communications(Base):
    __tablename__ = 'rule_metric_communications'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    is_voice_mismatch = Column(Boolean)
    neg_intent_score = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

class rule_metric_network(Base):
    __tablename__ = 'rule_metric_network'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    number_connected_entites = Column(Integer)
    number_director = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)

class rule_metric_legal_and_regulatory(Base):
    __tablename__ = 'rule_metric_legal_and_regulatory'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    mer_employee_turnover_30d = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

class rule_metric_digital_footprint(Base):
    __tablename__ = 'digital_footprint'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    num_neg_review_30d = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)


class voice_verification(Base):
    __tablename__ = 'voice_verification'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    is_mismatch = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

class metric_value_merchant(Base):
    __tablename__ = 'metric_value_merchant'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    timestamp = Column(DateTime, default=datetime.now)
    merchant_id = Column(UUID(as_uuid=True))
    ip_density_7d = Column(Integer)
    cx_city = Column(String)
    pct_failed_txn_amt_7d = Column(String)
    is_forged_doc = Column(Boolean)
    is_voice_mismatch = Column(Boolean)
    neg_intent_score = Column(Float)
    number_connected_entites = Column(Integer)
    number_director = Column(Integer)
    mer_employee_turnover_30d = Column(Float)
    num_neg_review_30d = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)

class boolean_value_merchant(Base):
    __tablename__ = 'boolean_value_merchant'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    ip_density_7d = Column(Boolean)
    cx_city = Column(Boolean)
    pct_failed_txn_amt_7d = Column(Boolean)
    is_forged_doc = Column(Boolean)
    is_voice_mismatch = Column(Boolean)
    neg_intent_score = Column(Boolean)
    number_connected_entites = Column(Boolean)
    number_director = Column(Boolean)
    mer_employee_turnover_30d = Column(Boolean)
    num_neg_review_30d = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

class active_chats(Base):
    __tablename__ = 'active_chats'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    chat_id = Column(UUID(as_uuid=True))
    title = Column(String(255))
    last_chated = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)


class investigators(Base):
    __tablename__ = 'investigators'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255))
    email = Column(String(255))
    current_caseload = Column(Integer)
    expertise = Column(String(255))
    SLA_adherence_percentage = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)

class related_cases(Base):
    __tablename__ = 'related_cases'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_number = Column(String(50))
    related_case_number = Column(String(50))
    relationship_type = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class email_communication(Base):
    __tablename__ = "email_communication"
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), primary_key=True)
    message_id = Column(String)
    thread_id = Column(String)
    parent_message_id = Column(String, nullable=True)
    sender = Column(String)
    receiver = Column(String)
    subject = Column(String)
    content = Column(Text)
    timestamp = Column(DateTime)
    created_at = Column(DateTime)
    attachments = Column(ARRAY(String), nullable=True)  # Add this line

class risk_score_for_percentile(Base):
    __tablename__ = 'risk_score_for_percentile'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    risk_score = Column(Integer)
    risk_score_category = Column(Integer)
    merchant_id = Column(UUID(as_uuid=True))
    created_at = Column(DateTime, default=datetime.now)

class contacts(Base):
    __tablename__ = 'contacts'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    type = Column(String(50))
    registeredAddress = Column(Text)
    operatingAddress = Column(Text)
    contactPerson = Column(String(255))
    email = Column(String(255))
    phone = Column(String(20))
    altPhone = Column(String(20))
    mobile = Column(String(20))
    created_at = Column(DateTime, default=datetime.now)

class online_presence(Base):
    __tablename__ = 'online_presence'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    platform = Column(String(50))
    url = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)

class compliance_docs(Base):
    __tablename__ = 'compliance_docs'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    document_type = Column(String(50))
    document_number = Column(String(100))
    status = Column(String(50))
    last_updated = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

class kyc_statuses(Base):
    __tablename__ = 'kyc_statuses'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    kyc_type = Column(String(50))
    status = Column(String(50))
    verified_at = Column(DateTime)
    verification_method = Column(String(100))
    verification_details = Column(JSONB)
    created_at = Column(DateTime, default=datetime.now)

class documents(Base):
    __tablename__ = 'documents'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    document_type = Column(String(100))
    document_number = Column(String(100))
    is_forged = Column(Boolean)
    status = Column(String(50))
    upload_date = Column(Date)
    expiry_date = Column(Date)
    verification_status = Column(String(50))
    document_url = Column(String(255))
    meta_data = Column(JSONB)
    created_at = Column(DateTime, default=datetime.now)

class banking(Base):
    __tablename__ = 'banking'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    account_number = Column(String(50))
    ifsc = Column(String(20))
    bank_name = Column(String(100))
    account_type = Column(String(50))
    verification_status = Column(String(50))
    verified_at = Column(DateTime)
    settlement_cycle = Column(String(10))
    rolling_reserve_percentage = Column(DECIMAL(5,2))
    current_balance = Column(DECIMAL(15,2))
    created_at = Column(DateTime, default=datetime.now)


class revenue_trends(Base):
    __tablename__ = 'revenue_trends'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    month = Column(Date)
    revenue = Column(DECIMAL(15,2))
    peak_hours = Column(String(50))
    seasonal_peaks = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)


class entity_report_gen(Base):
    __tablename__ = 'entity_report_gen'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True))
    frontend_component_id = Column(String(50))
    component_type = Column(String(50))
    data = Column(JSONB)
    created_at = Column(DateTime, default=datetime.now)

class report_gen(Base):
    __tablename__ = 'report_gen'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True))
    investigator_email = Column(String(255))
    report_title = Column(String(255))
    last_updated = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

class transactions(Base):
    __tablename__ = 'transactions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    transaction_type = Column(String(50))  # Card Payment, UPI Payment, etc.
    merchant_type = Column(String(100))    # Electronics Store, Entertainment, etc.
    city = Column(String(100))
    payment_channel = Column(String(50))
    country_code = Column(String(2))
    amount = Column(DECIMAL(15,2))
    merchant_name = Column(String(255))
    risk_score = Column(Integer)
    risk_description = Column(Text, nullable=True)
    product_name = Column(String(255), nullable=True)
    dispute_date = Column(DateTime, nullable=True)
    complain_date = Column(DateTime, nullable=True)
    timestamp = Column(DateTime)
    status = Column(String(50))           # completed, pending, failed
    created_at = Column(DateTime, default=datetime.now)
    is_fraud_transaction = Column(Boolean)
    cx_id = Column(UUID(as_uuid=True))
    cx_ip = Column(String(50))
    cx_device_id = Column(String(50))
    cx_city = Column(String(50))
    cx_card_number = Column(String(50))
    cx_pii_linkage_score = Column(Integer)
    is_cardholder_name_match = Column(Boolean)
    is_chargeback = Column(Boolean)
    is_cx_international = Column(Boolean)
    txn_status = Column(String(50))
    is_cx_risky = Column(Boolean)
    invoice_amount = Column(DECIMAL(15,2))
    is_cancelled = Column(Boolean)
    txn_currency = Column(String(50))
    has_cx_complaint = Column(Boolean)
    ip_density = Column(Float)
    cx_city = Column(Text)
    fail_pct = Column(Float)


class risk_metrics(Base):
    __tablename__ = 'risk_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    overall_score = Column(Integer)
    indicator_type = Column(String(100))
    frequency = Column(String(50))
    severity = Column(String(50))
    status = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class related(Base):
    __tablename__ = 'related'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    related_entity_id = Column(UUID(as_uuid=True))
    related_entity_name = Column(String(255))
    relationship_type = Column(String(100))

    created_at = Column(DateTime, default=datetime.now)

class relationships(Base):
    __tablename__ = 'relationships'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    related_entity_name = Column(String(255))
    relationship_type = Column(String(100))
    registration_number = Column(String(100))
    location = Column(String(255))
    connection_count = Column(Integer)
    risk_level = Column(String(50))
    details = Column(JSONB)
    created_at = Column(DateTime, default=datetime.now)

class common_connections(Base):
    __tablename__ = 'common_connections'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    connection_type = Column(String(50))
    connection_value = Column(Text)
    shared_with = Column(JSONB)
    sender_id = Column(UUID(as_uuid=True))
    receiver_id = Column(UUID(as_uuid=True))
    created_at = Column(DateTime, default=datetime.now)

class financial_metrics(Base):
    __tablename__ = 'financial_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    monthlyVolume = Column(DECIMAL(15,2))
    averageTicketSize = Column(DECIMAL(10,2))
    successRate = Column(DECIMAL(5,2))
    refundRate = Column(DECIMAL(5,2))
    chargebackRate = Column(DECIMAL(5,2))
    disputeRate = Column(DECIMAL(5,2))
    accountNumber = Column(String(50))
    ifsc = Column(String(20))
    bankName = Column(String(100))
    accountType = Column(String(50))
    verificationStatus = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class processing_metrics(Base):
    __tablename__ = 'processing_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    monthly_volume = Column(DECIMAL(15,2))
    average_ticket_size = Column(DECIMAL(10,2))
    success_rate = Column(DECIMAL(5,2))
    refund_rate = Column(DECIMAL(5,2))
    chargeback_rate = Column(DECIMAL(5,2))
    dispute_rate = Column(DECIMAL(5,2))
    created_at = Column(DateTime, default=datetime.now)

class identity_proofs(Base):
    __tablename__ = 'identity_proofs'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    type = Column(String(50))
    verification_status = Column(String(50))
    document_type = Column(String(50))
    document_number = Column(String(100))
    upload_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)

class payment_channels(Base):
    __tablename__ = 'payment_channels'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    type = Column(String(50))
    name = Column(String(100))
    status = Column(String(50))
    added_on = Column(DateTime, default=datetime.now)
    created_at = Column(DateTime, default=datetime.now)

class payouts(Base):
    __tablename__ = 'payouts'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    amount = Column(Integer)
    status = Column(String(50))
    bank_account = Column(String(100))
    timestamp = Column(DateTime)
    utr = Column(String(100))
    created_at = Column(DateTime, default=datetime.now)

class communications(Base):
    __tablename__ = 'communications'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sender_id = Column(UUID(as_uuid=True))
    receiver_id = Column(UUID(as_uuid=True))
    type = Column(String(50))
    subject = Column(String(255))
    content = Column(Text)
    timestamp = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

class timeline_events(Base):
    __tablename__ = 'timeline_events'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    time = Column(DateTime)
    event = Column(String(255))
    type = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class investigations(Base):
    __tablename__ = 'investigations'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    investigation_id = Column(String(50))
    created_by = Column(String(50))
    case_number = Column(String(50))
    title = Column(String(255))
    description = Column(Text)
    status = Column(String(50))
    priority = Column(String(50))
    assignee_Name = Column(String(100))
    assignee_Email = Column(String(100))
    merchant_id = Column(UUID(as_uuid=True))
    merchant_name = Column(String(255))
    last_updated = Column(String(50))
    sla_deadline = Column(String(50))
    initiating_email_id = Column(String(50), default="")
    created_at = Column(DateTime, default=datetime.now)

class caseEventMetaData(Base):
    __tablename__ = 'caseEventMetaData'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_event_id = Column(String(50))
    channel = Column(String(50))
    oldStatus = Column(String(50))
    newStatus = Column(String(50))
    documentType = Column(String(50))
    communicationType = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class caseEvents(Base):
    __tablename__ = 'caseEvents'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_event_id = Column(String(50))
    investigation_id = Column(String(50))
    timestamp = Column(String(50))
    type = Column(String(255))
    description = Column(Text)
    user = Column(String(255))
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class investigation_notes(Base):
    __tablename__ = 'investigation_notes'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    investigation_id = Column(String(50))
    title = Column(String(255))
    description = Column(Text)
    timestamp = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class flags(Base):
    __tablename__ = 'flags'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    flag_type = Column(String(50))  # transactions, compliance, network
    severity = Column(String(50))
    importance = Column(Float)
    text = Column(Text)
    timestamp = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

class key_metrics(Base):
    __tablename__ = 'key_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    total_amount = Column(Float)
    total_count = Column(Integer)
    business_category = Column(String(50))
    business_type = Column(String(50))
    total_num_investigations = Column(Integer)
    merchant_legalName = Column(String(255))
    chargeback_percentage = Column(Float)
    current_balance_in_ledger = Column(Float)
    date_of_onboarding = Column(Date)
    average_daily_transactions = Column(Float)
    average_payout_size = Column(Float)
    account_status = Column(String(50))
    integration_types = Column(String(50))
    no_of_unique_customers = Column(Integer)
    active_investigation_cases = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)

class network_overview(Base):
    __tablename__ = 'network_overview'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    total_connections = Column(Integer)
    high_risk_connections = Column(Integer)
    network_risk_score = Column(Integer)
    directors_count = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)

class merchant_temporal_metrics(Base):
    __tablename__ = 'merchant_temporal_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    data_type = Column(String(50))
    data_value = Column(String(50))
    data_timestamp = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

class digital_information(Base):
    __tablename__ = 'digital_information'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    domain = Column(String(255))
    server_ip_address = Column(String(255))
    server_ip_country = Column(String(255))
    server_ip_owner = Column(String(255))
    domain_age = Column(String(50))
    registrar = Column(String(50))
    owner_email = Column(String(50))
    admin_contact = Column(String(50))
    privacy_protection = Column(Boolean)
    business_model = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class web_keywords(Base):
    __tablename__ = 'web_keywords'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    keyword = Column(String(255))
    frequency = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)

class web_contacts(Base):
    __tablename__ = 'web_contacts'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    contact_type = Column(String(50))
    contact_value = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)

class reviews_merchant(Base):
    __tablename__ = 'reviews_merchant'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    sentiment_status = Column(Text)
    reviews_summary = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class individual_reviews(Base):
    __tablename__ = 'individual_reviews'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    source_website = Column(String(255))
    review_title = Column(String(255))
    review_summary = Column(Text)
    review_content = Column(Text)
    url = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)

class products_and_services(Base):
    __tablename__ = 'products_and_services'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    products_and_services_description_summary = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class products_and_services_details(Base):
    __tablename__ = 'products_and_services_details'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    product_and_service = Column(String(255))
    summary = Column(Text)
    price = Column(String(255))
    industry_and_location = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)


class news_fraud_scam_illegal_risk(Base):
    __tablename__ = 'news_fraud_scam_illegal_risk'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    news_fraud_scam_illegal_risk_summary = Column(Text)
    news_sentiment = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class news_incidents(Base):
    __tablename__ = 'news_incidents'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    incident_title = Column(String(255))
    summary = Column(Text)
    content = Column(Text)
    time_of_upload = Column(String(255))
    link = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)

class domain_info(Base):
    __tablename__ = 'domain_info'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    domain = Column(String(255))
    domain_age = Column(String(50))
    registrar = Column(String(50))
    owner_email = Column(String(50))
    admin_contact = Column(String(50))
    privacy_protection = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

class ip_info(Base):
    __tablename__ = 'ip_info'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    country = Column(String(50))
    ip = Column(String(50))
    owner = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class response_time_series(Base):
    __tablename__ = 'response_time_series'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    timestamp = Column(String(50))
    status_code = Column(String(50))
    response_time_ms = Column(String(50))
    is_active = Column(Boolean)
    error = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class website_metrics(Base):
    __tablename__ = 'website_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    domain = Column(String(255))
    age = Column(String(50))
    monthly_traffic = Column(String(50))
    traffic_trend_value = Column(String(50))
    traffic_trend_positive = Column(Boolean)
    trust_score = Column(String(20))
    security = Column(JSONB)
    last_updated = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class review_sources(Base):
    __tablename__ = 'review_sources'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    platform = Column(String(100))
    rating = Column(Float)
    total_reviews = Column(Integer)
    sentiment = Column(JSONB)
    created_at = Column(DateTime, default=datetime.now)

class social_presence(Base):
    __tablename__ = 'social_presence'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    platform = Column(String(100))
    icon_type = Column(String(50))
    metrics = Column(Integer)
    followers = Column(Integer)
    posts_monthly = Column(Integer)
    engagement_rate = Column(Float)
    employee_count = Column(Integer)
    verified = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

class recent_mentions(Base):
    __tablename__ = 'recent_mentions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    source = Column(String(255))
    title = Column(String(255))
    date = Column(Date)
    sentiment = Column(String(50))
    snippet = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class risk_assessments(Base):
    __tablename__ = 'risk_assessments'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    percentile = Column(String(50))
    percentile_business_category = Column(String(50))
    risk_level = Column(String(50))
    description = Column(Text)
    risk_score = Column(Integer)
    risk_score_025_rank = Column(Integer)
    risk_score_050_rank = Column(Integer)
    risk_score_075_rank = Column(Integer)
    risk_score_business_category_025_rank = Column(Integer)
    risk_score_business_category_050_rank = Column(Integer)
    risk_score_business_category_075_rank = Column(Integer)
    risk_score_max = Column(Integer)
    risk_score_max_business_category = Column(Integer)
    merchant_id = Column(UUID(as_uuid=True))
    created_at = Column(DateTime, default=datetime.now)

class risk_categories(Base):
    __tablename__ = 'risk_categories'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    category = Column(String(50))
    score = Column(Integer)
    description = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class risk_indicators(Base):
    __tablename__ = 'risk_indicators'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    category = Column(String(50))
    indicator_label = Column(String(50))
    indicator_value = Column(String(50))
    severity = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class documents_uploaded(Base):
    __tablename__ = 'documents_uploaded'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    document_type = Column(String(50))
    document_number = Column(String(100))
    is_forged = Column(Boolean)
    status = Column(String(50))
    date_of_upload = Column(Date)
    created_at = Column(DateTime, default=datetime.now)

class devices_used(Base):
    __tablename__ = 'devices_used'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    device_id = Column(String(100))
    ip_address = Column(String(100))
    date_of_addition = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class Merchant(Base):
    __tablename__ = 'merchants'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    legal_name = Column(String(255))
    trade_name = Column(String(255))
    business_type = Column(String(100))
    summery = Column(Text)
    email = Column(String(255))
    num_directors = Column(Integer)
    domain = Column(String(255))
    incorporation_date = Column(Date)
    industry = Column(String(100))
    description = Column(Text)
    mca_description = Column(Text)
    business_category = Column(String(100))
    business_subcategory = Column(String(100))
    business_model = Column(String(50))
    onboarding_date = Column(Date)
    onboarding_platform = Column(String(100))
    kyc_verification_status = Column(String(50))
    kyc_verification_date = Column(Date)
    ip_geolocation = Column(String(100))
    # these are the columns that are not present in the table
    first_txn_date = Column(Date)
    last_txn_date = Column(Date)
    # ------------------------------
    fraud_flag = Column(Boolean)
    avg_txn_size = Column(Integer)
    # ------------------------------
    total_txn = Column(Integer)
    total_txn_fy = Column(Integer)
    gst_risk_flag = Column(Boolean)
    mca_fillings_risk_flag = Column(Boolean)
    directors_risk_flag = Column(Boolean)
    num_employees = Column(Integer)
    epfo_reg_status = Column(Boolean)
    is_sanctioned = Column(Boolean)
    is_online_business = Column(Boolean)
    online_presence_flag = Column(Boolean)
    tax_irregularity_flag = Column(Boolean)
    is_pan_compatible = Column(Boolean)
    is_address_compatible = Column(Boolean)
    prior_fraud_investigation_flag = Column(Boolean)
    is_MCA_submission_taken = Column(Boolean)
    is_regular_tax = Column(Boolean)
    multiple_merchant_pan = Column(Boolean)
    udyam_cert_missing = Column(Boolean)
    udyam_cert_flag = Column(Boolean)
    # these are the columns that are present in the table
    status = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    # ------------------------------
    avg_cx_pii_score = Column(Integer)
    txn_amt_avg = Column(Integer)
    cancelled_txn_cnt_pct = Column(Integer)
    card_num_density = Column(Integer)
    curr_diversity_score = Column(Integer)
    customer_density = Column(Integer)
    cx_complaint_txn_pct = Column(Integer)
    day_cos = Column(Integer)
    day_sin = Column(Integer)
    device_id_density = Column(Integer)
    failed_txn_cnt_pct = Column(Integer)
    hour_cos = Column(Integer)
    hour_sin = Column(Integer)
    hrs_since_last_transaction = Column(Integer)
    interntational_txn_cnt_pct = Column(Integer)
    invoice_and_txn_amt_diff_pct = Column(Integer)
    ip_density = Column(Integer)
    late_night_txn_amt_avg = Column(Integer)
    late_night_txn_cnt = Column(Integer)
    month_cos = Column(Integer)
    month_sin = Column(Integer)
    num_distinct_currency_used = Column(Integer)
    chargeback_txn_cnt_pct = Column(Integer)
    name_mismatch_txn_cnt_pct = Column(Integer)
    risky_cx_txn_cnt_pct = Column(Integer)
    round_txn_cnt_pct = Column(Integer)
    txn_cnt = Column(Integer)
    txn_amt_sum = Column(Integer)
    velocity_transaction = Column(Integer)
    # ------------------------------
    avg_cx_pii_score_is_high = Column(Boolean)
    txn_amt_avg_is_high = Column(Boolean)
    cancelled_txn_cnt_pct_is_high = Column(Boolean)
    card_num_density_is_high = Column(Boolean)
    curr_diversity_score_is_high = Column(Boolean)
    customer_density_is_high = Column(Boolean)
    cx_complaint_txn_pct_is_high = Column(Boolean)
    day_cos_is_high = Column(Boolean)
    day_sin_is_high = Column(Boolean)
    device_id_density_is_high = Column(Boolean)
    failed_txn_cnt_pct_is_high = Column(Boolean)
    hour_cos_is_high = Column(Boolean)
    hour_sin_is_high = Column(Boolean)
    hrs_since_last_transaction_is_high = Column(Boolean)
    interntational_txn_cnt_pct_is_high = Column(Boolean)
    invoice_and_txn_amt_diff_pct_is_high = Column(Boolean)
    ip_density_is_high = Column(Boolean)
    late_night_txn_amt_avg_is_high = Column(Boolean)
    late_night_txn_cnt_is_high = Column(Boolean)
    month_cos_is_high = Column(Boolean)
    month_sin_is_high = Column(Boolean)
    num_distinct_currency_used_is_high = Column(Boolean)
    chargeback_txn_cnt_pct_is_high = Column(Boolean)
    name_mismatch_txn_cnt_pct_is_high = Column(Boolean)
    risky_cx_txn_cnt_pct_is_high = Column(Boolean)
    round_txn_cnt_pct_is_high = Column(Boolean)
    txn_cnt_is_high = Column(Boolean)
    txn_amt_sum_is_high = Column(Boolean)
    velocity_transaction_is_high = Column(Boolean)
    rule_1 = Column(Boolean)
    rule_2 = Column(Boolean)


class User(Base):
    __tablename__ = 'users'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)

class Otp(Base):
    __tablename__ = 'otps'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, index=True)
    otp = Column(String)
    expires_at = Column(DateTime)


class monitoring(Base):
    __tablename__ = 'monitoring'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    segment_number = Column(Integer)
    segment_highest_risk = Column(Integer)
    segment_number_of_merchants = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)


class monitoring_category(Base):
    __tablename__ = 'monitoring_category'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    category = Column(String(50))
    segment_number = Column(Integer)
    segment_highest_risk = Column(Integer)
    segment_number_of_merchants = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)

class Metric(Base):
    __tablename__ = 'metric'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(255), nullable=True)

class AllDFPDataTable(Base):
    __tablename__ = 'raw_dfp_data'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, default=datetime.now)
    data = Column(JSONB, nullable=False)  # Store the entire Pydantic model as JSON

class DigitalFootPrint(Base):
    __tablename__ = 'digital_footprint_final'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, default=datetime.now)
    data = Column(JSONB, nullable=False)  # Store the entire Pydantic model as JSON

class MetricStore(Base):
    __tablename__ = 'metric_store'
    __table_args__ = (
        Index('idx_metric_store_metric_code', 'metric_code', unique=True),
        {'schema': 'public'}
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(String(1000), nullable=True)
    status = Column(String(50), default='pending', nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    created_by = Column(String(100), nullable=True)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    updated_by = Column(String(100), nullable=True)
    approved_by = Column(String(100), nullable=True)
    approved_date = Column(DateTime, nullable=True)
    type = Column(String(50), nullable=True)
    priority = Column(Integer, default=0)
    query = Column(String(1000), nullable=True)
    variables = Column(ARRAY(String), nullable=True)
    prompt = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    schedule = Column(String(100), nullable=True)
    metric_code = Column(String(100), nullable=True, unique=True, index=True)
    metric_table = Column(String(100), nullable=True)
    source_fetching_query = Column(String(1000), nullable=True)
    metric_value_type = Column(String(50), nullable=False)
    query_analysis = Column(JSONB, nullable=True)
    frequency = Column(String(50), nullable=True)
    tags = Column(ARRAY(String), nullable=True)

class merchant_metrics(Base):
    __tablename__ = 'merchant_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(JSONB)
    year = Column(Integer)
    financials_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class transaction_metrics(Base):
    __tablename__ = 'transaction_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(JSONB)
    year = Column(Integer)
    financials_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class customer_metrics(Base):
    __tablename__ = 'customer_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(JSONB)
    year = Column(Integer)
    financials_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class rules_store(Base):
    __tablename__ = 'rules_store'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(100), nullable=False, unique=True)
    name = Column(String(255), nullable=False)
    description = Column(String(1000), nullable=True)
    status = Column(Boolean, default=True)
    type = Column(String(50), nullable=False)
    severity = Column(String(50), nullable=False)
    fraud_type = Column(String(50), nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(100), nullable=True)
    created_by = Column(String(100), nullable=True)
    rule = Column(JSONB, nullable=False)  # Made non-nullable since rules are required
    version = Column(Integer, default=1)  # Added version tracking for rule updates
    is_active = Column(Boolean, default=True)  # For enabling/disabling rules
    is_deleted = Column(Boolean, default=False)  # For tracking deletion status
    deleted_at = Column(DateTime, nullable=True)  # Added to track when the rule was soft deleted


class customer(Base):
    __tablename__ = 'customer'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255))
    email = Column(String(255))
    phone = Column(String(50))
    address = Column(Text)
    city = Column(String(100))
    state = Column(String(100))
    country = Column(String(100))
    postal_code = Column(String(20))
    customer_type = Column(String(50))  # individual, business
    registration_date = Column(DateTime)
    last_transaction_date = Column(DateTime)
    total_transactions = Column(Integer, default=0)
    total_spent = Column(Float, default=0.0)
    risk_score = Column(Float, default=0.0)
    status = Column(String(50), default='active')  # active, inactive, blocked
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class merchant_red_flags(Base):
    __tablename__ = 'merchant_red_flags'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    rule_code = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    severity = Column(String(50), nullable=True)
    metric_values = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    metric_data_timestamp = Column(DateTime, nullable=True)
    notes = Column(Text, nullable=True)
    category = Column(VARCHAR(50))  # Added category for better classification

class customer_red_flags(Base):
    __tablename__ = 'customer_red_flags'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(UUID(as_uuid=True))
    rule_code = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    severity = Column(String(50), nullable=True)
    metric_values = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    metric_data_timestamp = Column(DateTime, nullable=True)
    notes = Column(Text, nullable=True)
    category = Column(VARCHAR(50))  # Added category for better classification

class ExternalInsights(Base):
    __tablename__ = 'external_insights'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True), index=True)
    insight_type = Column(String(50), index=True)
    insight_value = Column(JSONB, nullable=True)
    product = Column(String(100), nullable=False)  # e.g., 'merchant', 'customer', 'transaction'
    created_at = Column(DateTime, default=datetime.now)

class CompanyInsights(Base):
    __tablename__ = "company_insights"
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    insights = Column(JSONB, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class IndustryInsights(Base):
    __tablename__ = "industry_insights"
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    industry_name = Column(String, nullable=False)
    insights = Column(JSONB, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class TransactionData(Base):
    __tablename__ = 'transaction_data_table'
    __table_args__ = (
        Index('idx_transaction_data_txn_id', 'txn_id'),
        Index('idx_transaction_data_mer_id', 'mer_id'),
        {'schema': 'public'}
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    txn_id = Column(String(50), unique=True, nullable=False, index=True)
    mer_id = Column(String(50), nullable=False, index=True)
    txn_amt = Column(DECIMAL(15,2), nullable=False)
    fraud_label = Column(Boolean, nullable=False)
    txn_datetime = Column(DateTime, nullable=False)
    transaction_datetime = Column(DateTime, nullable=False)
    status = Column(String(50), nullable=False)
    channel = Column(String(50), nullable=False)
    city = Column(String(100), nullable=False)
    chargeback_amt_30d = Column(DECIMAL(15,2), nullable=True)
    txn_amt_30d = Column(DECIMAL(15,2), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class BacktestingAnalysis(Base):
    __tablename__ = 'backtesting_analysis'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    analysis_id = Column(String(100), unique=True, nullable=False, index=True)
    analysis_type = Column(String(20), nullable=False)  # 'rule' or 'metric'
    analysis_config = Column(JSONB, nullable=False)  # Store the entire analysis configuration
    prelim_sql = Column(Text, nullable=True)  # Generated preliminary SQL
    pivot_sql = Column(Text, nullable=True)  # Generated pivot table SQL
    status = Column(String(20), default='created')  # created, processing, completed, failed
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)