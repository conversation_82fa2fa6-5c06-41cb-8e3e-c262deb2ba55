from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

def print_log(level: str, message: str, *args):
    """Print formatted log messages to console"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    if args:
        message = message % args
    print(f"{timestamp} - {level} - {message}")

def process_analysis_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """Process the analysis configuration JSON"""
    try:
        print_log("INFO", "Starting analysis config processing")
        print_log("INFO", "Config: %s", config)
        
        # Validate required fields
        required_fields = ["analysisId", "analysisType", "dataTable", "configuration", "filters", "analysis"]
        missing_fields = [field for field in required_fields if not config.get(field)]
        if missing_fields:
            print_log("ERROR", "Missing required fields: %s", ", ".join(missing_fields))
            raise ValueError(f"Missing required fields in configuration: {', '.join(missing_fields)}")

        # Extract and validate each component
        analysis_id = config["analysisId"]
        analysis_type = config["analysisType"]
        print_log("INFO", "Processing analysis ID: %s, type: %s", analysis_id, analysis_type)

        # Validate nested fields
        if not isinstance(config.get("dataTable"), dict):
            raise ValueError("dataTable must be a dictionary")
        if not config.get("dataTable", {}).get("selectedTable"):
            raise ValueError("dataTable.selectedTable is required")

        if not isinstance(config.get("configuration"), dict):
            raise ValueError("configuration must be a dictionary")
        if not isinstance(config.get("configuration", {}).get("columnMappings"), dict):
            raise ValueError("configuration.columnMappings must be a dictionary")

        if not isinstance(config.get("analysis"), dict):
            raise ValueError("analysis must be a dictionary")

        # Extract and validate each component
        analysis_id = config["analysisId"]
        analysis_type = config["analysisType"]
        table_name = config["dataTable"]["selectedTable"]
        column_mappings = config["configuration"]["columnMappings"]
        filters = config["filters"]
        analysis_details = config["analysis"]

        # Validate column mappings
        if not isinstance(column_mappings, dict):
            raise ValueError("columnMappings must be a dictionary")

        # Safe access to bucketing configuration
        bucketing = {}
        if isinstance(analysis_details, dict):
            bucketing = analysis_details.get("bucketing", {})

        processed = {
            "analysis_id": analysis_id,
            "analysis_type": analysis_type,
            "table_name": table_name,
            "column_mappings": column_mappings,
            "filters": filters,
            "analysis_details": analysis_details,
            "bucketing": bucketing
        }
        
        print_log("INFO", "Successfully processed analysis config")
        return processed

    except Exception as e:
        print_log("ERROR", "Failed to process analysis config: %s", str(e))
        raise ValueError(f"Error processing analysis config: {str(e)}")


def generate_filter_conditions(filters: Dict[str, Any], column_mappings: Dict[str, Any]) -> str:
    """Generate WHERE clause conditions"""
    print_log("INFO", "Generating filter conditions")
    conditions = []

    # Handle timeframe filter
    timeframe = filters.get("timeframeFilter", {})
    if timeframe.get("startDate") and timeframe.get("endDate"):
        start_date = timeframe["startDate"]
        end_date = timeframe["endDate"]
        print_log("INFO", "Adding timeframe filter: %s to %s", start_date, end_date)
        
        datetime_mapping = column_mappings.get("datetime")
        if isinstance(datetime_mapping, dict) and datetime_mapping.get("mappedTo"):
            datetime_col = datetime_mapping["mappedTo"]
        else:
            datetime_col = "txn_datetime"
            print_log("WARNING", "Using default datetime column: %s", datetime_col)

        conditions.append(f"{datetime_col} >= '{start_date}' AND {datetime_col} <= '{end_date}'")

    # Handle population filters
    population_filters = filters.get("populationFilters", [])
    if population_filters:
        print_log("INFO", "Processing %d population filters", len(population_filters))
    
    # Default conditions
    default_conditions = [
        "status = 'completed'",
        "channel IN ('online', 'store')",
        "txn_amt > 0"
    ]
    print_log("INFO", "Adding default conditions")
    conditions.extend(default_conditions)

    final_condition = " AND ".join(conditions) if conditions else "1=1"
    print_log("DEBUG", "Generated WHERE clause: %s", final_condition)
    return final_condition


def generate_bucket_case_statements(bucketing_config: Dict[str, Any]) -> List[str]:
    print_log("INFO", "Generating bucket case statements")
    print_log("INFO", "Bucketing config: %s", bucketing_config)
    """
    Generate CASE statements for bucketing columns.

    Args:
        bucketing_config: Bucketing configuration from the JSON

    Returns:
        List of SQL CASE statements for bucketing
    """
    case_statements = []
    print_log("INFO", "Starting bucket case statement generation")

    if not bucketing_config or not isinstance(bucketing_config, dict):
        print_log("WARNING", "Empty or invalid bucketing config")
        return case_statements

    columns = bucketing_config.get("columns", [])
    print_log("INFO", "Processing %d columns for bucketing", len(columns))
    
    for col_config in columns:
        if not isinstance(col_config, dict):
            print_log("WARNING", "Invalid column config, skipping")
            continue
            
        column_name = col_config.get("column")
        if not column_name:
            print_log("WARNING", "Missing column name, skipping")
            continue
            
        print_log("INFO", "Processing column: %s", column_name)
        
        statistics = col_config.get("statistics", {})
        if not isinstance(statistics, dict):
            print_log("WARNING", "Invalid statistics for column %s", column_name)
            continue
            
        data_type = statistics.get("type")
        print_log("INFO", "Column %s has type: %s", column_name, data_type)
        
        bucketing = statistics.get("bucketing", {})
        if not isinstance(bucketing, dict):
            print_log("WARNING", "Invalid bucketing for column %s", column_name)
            continue
            
        buckets = bucketing.get("buckets", [])
        if not buckets:
            print_log("WARNING", "No buckets defined for column %s", column_name)
            continue

        print_log("INFO", "Found %d buckets for column %s", len(buckets), column_name)
        
        # Special handling for datetime bucketing
        if data_type == "datetime":
            print_log("INFO", "Processing datetime buckets for %s", column_name)
            bucket_parts = []
            for i, bucket in enumerate(buckets):
                print_log("DEBUG", "Bucket: %s", bucket)
                if not isinstance(bucket, dict):
                    print_log("WARNING", "Invalid bucket at index %d", i)
                    continue
                
                label = bucket.get("label", "")
                date_range = bucket.get("dateRange", {})
                
                if not isinstance(date_range, dict):
                    print_log("WARNING", "Invalid date range for bucket %d", i)
                    continue
                    
                start_date = date_range.get("start")
                end_date = date_range.get("end")
                
                print_log("DEBUG", "Start date: %s, End date: %s", start_date, end_date)
                
                if not start_date or not end_date:
                    print_log("WARNING", "Missing start or end date for bucket %d", i)
                    continue
                
                try:
                    # Validate dates are in correct format
                    start_date = datetime.fromisoformat(start_date)
                    end_date = datetime.fromisoformat(end_date)
                    
                    print_log("DEBUG", "Processed date bucket: %s (start: %s, end: %s)", 
                            label, start_date, end_date)
                    bucket_parts.append({
                        "start": start_date.strftime("%Y-%m-%d %H:%M:%S"),
                        "end": end_date.strftime("%Y-%m-%d %H:%M:%S"),
                        "label": label,
                        "order": i
                    })
                except ValueError as e:
                    print_log("WARNING", "Invalid date format in bucket %d: %s", i, str(e))
                    continue
            
            if bucket_parts:
                # Sort bucket parts by start date to ensure proper chronological order
                bucket_parts.sort(key=lambda x: x["start"])
                print_log("INFO", "Generated %d datetime buckets", len(bucket_parts))
                
                # Generate the CASE statement for datetime bucketing
                case_parts = [
                    "CASE"
                ]
                
                for bucket in bucket_parts:
                    case_part = (
                        f"    WHEN {column_name}::timestamp >= '{bucket['start']}'::timestamp "
                        f"AND {column_name}::timestamp < '{bucket['end']}'::timestamp "
                        f"THEN '{chr(97 + bucket['order'])}.{bucket['label']}'"
                    )
                    print_log("DEBUG", "Added datetime CASE part: %s", case_part)
                    case_parts.append(case_part)
                
                case_parts.append("    ELSE 'z.Other'")  # Add default bucket for dates outside ranges
                case_parts.append(f"END AS {column_name}_bkt")
                case_sql = "\n".join(case_parts)
                print_log("INFO", "Generated datetime CASE statement for %s", column_name)
                print_log("DEBUG", "Full datetime CASE statement:\n%s", case_sql)
                case_statements.append(case_sql)
                continue  # Skip non-datetime processing

        # Handle non-datetime types
        print_log("INFO", "Processing non-datetime buckets for %s", column_name)
        case_parts = ["CASE"]
        for i, bucket in enumerate(buckets):
            if not isinstance(bucket, dict):
                print_log("WARNING", "Invalid bucket at index %d", i)
                continue
                
            bucket_type = bucket.get("type", "auto")
            label = bucket.get("label", f"bucket_{i}")
            print_log("DEBUG", "Processing bucket: %s (type: %s)", label, bucket_type)
            
            if data_type == "numeric":
                # Handle numeric ranges
                if "range" in bucket:
                    range_info = bucket["range"]
                    if isinstance(range_info, dict):
                        min_val = range_info.get("min")
                        max_val = range_info.get("max")
                        if min_val is not None and max_val is not None:
                            case_part = f"    WHEN {column_name} >= {min_val} AND {column_name} < {max_val} THEN '{chr(97+i)}.{label}'"
                            print_log("DEBUG", "Added numeric CASE part:\n%s", case_part)
                            case_parts.append(case_part)
            elif data_type == "categorical":
                # Handle categorical values
                if "values" in bucket:
                    values = bucket["values"]
                    if isinstance(values, list) and values:
                        values_str = "', '".join(str(v) for v in values)
                        case_part = f"    WHEN {column_name} IN ('{values_str}') THEN '{chr(97+i)}.{label}'"
                        print_log("DEBUG", "Added categorical CASE part:\n%s", case_part)
                        case_parts.append(case_part)

        if data_type != "datetime":
            case_parts.append("    ELSE 'z.Other'")
        case_parts.append(f"END AS {column_name}_bkt")
        case_sql = "\n".join(case_parts)
        print_log("INFO", "Generated %s CASE statement for %s", data_type, column_name)
        print_log("DEBUG", "Full %s CASE statement:\n%s", data_type, case_sql)
        case_statements.append(case_sql)

    print_log("INFO", "Generated %d total CASE statements", len(case_statements))
    return case_statements


def generate_rule_conditions(rule_config: Dict[str, Any]) -> str:
    """Generate rule conditions"""
    print_log("INFO", "Generating rule conditions")
    
    if not rule_config:
        print_log("INFO", "No rule config provided, using default condition")
        return "1 = 1"

    equation = rule_config.get("equation", {})
    if not isinstance(equation, dict):
        print_log("WARNING", "Invalid equation format, using default condition")
        return "1 = 1"

    operator = equation.get("operator", "AND")
    conditions = equation.get("conditions", [])
    print_log("INFO", "Processing rule with operator: %s and %d conditions", operator, len(conditions))

    rule_conditions = []
    for i, condition in enumerate(conditions):
        if not isinstance(condition, dict):
            print_log("WARNING", "Invalid condition at index %d, skipping", i)
            continue
            
        metric_name = condition.get("metric_name")
        operation = condition.get("operation")
        value = condition.get("value")

        if metric_name and operation and value is not None:
            condition_str = f"{metric_name} {operation} {value}"
            print_log("DEBUG", "Added condition: %s", condition_str)
            rule_conditions.append(condition_str)

    if rule_conditions:
        final_condition = f" {operator} ".join(rule_conditions)
        print_log("INFO", "Generated rule condition: %s", final_condition)
        return final_condition

    print_log("WARNING", "No valid conditions found, using default")
    return "1 = 1"


def generate_preliminary_sql(config: Dict[str, Any]) -> str:
    """
    Generate the preliminary data SQL query.

    Args:
        config: Processed analysis configuration

    Returns:
        SQL query string for preliminary data
    """
    if not isinstance(config, dict):
        raise ValueError("Config must be a dictionary")

    # Validate required fields
    required_fields = ["table_name", "column_mappings", "filters", "analysis_details"]
    for field in required_fields:
        if field not in config:
            raise ValueError(f"Missing required field in config: {field}")
        if config[field] is None:
            raise ValueError(f"Field {field} cannot be None")

    table_name = config["table_name"]
    column_mappings = config["column_mappings"]
    filters = config["filters"]
    bucketing = config.get("bucketing", {})
    analysis_details = config["analysis_details"]

    if not isinstance(column_mappings, dict):
        raise ValueError("column_mappings must be a dictionary")

    # Get mapped column names with fallbacks
    def get_mapped_column(field: str, default: str) -> str:
        mapping = column_mappings.get(field)
        if isinstance(mapping, dict) and mapping.get("mappedTo"):
            return mapping["mappedTo"]
        return default

    txn_id = get_mapped_column("rowUniqueId", "txn_id")
    txn_amt = get_mapped_column("amount", "txn_amt")
    mer_id = get_mapped_column("accountId", "mer_id")
    fraud_label = get_mapped_column("fraudLabel", "fraud_label")
    txn_datetime = get_mapped_column("datetime", "txn_datetime")

    # Generate filter conditions
    where_conditions = generate_filter_conditions(filters, column_mappings)

    # Generate bucket case statements
    bucket_statements = generate_bucket_case_statements(bucketing)

    # Base columns - only include columns that exist in the mapping
    select_columns = []
    if txn_id:
        select_columns.append(txn_id)
    if txn_amt:
        select_columns.append(txn_amt)
    if mer_id:
        select_columns.append(mer_id)
    if fraud_label:
        select_columns.append(fraud_label)
    if txn_datetime:
        select_columns.append(txn_datetime)

    # Add additional columns if they exist in the table
    additional_columns = ["city", "chargeback_amt_30d", "txn_amt_30d"]
    select_columns.extend(additional_columns)

    # Add bucket columns
    if bucket_statements:
        select_columns.extend(bucket_statements)

    # Add calculated columns
    calculated_columns = [
        # "chargeback_amt_30d / NULLIF(txn_amt_30d, 0) AS chargeback_amt_ratio_30d",
        "TO_CHAR(txn_datetime, 'YYYY-MM') AS txn_mth"
    ]
    select_columns.extend(calculated_columns)

    # Add rule condition if it's a rule analysis
    if config.get("analysis_type") == "rule":
        rule_config = {}
        if isinstance(analysis_details, dict):
            rule_config = analysis_details.get("rule", {})
        if not isinstance(rule_config, dict):
            rule_config = {}
        rule_condition = generate_rule_conditions(rule_config)
        rule_column = f"CASE WHEN {rule_condition} THEN 1 ELSE 0 END AS rule_flag"
        select_columns.append(rule_column)

    if not select_columns:
        raise ValueError("No columns selected for the query")

    sql = f"""
WITH prelim_data AS (
    SELECT
        {',\n        '.join(select_columns)}
    FROM {table_name}
    WHERE {where_conditions}
)
SELECT * FROM prelim_data
"""

    return sql.strip()


def generate_pivot_sql(config: Dict[str, Any], analysis_type: str) -> str:
    """
    Generate the pivot table SQL query for metric or rule performance.

    Args:
        config: Processed analysis configuration
        analysis_type: 'metric' or 'rule'

    Returns:
        SQL query string for pivot table
    """
    if not isinstance(config, dict):
        raise ValueError("Config must be a dictionary")

    # Validate required fields
    required_fields = ["column_mappings"]
    for field in required_fields:
        if field not in config:
            raise ValueError(f"Missing required field in config: {field}")
        if config[field] is None:
            raise ValueError(f"Field {field} cannot be None")

    column_mappings = config["column_mappings"]
    bucketing = config.get("bucketing", {})

    if not isinstance(column_mappings, dict):
        raise ValueError("column_mappings must be a dictionary")

    # Get mapped column names with fallbacks
    def get_mapped_column(field: str, default: str) -> str:
        mapping = column_mappings.get(field)
        if isinstance(mapping, dict) and mapping.get("mappedTo"):
            return mapping["mappedTo"]
        return default

    txn_id = get_mapped_column("rowUniqueId", "txn_id")
    txn_amt = get_mapped_column("amount", "txn_amt")
    mer_id = get_mapped_column("accountId", "mer_id")
    fraud_label = get_mapped_column("fraudLabel", "fraud_label")

    # Get bucketing columns for GROUP BY
    bucket_columns = []
    if isinstance(bucketing, dict):
        columns = bucketing.get("columns", [])
        for col_config in columns:
            if not isinstance(col_config, dict):
                continue
            column_name = col_config.get("column")
            if column_name:
                bucket_columns.append(f"{column_name}_bkt")

    # Define GROUP BY columns based on analysis type
    if analysis_type == "rule":
        # Rule analysis: Group by time and rule flag only
        group_by_columns = ["txn_mth", "rule_flag"]
    else:  # metric analysis
        # Metric analysis: Group by time and buckets
        group_by_columns = ["txn_mth"]
        if bucket_columns:
            group_by_columns.extend(bucket_columns)

    # Generate aggregation columns
    agg_columns = [
        f"COUNT({txn_id}) AS txn_cnt",
        f"SUM({txn_amt}) AS txn_amt",
        f"COUNT(DISTINCT {mer_id}) AS mer_cnt",
        f"COUNT(CASE WHEN {fraud_label} = true THEN {txn_id} END) AS fraud_cnt",
        f"SUM(CASE WHEN {fraud_label} = true THEN {txn_amt} END) AS fraud_amt",
        f"COUNT(DISTINCT CASE WHEN {fraud_label} = true THEN {mer_id} END) AS fraud_mer",
        f"COUNT(CASE WHEN {fraud_label} = true THEN {txn_id} END) * 1.0 / NULLIF(COUNT({txn_id}), 0) AS fraud_cnt_rate",
        f"SUM(CASE WHEN {fraud_label} = true THEN {txn_amt} END) * 1.0 / NULLIF(SUM({txn_amt}), 0) AS fraud_amt_rate",
        f"COUNT(DISTINCT CASE WHEN {fraud_label} = true THEN {mer_id} END) * 1.0 / NULLIF(COUNT(DISTINCT {mer_id}), 0) AS fraud_mer_rate"
    ]

    # Add bucket columns to SELECT clause for metric analysis
    select_columns = group_by_columns.copy()
    if analysis_type == "metric" and bucket_columns:
        select_columns.extend(bucket_columns)

    if not group_by_columns:
        raise ValueError("No group by columns defined")

    sql = f"""
{analysis_type}_performance_pivot_table AS (
    SELECT
        {',\n        '.join(select_columns + agg_columns)}
    FROM prelim_data
    GROUP BY {', '.join(group_by_columns)}
)
SELECT * FROM {analysis_type}_performance_pivot_table
ORDER BY {group_by_columns[0] if group_by_columns else 'txn_cnt'} DESC
"""

    return sql.strip()


def generate_combined_sql(config: Dict[str, Any]) -> Dict[str, str]:
    """
    Generate all SQL queries for the analysis.

    Args:
        config: Analysis configuration JSON

    Returns:
        Dictionary containing all generated SQL queries
    """
    try:
        if not isinstance(config, dict):
            raise ValueError("Config must be a dictionary")

        processed_config = process_analysis_config(config)
        if not processed_config:
            raise ValueError("Failed to process analysis config")

        analysis_type = processed_config.get("analysis_type")
        if not analysis_type:
            raise ValueError("Analysis type is required")

        # Generate preliminary SQL
        prelim_sql = generate_preliminary_sql(processed_config)
        if not prelim_sql:
            raise ValueError("Failed to generate preliminary SQL")

        # Generate pivot SQL template
        pivot_sql = generate_pivot_sql(processed_config, analysis_type)
        if not pivot_sql:
            raise ValueError("Failed to generate pivot SQL")

        # Create properly formatted combined SQL
        try:
            # Extract the prelim_data CTE content
            prelim_parts = prelim_sql.split("WITH prelim_data AS (")
            if len(prelim_parts) != 2:
                raise ValueError("Invalid preliminary SQL format")

            prelim_content = prelim_parts[1].split(")\nSELECT * FROM prelim_data")[0].strip()

            # Combine the SQL
            combined_sql = f"""WITH prelim_data AS (
{prelim_content}
),
{pivot_sql}"""

        except Exception as e:
            raise ValueError(f"Error combining SQL queries: {str(e)}")

        return {
            "preliminary_sql": prelim_sql,
            "pivot_sql": pivot_sql,
            "combined_sql": combined_sql
        }

    except Exception as e:
        raise ValueError(f"Error generating combined SQL: {str(e)}")
