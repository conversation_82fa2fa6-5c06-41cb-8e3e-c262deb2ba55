import json
import logging
from typing import Dict, List, Any
from openai import OpenAI
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class OpenAIService:
    """Service for handling OpenAI API communication for data structuring"""
    
    def __init__(self):
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        self.client = OpenAI(api_key=api_key)
        self.model = "gpt-4-1106-preview"  # Use GPT-4 Turbo for better JSON handling
        
    async def structure_table_data(self, prompt: str, table_name: str) -> List[Dict[str, Any]]:
        """
        Send data to OpenAI for structuring according to database schema
        
        Args:
            prompt: The formatted prompt containing raw data and schema
            table_name: Name of the table being processed
            
        Returns:
            List of structured data dictionaries ready for database insertion
        """
        try:
            logger.info(f"Sending {table_name} data to OpenAI for structuring...")
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,  # Low temperature for consistent, deterministic output
                max_tokens=4000,
                response_format={"type": "json_object"}  # Ensure JSON response
            )
            
            # Extract and parse the response
            response_content = response.choices[0].message.content
            logger.info(f"Received response from OpenAI for {table_name}")
            
            # Parse the JSON response
            try:
                structured_data = json.loads(response_content)
                
                # Handle different response formats
                if isinstance(structured_data, dict):
                    if "data" in structured_data:
                        return structured_data["data"]
                    elif "records" in structured_data:
                        return structured_data["records"]
                    else:
                        # Assume the entire dict is a single record
                        return [structured_data]
                elif isinstance(structured_data, list):
                    return structured_data
                else:
                    logger.error(f"Unexpected response format for {table_name}: {type(structured_data)}")
                    return []
                    
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response for {table_name}: {str(e)}")
                logger.error(f"Raw response: {response_content}")
                return []
                
        except Exception as e:
            logger.error(f"Error calling OpenAI API for {table_name}: {str(e)}")
            return []
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for data structuring"""
        return """
        You are an expert data processing assistant specialized in transforming raw business data 
        into structured database records. Your task is to:

        1. Analyze the provided raw data and database schema
        2. Map raw data fields to the correct database schema fields
        3. Perform necessary data type conversions and validations
        4. Handle missing data appropriately
        5. Generate required IDs and timestamps
        6. Return clean, structured JSON data

        Key Guidelines:
        - Always return valid JSON in the specified format
        - Use null for missing optional fields
        - Generate UUIDs for ID fields when not provided
        - Convert dates to YYYY-MM-DD format
        - Convert numbers to appropriate numeric types
        - Handle text fields with proper cleaning and validation
        - Ensure all required fields have values (use reasonable defaults if needed)
        - Be consistent with field naming and data types
        - If multiple records are provided, return an array of objects
        - If a single record is provided, still return an array with one object

        Response Format:
        Always return a JSON object with this structure:
        {
            "data": [
                {
                    "field1": "value1",
                    "field2": "value2",
                    ...
                }
            ]
        }
        """
    
    async def validate_structured_data(self, data: List[Dict], table_name: str, schema: Dict) -> List[Dict]:
        """
        Validate structured data against the database schema
        
        Args:
            data: List of structured data records
            table_name: Name of the table
            schema: Database schema definition
            
        Returns:
            Validated and cleaned data records
        """
        validated_data = []
        
        for record in data:
            validated_record = self._validate_single_record(record, schema, table_name)
            if validated_record:
                validated_data.append(validated_record)
        
        return validated_data
    
    def _validate_single_record(self, record: Dict, schema: Dict, table_name: str) -> Dict:
        """Validate a single data record against schema"""
        validated_record = {}
        
        for field_name, field_config in schema.items():
            field_type = field_config["type"].lower()
            is_nullable = field_config["nullable"]
            
            value = record.get(field_name)
            
            # Handle missing values
            if value is None or value == "":
                if not is_nullable:
                    # Generate default for required fields
                    if "uuid" in field_type or field_name.endswith("_id"):
                        from uuid import uuid4
                        value = str(uuid4())
                    elif "date" in field_type:
                        value = datetime.now().date().isoformat()
                    elif "int" in field_type:
                        value = 0
                    elif "float" in field_type or "numeric" in field_type:
                        value = 0.0
                    elif "bool" in field_type:
                        value = False
                    elif "text" in field_type or "varchar" in field_type:
                        value = ""
                    else:
                        logger.warning(f"Could not generate default for required field {field_name} in {table_name}")
                        return None
                else:
                    value = None
            
            # Type conversion and validation
            try:
                if value is not None:
                    if "uuid" in field_type:
                        # Ensure it's a valid UUID string
                        value = str(value)
                    elif "int" in field_type:
                        value = int(float(value)) if value != "" else None
                    elif "float" in field_type or "numeric" in field_type:
                        value = float(value) if value != "" else None
                    elif "bool" in field_type:
                        value = bool(value) if isinstance(value, bool) else str(value).lower() in ['true', '1', 'yes', 'y']
                    elif "date" in field_type:
                        if isinstance(value, str):
                            # Try to parse date string
                            try:
                                from datetime import datetime as dt
                                parsed_date = dt.fromisoformat(value.replace('Z', '+00:00'))
                                value = parsed_date.date().isoformat()
                            except:
                                logger.warning(f"Could not parse date {value} for field {field_name}")
                                value = None if is_nullable else datetime.now().date().isoformat()
                    else:
                        # Text fields - ensure string type
                        value = str(value) if value is not None else None
                
                validated_record[field_name] = value
                
            except (ValueError, TypeError) as e:
                logger.warning(f"Type conversion error for field {field_name} in {table_name}: {str(e)}")
                if not is_nullable:
                    logger.error(f"Required field {field_name} has invalid data in {table_name}")
                    return None
                validated_record[field_name] = None
        
        return validated_record
