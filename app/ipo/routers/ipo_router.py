from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func
from typing import List, Optional
from datetime import datetime, date
from uuid import UUID, uuid4
import logging
import boto3
import pandas as pd
import json
import os
import tempfile
from botocore.exceptions import ClientError
from botocore.config import Config

from ...database import get_db, get_db_async
from ...models import models
from ..models.listing_models import (
    ListingCompanies, IPOOfferings, Financials, PeerGroup, 
    BookRunningLeadManagers, Registrar, LegalCounsel, 
    StatutoryAuditor, IndependentChartedAccountant, 
    ManagementPromoters, OfferDocumentReview, Regulations,
    ComplianceClauses, IssuerComplianceStatus
)
from ..schemas.ipo_schemas import (
    CompaniesListResponse, 
    CompanyListingsResponse, 
    CompanyAboutResponse, 
    CompanyIndustryResponse, 
    OfferingDetailsResponse,
    PeerGroupComparisonResponse,
    ServiceProvidersResponse,
    AllServiceProvidersResponse,
    AllServiceProvidersWithCompaniesResponse,
    EnrichedIPOOfferingResponse,
    ManagementPromotersResponse,
    OfferDocumentReviewResponse,
    FinancialMetricsResponse,
    OfferDocumentReviewResponse,
    PresignedUrlRequest,
    PresignedUrlResponse,
    ExcelProcessRequest,
    ExcelProcessResponse
)
from ...routers.apiProtection import get_current_user
from ...utils.llm import create_and_run_pipeline_without_parser
from .internal_apis import (
    get_external_data_internal,
    get_flags_from_auditor_disclosures_internal,
    get_annual_report_insights_internal
)
from ..services.listing_service import ListingService
from ..services.external_insights_service import ExternalInsightsService
from .document_processing_router import router as document_processing_router

logger = logging.getLogger(__name__)
router = APIRouter()

# Include document processing sub-router
router.include_router(document_processing_router, prefix="/documents", tags=["Document Processing"])

@router.get("/companies")
async def get_all_companies(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=500, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """
    Get all companies with pagination - returns only legal_name, id, and cin
    """
    try:
        companies = ListingService.get_all_companies(db, skip, limit)
        total = db.query(ListingCompanies).count()
        
        return {
            "companies": companies,
            "total": total,
            "skip": skip,
            "limit": limit
        }
    except Exception as e:
        logger.error(f"Error fetching companies: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/companies/{company_id}/listings", response_model=CompanyListingsResponse)
async def get_company_listings(
    company_id: str,
    db: Session = Depends(get_db)
):
    """
    Get all IPO listings for a specific company
    """
    try:
        # Check if company exists
        company = db.query(ListingCompanies).filter(ListingCompanies.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        listings = db.query(IPOOfferings).filter(IPOOfferings.company_id == company_id).all()
        
        return {
            "company_id": company_id,
            "company_name": company.legal_name,
            "listings": listings
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching listings for company {company_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/companies/{company_id}/about", response_model=CompanyAboutResponse)
async def get_company_about(
    company_id: str,
    db: Session = Depends(get_db)
):
    """
    Get about company information from listing_companies table
    """
    try:
        company = db.query(ListingCompanies).filter(ListingCompanies.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        return {
            "company_id": company_id,
            "legal_name": company.legal_name,
            "about_company": company.about_company,
            "description": company.description,
            "website": company.website,
            "email": company.email,
            "contact_email": company.contact_email,
            "contact_phone": company.contact_phone,
            "registered_address": company.registered_address,
            "business_address": company.business_address
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching company about info for {company_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/companies/{company_id}/industry", response_model=CompanyIndustryResponse)
async def get_company_industry(
    company_id: str,
    db: Session = Depends(get_db)
):
    """
    Get about industry information from listing_companies table
    """
    try:
        company = db.query(ListingCompanies).filter(ListingCompanies.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        return {
            "company_id": company_id,
            "legal_name": company.legal_name,
            "about_industry": company.about_industry,
            "classification": company.classification
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching industry info for company {company_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/listings/{listing_id}/offering-details", response_model=OfferingDetailsResponse)
async def get_offering_details(
    listing_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed offering information in the specified JSON format
    """
    try:
        # Get the listing with company information
        listing = db.query(IPOOfferings).filter(IPOOfferings.id == listing_id).first()
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found")
        
        company = db.query(ListingCompanies).filter(ListingCompanies.id == listing.company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        # Calculate business age
        business_age = ""
        if company.incorporation_date:
            today = date.today()
            years = today.year - company.incorporation_date.year
            months = today.month - company.incorporation_date.month
            if months < 0:
                years -= 1
                months += 12
            business_age = f"{years} years {months} months"
        
        # Get financials to check for audit qualifications
        audit_qualifications = 0
        financials = db.query(Financials).filter(Financials.company_id == company.id).all()
        for financial in financials:
            if financial.report_has_adverse_remarks:
                audit_qualifications += 1
        
        # Parse listing details for issue information
        issue_details = {}
        if listing.listing_details and isinstance(listing.listing_details, dict):
            listing_details_data = listing.listing_details
            
            # Extract fresh issue and offer for sale details
            fresh_issue = listing_details_data.get('fresh_issue', {})
            offer_for_sale = listing_details_data.get('offer_for_sale', {})
            if fresh_issue or offer_for_sale:
                issue_details = {
                    "freshIssue": {
                        "numberOfShares": str(fresh_issue.get('number_of_shares', 'N/A')),
                        "faceValue": str(fresh_issue.get('face_value', 'N/A')),
                        "aggregatingAmount": str(fresh_issue.get('aggregating_amount', 'N/A'))
                    } if fresh_issue else None,
                    "offerForSale": {
                        "numberOfShares": str(offer_for_sale.get('number_of_shares', 'N/A')),
                        "faceValue": str(offer_for_sale.get('face_value', 'N/A')),
                        "aggregatingAmount": str(offer_for_sale.get('aggregating_amount', 'N/A'))
                    } if offer_for_sale else None
                }
        
        # Format offer size
        offer_amount_formatted = f"₹{listing.aggregating_amount}" if listing.aggregating_amount else "₹ [●]"
        print(f"Offer Amount Formatted: {offer_amount_formatted}")
        artifact_data = listing.data
        response = {
            "offeringId": f"IPO-{company.legal_name[:3].upper()}-{listing.listing_date.year if listing.listing_date else 'XXXX'}-{str(listing_id)[-1]}",
            "legalName": company.legal_name or "N/A",
            "securityType": "Equity",  # Default as per requirement
            "documentType": listing.document_type or "N/A",
            "offeringDate": listing.listing_date.strftime("%B %d, %Y") if listing.listing_date else None,
            "eligibilityType": listing.eligibility_type or "N/A",
            "issueType": listing.listing_type or "N/A",
            "totalIssueSize": str(listing.offer_size) if listing.offer_size else "N/A",
            "totalOfferAmount": offer_amount_formatted,
            "eFilingStatus": company.efiling_status or "N/A",
            "activeCompliance": "Yes" if company.active_compliance else "No",
            "companyIncorporationDate": company.incorporation_date.strftime("%B %d, %Y") if company.incorporation_date else None,
            "businessAge": business_age,
            "designatedExchange": listing_details_data.get('designated_exchange', 'N/A') if listing.listing_details else 'N/A',
            "auditQualifications": audit_qualifications,
            "issueDetails": issue_details if issue_details else None,
            "data": artifact_data if artifact_data else None,
        }
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching offering details for listing {listing_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/companies/{company_id}/listings/{listing_id}/offering-details", response_model=OfferingDetailsResponse)
async def get_company_offering_details(
    company_id: str,
    listing_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed offering information for a specific company and listing
    """
    try:
        # Verify the listing belongs to the company
        listing = db.query(IPOOfferings).filter(
            IPOOfferings.id == listing_id,
            IPOOfferings.company_id == company_id
        ).first()
        
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found for this company")
        
        # Use the existing offering details endpoint
        return await get_offering_details(listing_id, db)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching offering details for company {company_id} and listing {listing_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/companies/{company_id}/listings/{listing_id}/peer-comparison", response_model=PeerGroupComparisonResponse)
async def get_peer_group_comparison(
    company_id: str,
    listing_id: str,
    db: Session = Depends(get_db)
):
    """
    Get peer group comparison data for a specific company and listing
    Returns two separate arrays:
    - disclosed_peers: Only companies where is_external is False (disclosed companies with detailed financial metrics)
    - external_peers: Only companies where is_external is True (external companies with basic information)
    """
    try:
        # Verify the listing belongs to the company
        listing = db.query(IPOOfferings).filter(
            IPOOfferings.id == listing_id,
            IPOOfferings.company_id == company_id
        ).first()
        
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found for this company")
        
        # Get peer group data
        peer_groups = db.query(PeerGroup).filter(
            PeerGroup.company_id == company_id,
            PeerGroup.listing_id == listing_id
        ).all()
        
        # Prepare labels maps for both peer types
        disclosed_labels_map = {
            "name": "Company Name",
            "disclosure_status": "Disclosure",
            "listing_status": "Listing",
            "revenue_from_operations_in_million_inr": "Revenue from Operations (in ₹ million)",
            "face_value_per_equity_share": "Face value per equity share",
            "closing_price_inr": "Closing Price (₹)",
            "closing_price_date": "Closing Price Date",
            "pe_ratio": "P/E Ratio",
            "eps_basic_inr": "EPS (Basic) (₹)",
            "eps_diluted_inr": "EPS (Diluted) (₹)",
            "ronw_percent": "Return on Net Worth (RoNW) (%)",
            "nav_per_equity_share_inr": "NAV (₹ per equity share)"
        }
        
        external_labels_map = {
            "name": "Name of the Company",
            "disclosure_status": "Disclosure",
            "listing_status": "Listing", 
            "website": "Website",
            "description": "Description",
            "revenue": "Revenue",
            "geography": "Geography", 
            "primary_products_services": "Primary Products/Services"
        }
        
        # Separate peers into disclosed and external arrays
        disclosed_peers = []
        external_peers = []
        
        for peer in peer_groups:
            # Format primary products/services from JSONB for external data
            products_services = "N/A"
            if peer.primary_products_services and isinstance(peer.primary_products_services, list):
                products_services = ", ".join(peer.primary_products_services)
            elif peer.primary_products_services and isinstance(peer.primary_products_services, str):
                products_services = peer.primary_products_services

            # Disclosed peers: Only include companies where is_external is False
            if not peer.is_external:
                disclosed_peer = {
                    "name": peer.name or "N/A",
                    "disclosure_status": peer.disclosure_status or "N/A",
                    "listing_status": peer.listing_status or "N/A",
                    "revenue_from_operations_in_million_inr": float(peer.revenue_from_operations_in_million_inr) if peer.revenue_from_operations_in_million_inr else None,
                    "face_value_per_equity_share": f"₹{peer.face_value_per_equity_share}" if peer.face_value_per_equity_share else "N/A",
                    "closing_price_inr": float(peer.closing_price_inr) if peer.closing_price_inr else "NA",
                    "closing_price_date": peer.closing_price_date.isoformat() if peer.closing_price_date else None,
                    "pe_ratio": float(peer.pe_ratio) if peer.pe_ratio else "NA",
                    "eps_basic_inr": float(peer.eps_basic_inr) if peer.eps_basic_inr else None,
                    "eps_diluted_inr": float(peer.eps_diluted_inr) if peer.eps_diluted_inr else None,
                    "ronw_percent": float(peer.ronw_percent) if peer.ronw_percent else None,
                    "nav_per_equity_share_inr": float(peer.nav_per_equity_share_inr) if peer.nav_per_equity_share_inr else None
                }
                disclosed_peers.append(disclosed_peer)

            # External peers: Only include companies where is_external is True
            elif peer.is_external:
                external_peer = {
                    "name": peer.name or "N/A",
                    "disclosure_status": peer.disclosure_status or "N/A",
                    "listing_status": peer.listing_status or "N/A",
                    "website": peer.website or "N/A",
                    "description": peer.description or "Description appears here",
                    "revenue": f"₹{peer.revenue_from_operations_in_million_inr} Cr" if peer.revenue_from_operations_in_million_inr else "xXX Cr",
                    "geography": peer.geography or "India, Middle East",
                    "primary_products_services": products_services
                }
                external_peers.append(external_peer)

        return {
            "disclosed_peers": disclosed_peers,
            "external_peers": external_peers,
            "disclosed_labels_map": disclosed_labels_map,
            "external_labels_map": external_labels_map
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching peer group comparison for company {company_id} and listing {listing_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/companies/{company_id}/listings/{listing_id}/service-providers", response_model=ServiceProvidersResponse)
async def get_service_providers(
    company_id: str,
    listing_id: str,
    db: Session = Depends(get_db)
):
    """
    Get all service providers (BRLM, Registrars, Legal Counsels, Auditors, etc.) for a listing
    """
    try:
        # Verify the listing belongs to the company
        listing = db.query(IPOOfferings).filter(
            IPOOfferings.id == listing_id,
            IPOOfferings.company_id == company_id
        ).first()
        
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found for this company")
        
        # Get all service providers
        brlms = db.query(BookRunningLeadManagers).filter(
            BookRunningLeadManagers.company_id == company_id,
            BookRunningLeadManagers.listing_id == listing_id
        ).all()
        
        registrars = db.query(Registrar).filter(
            Registrar.company_id == company_id,
            Registrar.listing_id == listing_id
        ).all()
        
        legal_counsels = db.query(LegalCounsel).filter(
            LegalCounsel.company_id == company_id,
            LegalCounsel.listing_id == listing_id
        ).all()
        
        statutory_auditors = db.query(StatutoryAuditor).filter(
            StatutoryAuditor.company_id == company_id,
            StatutoryAuditor.listing_id == listing_id
        ).all()
        
        independent_accountants = db.query(IndependentChartedAccountant).filter(
            IndependentChartedAccountant.company_id == company_id,
            IndependentChartedAccountant.listing_id == listing_id
        ).all()
        
        return {
            "book_running_lead_managers": brlms,
            "registrars": registrars,
            "legal_counsels": legal_counsels,
            "statutory_auditors": statutory_auditors,
            "independent_charted_accountants": independent_accountants
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching service providers for company {company_id} and listing {listing_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/companies/{company_id}/listings/{listing_id}/management-promoters", response_model=ManagementPromotersResponse)
async def get_management_promoters(
    company_id: str,
    listing_id: str,
    db: Session = Depends(get_db)
):
    """
    Get management and promoters information for a listing
    """
    try:
        # Verify the listing belongs to the company
        listing = db.query(IPOOfferings).filter(
            IPOOfferings.id == listing_id,
            IPOOfferings.company_id == company_id
        ).first()
        
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found for this company")
        
        # Get management promoters data
        promoters = db.query(ManagementPromoters).filter(
            ManagementPromoters.company_id == company_id,
            ManagementPromoters.listing_id == listing_id
        ).all()
        
        # Prepare labels map
        labels_map = {
            "name": "Name",
            "age": "Age",
            "pan": "PAN",
            "aadhar": "Aadhar",
            "bank_account": "Bank Account",
            "designation": "Designation",
            "date_of_appointment": "Date of Appointment",
            "date_of_cessation": "Date of Cessation",
            "promoter_y_n": "Promoter (Y/N)",
            "promoter_group_y_n": "Promoter Group (Y/N)",
            "director_y_n": "Director (Y/N)",
            "kmp_y_n": "KMP (Y/N)",
            "wilful_defaulter_list_y_n": "Wilful Defaulter List (Y/N)",
            "sebi_debarred_y_n": "SEBI Debarred (Y/N)",
            "disqualified_under_companies_act_y_n": "Disqualified Under Companies Act (Y/N)",
            "relationship": "Relationship",
            "basis_of_inclusion": "Basis of Inclusion",
            "additional_information": "Additional Information"
        }
        
        # Format promoters data
        promoters_data = []
        for i, promoter in enumerate(promoters, 1):
            promoter_item = {
                "name": promoter.name or "N/A",
                "age": promoter.age,
                "pan": promoter.pan,
                "aadhar": promoter.aadhar,
                "bank_account": promoter.bank_account,
                "designation": promoter.designation,
                "date_of_appointment": promoter.date_of_appointment.strftime("%B %d, %Y") if promoter.date_of_appointment else None,
                "date_of_cessation": promoter.date_of_cessation.strftime("%B %d, %Y") if promoter.date_of_cessation else None,
                "promoter_y_n": "Y" if promoter.promoter_y_n else "N",
                "promoter_group_y_n": "Y" if promoter.promoter_group_y_n else "N",
                "director_y_n": "Y" if promoter.director_y_n else "N",
                "kmp_y_n": "Y" if promoter.kmp_y_n else "N",
                "wilful_defaulter_list_y_n": "Y" if promoter.wilful_defaulter_list_y_n else "N",
                "sebi_debarred_y_n": "Y" if promoter.sebi_debarred_y_n else "N",
                "disqualified_under_companies_act_y_n": "Y" if promoter.disqualified_under_companies_act_y_n else "N",
                "relationship": promoter.relationship_type,
                "basis_of_inclusion": promoter.basis_of_inclusion,
                "additional_information": promoter.additional_information or "N/A"
            }
            promoters_data.append(promoter_item)
        
        return {
            "data": promoters_data,
            "labels_map": labels_map
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching management promoters for company {company_id} and listing {listing_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/companies/{company_id}/listings/{listing_id}/offer-document-review", response_model=OfferDocumentReviewResponse)
async def get_offer_document_review(
    company_id: str,
    listing_id: str,
    db: Session = Depends(get_db)
):
    """
    Get offer document review information for a listing
    """
    try:
        # Verify the listing belongs to the company
        listing = db.query(IPOOfferings).filter(
            IPOOfferings.id == listing_id,
            IPOOfferings.company_id == company_id
        ).first()
        
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found for this company")
        
        # Get offer document reviews
        reviews = db.query(OfferDocumentReview).filter(
            OfferDocumentReview.company_id == company_id,
            OfferDocumentReview.listing_id == listing_id
        ).all()
        
        # Prepare labels map
        labels_map = {
            "id": "ID",
            "section_of_offer_document": "Section of Offer Document",
            "page_no": "Page No.",
            "original_text_excerpt": "Original Text (Excerpt)",
            "review_content": "Review Content",
            "review_type": "Review Type"
        }
        
        # Format review data
        review_data = []
        for review in reviews:
            # Map review type to display name
            review_type_display = "N/A"
            # if review.review_type:
            #     type_mapping = {
            #         "additional_information": "Additional Information Needed",
            #         "clarity_issue": "Clarity Issues", 
            #         "issue_reason": "Issue / Reason",
            #         "disclosed_red_flags" : "Disclosed Red Flags"
            #     }
            #     review_type_display = type_mapping.get(review.review_type, review.review_type)
            
            review_item = {
                "id": str(review.id),
                "section_of_offer_document": review.section_of_offer_document or "N/A",
                "page_no": str(review.page_no) if review.page_no else "N/A",
                "original_text_excerpt": review.original_text_excerpt or "N/A",
                "review_content": review.review_content or "N/A",
                "review_type": review.review_type
            }
            review_data.append(review_item)
        
        return {
            "data": review_data,
            "labels_map": labels_map
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching offer document review for company {company_id} and listing {listing_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/companies/{company_id}/listings/{listing_id}/summary")
async def get_listing_summary(
    company_id: str,
    listing_id: str,
    db: Session = Depends(get_db)
):
    """
    Get a comprehensive summary of all listing information in one call
    """
    try:
        # Verify the listing belongs to the company
        listing = db.query(IPOOfferings).filter(
            IPOOfferings.id == listing_id,
            IPOOfferings.company_id == company_id
        ).first()
        
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found for this company")
        
        company = db.query(ListingCompanies).filter(ListingCompanies.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        # Get basic offering details
        offering_details = await get_offering_details(listing_id, db)
        
        # Get peer group count
        peer_count = db.query(PeerGroup).filter(
            PeerGroup.company_id == company_id,
            PeerGroup.listing_id == listing_id
        ).count()
        
        # Get service providers count
        brlm_count = db.query(BookRunningLeadManagers).filter(
            BookRunningLeadManagers.company_id == company_id,
            BookRunningLeadManagers.listing_id == listing_id
        ).count()
        
        registrar_count = db.query(Registrar).filter(
            Registrar.company_id == company_id,
            Registrar.listing_id == listing_id
        ).count()
        
        # Get management promoters count
        promoters_count = db.query(ManagementPromoters).filter(
            ManagementPromoters.company_id == company_id,
            ManagementPromoters.listing_id == listing_id
        ).count()
        
        # Get document review count
        review_count = db.query(OfferDocumentReview).filter(
            OfferDocumentReview.company_id == company_id,
            OfferDocumentReview.listing_id == listing_id
        ).count()
        
        # Get financials count
        financials_count = db.query(Financials).filter(
            Financials.company_id == company_id,
            Financials.listing_id == listing_id
        ).count()
        
        return {
            "company_info": {
                "id": company_id,
                "legal_name": company.legal_name,
                "cin": company.cin,
                "incorporation_date": company.incorporation_date.strftime("%B %d, %Y") if company.incorporation_date else None,
                "classification": company.classification,
                "status": company.status,
                "website": company.website,
                "email": company.email
            },
            "listing_info": {
                "id": listing_id,
                "listing_date": listing.listing_date.strftime("%B %d, %Y") if listing.listing_date else None,
                "listing_type": listing.listing_type,
                "eligibility_type": listing.eligibility_type,
                "document_type": listing.document_type,
                "offer_size": float(listing.offer_size) if listing.offer_size else None,
                "document_id": listing.document_id
            },
            "offering_details": offering_details,
            "data_availability": {
                "peer_companies": peer_count,
                "book_running_lead_managers": brlm_count,
                "registrars": registrar_count,
                "management_promoters": promoters_count,
                "document_reviews": review_count,
                "financial_records": financials_count
            },
            "api_endpoints": {
                "peer_comparison": f"/api/v1/ipo/companies/{company_id}/listings/{listing_id}/peer-comparison",
                "service_providers": f"/api/v1/ipo/companies/{company_id}/listings/{listing_id}/service-providers",
                "management_promoters": f"/api/v1/ipo/companies/{company_id}/listings/{listing_id}/management-promoters",
                "offer_document_review": f"/api/v1/ipo/companies/{company_id}/listings/{listing_id}/offer-document-review",
                "company_about": f"/api/v1/ipo/companies/{company_id}/about",
                "company_industry": f"/api/v1/ipo/companies/{company_id}/industry",
                "external_data": f"/api/v1/ipo/companies/{company_id}/getExternalData",
                "auditor_disclosures": f"/api/v1/ipo/companies/{company_id}/getFlagsFromAuditorDisclosures",
                "annual_report_insights": f"/api/v1/ipo/companies/{company_id}/getAnnualReportInsights"
            }
        }
    
    except Exception as e:
        logger.error(f"Error fetching listing summary: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


# IPO External Insights APIs
@router.get("/companies/{company_id}/getExternalData")
async def get_external_data(
    company_id: UUID = Path(..., description="The UUID of the company"),
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get external data for a given IPO company.
    """
    return await get_external_data_internal(company_id, db)


@router.get("/companies/{company_id}/getFlagsFromAuditorDisclosures")
async def get_flags_from_auditor_disclosures(
    company_id: UUID = Path(..., description="The UUID of the company"),
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get flags from auditor disclosures for a given IPO company.
    """
    return await get_flags_from_auditor_disclosures_internal(company_id, db)


@router.get("/companies/{company_id}/getAnnualReportInsights")
async def get_annual_report_insights(
    company_id: UUID = Path(..., description="The UUID of the company"),
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get annual report insights for a given IPO company.
    """
    return await get_annual_report_insights_internal(company_id, db)

@router.get("/compliance-details")
async def get_compliance_details():
    """
    Returns grouped compliance details for SEBI ICDR Regulations:
    1. Main Board IPO (Reg 6(1))
    2. Main Board IPO Alternate (Reg 6(2))
    3. SME IPO (Reg 228(1))
    4. SME IPO Alternate (Reg 228(2))
    """
    # Define metadata for each group
    compliance_groups = [
        {
            "key": "main_board_6_1",
            "title": "Main Board IPO Eligibility and Disclosure Validation (Regulation 6(1))",
            "description": "Review of DRHP for Main Board IPO under Regulation 6(1) of SEBI ICDR Regulations. Includes eligibility (6(1)), general conditions (5), and disclosure (Schedule V & VI).",
            "subcategories": [
                {
                    "key": "ipo_eligibility_6_1",
                    "title": "IPO Eligibility – Regulation 6(1)",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []  # Fill with actual data
                },
                {
                    "key": "general_conditions_5",
                    "title": "General Conditions – Regulation 5",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                },
                {
                    "key": "disclosure_v_vi",
                    "title": "Disclosure Requirements – Schedule V and VI",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                }
            ]
        },
        {
            "key": "main_board_6_2",
            "title": "Main Board (Alternate Route) IPO Eligibility and Disclosure Validation (Regulation 6(2))",
            "description": "Validation of DRHP for Main Board IPO under Regulation 6(2) (alternate route). Includes eligibility (6(2)), general conditions (5), and disclosure (Schedule V & VI).",
            "subcategories": [
                {
                    "key": "ipo_eligibility_6_2",
                    "title": "IPO Eligibility – Regulation 6(2)",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                },
                {
                    "key": "general_conditions_5",
                    "title": "General Conditions – Regulation 5",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                },
                {
                    "key": "disclosure_v_vi",
                    "title": "Disclosure Requirements – Schedule V and VI",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                }
            ]
        },
        {
            "key": "sme_228_1",
            "title": "SME IPO Eligibility and Disclosure Validation (Regulation 228(1))",
            "description": "Review of DRHP for SME IPO under Regulation 228(1). Includes SME eligibility (228(1)), general conditions (5), and disclosure (Schedule VI).",
            "subcategories": [
                {
                    "key": "sme_eligibility_228_1",
                    "title": "SME Eligibility – Regulation 228(1)",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                },
                {
                    "key": "general_conditions_5",
                    "title": "General Conditions – Regulation 5",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                },
                {
                    "key": "disclosure_vi",
                    "title": "Disclosure Requirements – Schedule VI",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                }
            ]
        },
        {
            "key": "sme_228_2",
            "title": "SME IPO Eligibility and Disclosure Validation (Regulation 228(2))",
            "description": "Review of DRHP for SME IPO under Regulation 228(2) (alternate route). Includes SME eligibility (228(2)), general conditions (5), and disclosure (Schedule VI).",
            "subcategories": [
                {
                    "key": "sme_eligibility_228_2",
                    "title": "SME Eligibility – Regulation 228(2)",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                },
                {
                    "key": "general_conditions_5",
                    "title": "General Conditions – Regulation 5",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                },
                {
                    "key": "disclosure_vi",
                    "title": "Disclosure Requirements – Schedule VI",
                    "labels_map": {
                        "requirement": "Requirement",
                        "icdr_citation": "ICDR Citation",
                        "bccl_disclosure": "BCCL Disclosure",
                        "independent_check": "Independent Check",
                        "compliant": "Compliant",
                        "page_number_of_document": "Page Number of Document"
                    },
                    "table_data": []
                }
            ]
        }
    ]

    # TODO: Populate table_data for each subcategory with actual compliance data
    # Example row: {"requirement": "Minimum Net Worth", "status": "Compliant", "remarks": "Meets threshold"}

    return {"compliance_groups": compliance_groups}

@router.get("/companies/{company_id}/listings/{listing_id}/compliance-details")
async def get_compliance_details_for_listing(
    company_id: str,
    listing_id: str,
    db: Session = Depends(get_db)
):
    """
    Returns compliance details for SEBI ICDR Regulations based on listing type.
    Returns structured data matching the frontend JSON format.
    """
    try:
        # Verify company and listing exist
        company = db.query(ListingCompanies).filter(ListingCompanies.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        listing = db.query(IPOOfferings).filter(
            IPOOfferings.id == listing_id,
            IPOOfferings.company_id == company_id
        ).first()
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found for this company")

        # Determine regulation type based on listing eligibility type
        eligibility_type = listing.eligibility_type
        listing_type = listing.listing_type
        
        # Helper function to get compliance status for a requirement ID
        def get_compliance_status(requirement_id: str):
            # Try to find compliance clause by clause_id or pattern matching
            clause = db.query(ComplianceClauses).filter(
                ComplianceClauses.clause_id == requirement_id
            ).first()
            
            if not clause:
                # Try pattern matching for legacy data
                patterns = {
                    # Regulation 5 patterns
                    "REG_5_1": "Reg. 5(1)",
                    "REG_5_2": "Reg. 5(2)",
                    "REG_5_3": "Reg. 5(3)",
                    "REG_5_4": "Reg. 5(4)",
                    "REG_5_5": "Reg. 5(5)",
                    "REG_5_": "Reg. 5(",

                    # Regulation 6(1) patterns
                    "REG_6_1_A": "Reg. 6(1)(a)",
                    "REG_6_1_B": "Reg. 6(1)(b)",
                    "REG_6_1_B_PROVISO": "Proviso to Reg. 6(1)(b)",
                    "REG_6_1_C": "Reg. 6(1)(c)",
                    "REG_6_1_D": "Reg. 6(1)(d)",
                    "REG_6_1_E": "Reg. 6(1)(e)",

                    # Regulation 6(2) patterns
                    "REG_6_2_A_1": "Reg. 6(2)(a)",
                    "REG_6_2_A_2": "Reg. 6(2)(a)",
                    "REG_6_2_A": "Reg. 6(2)(a)",
                    "REG_6_2_B": "Proviso to 6(1)(b)",
                    "REG_6_2_C": "Reg. 6(2)(c)",
                    "REG_6_2_D": "Reg. 6(2)(d)",
                    "REG_6_2_E": "Reg. 6(2)(e)",

                    # Regulation 228(1) patterns
                    "REG_228_1_A": "Reg. 228(1)(a)",
                    "REG_228_1_B": "Reg. 228(1)(b)",
                    "REG_228_1_C": "Reg. 228(1)(c)",
                    "REG_228_1_C_PROVISO": "Proviso to Reg. 228(1)(c)",
                    "REG_228_1_D": "Reg. 228(1)(d)",
                    "REG_228_1_": "Reg. 228(1)",

                    # Regulation 228(2) patterns
                    "REG_228_2_A": "Reg. 228(2)(a)",
                    "REG_228_2_B": "Reg. 228(2)(b)",
                    "REG_228_2_C": "Reg. 228(2)(c)",
                    "REG_228_2_D": "Reg. 228(2)(d)",
                    "REG_228_2_E": "Reg. 228(2)(e)",
                    "REG_228_2_": "Reg. 228(2)",

                    # Regulation 50 patterns
                    "REG_50_SCH_V": "Reg. 50 & Schedule V",
                    "REG_50_SCH_V_FULL": "Reg. 50 & Schedule V",

                    # Schedule V patterns
                    "SCH_V": "Schedule V",
                    "SCHEDULE_V": "Schedule V",
                    "SCH_V_PART_A_2": "Part A(2), Sch. V",
                    "SCH_V_PART_A_2_V": "Part A(2)(v)",
                    "SCH_V_PART_A_7": "Part A(7)",
                    "SCH_V_PART_A_8": "Part A(8)",
                    "SCH_V_PART_A_9": "Part A(9)",
                    "SCH_V_PART_A_21": "Part A(21)",
                    "SCH_V_PART_A_23": "Part A(23)",
                    "SCH_V_PART_B": "Part B",
                    "SCH_V_CAPITALISATION": "Schedule V",
                    "SCH_V_FINANCIAL_RATIOS": "Schedule V",
                    "REG_6_1_B_PROVISO": "Reg. 6(1)(b) proviso, Sch. V",

                    # Schedule VI patterns
                    "SCH_VI": "Schedule VI",
                    "SCH_VI_2": "Schedule VI",
                    "SCH_VI_3": "Schedule VI",
                    "SCHEDULE_VI": "Schedule VI",
                    "SCH_VI_PART_A_2": "Schedule VI, Part A(2)",
                    "SCH_VI_PART_A_2_V": "Schedule VI, Part A(2)(v)",
                    "SCH_VI_PART_A_4": "Schedule VI, Part A(4)",
                    "SCH_VI_PART_A_7": "Schedule VI, Part A(7)",
                    "SCH_VI_PART_A_8": "Schedule VI, Part A(8)",
                    "SCH_VI_PART_A_9": "Schedule VI, Part A(9)",
                    "SCH_VI_PART_A_21": "Schedule VI, Part A(21)",
                    "SCH_VI_PART_A_23": "Part A(23)",
                    "SCH_VI_PART_B": "Schedule VI, Part B"
                }
                
                for pattern_key, pattern_value in patterns.items():
                    if requirement_id.startswith(pattern_key):
                        clause = db.query(ComplianceClauses).filter(
                            ComplianceClauses.clause_reference.like(f'%{pattern_value}%')
                        ).first()
                        if clause:
                            break
            
            if clause:
                status_obj = db.query(IssuerComplianceStatus).filter(
                    IssuerComplianceStatus.company_id == company_id,
                    IssuerComplianceStatus.listing_id == listing_id,
                    IssuerComplianceStatus.clause_id == clause.id
                ).first()
                
                return {
                    "status": "compliant" if status_obj and status_obj.is_compliant else "non_compliant" if status_obj else "not_checked",
                    "isCompliant": status_obj.is_compliant if status_obj else False,
                    "bccl_disclosure": status_obj.issuer_disclosure if status_obj and status_obj.issuer_disclosure else "N/A",
                    "independent_check": status_obj.independent_check if status_obj and status_obj.independent_check else "N/A",
                    "page_number": "N/A"  # This field doesn't exist in the current model
                }
            
            return {
                "status": "not_checked",
                "isCompliant": False,
                "bccl_disclosure": "N/A",
                "independent_check": "N/A", 
                "page_number": "N/A"
            }

        # Define compliance data templates based on regulation type
        def get_regulation_6_1_data():
            """Main Board Regulation 6(1) compliance data"""
            requirements_eligibility = [
                {
                    "requirementID": "REG_6_1_A",
                    "requirement": "Track record of operations for at least 3 years",
                    "ICDRCitation": "Reg. 6(1)(a)"
                },
                {
                    "requirementID": "REG_6_1_B",
                    "requirement": "Net tangible assets ≥ ₹3 crore in each of 3 preceding full years",
                    "ICDRCitation": "Reg. 6(1)(b)"
                },
                {
                    "requirementID": "REG_6_1_B_PROVISO",
                    "requirement": "Monetary assets ≤ 50% of net tangible assets unless justified",
                    "ICDRCitation": "Proviso to Reg. 6(1)(b)"
                },
                {
                    "requirementID": "REG_6_1_C",
                    "requirement": "Operating profit in all 3 preceding years and average ≥ ₹15 crore",
                    "ICDRCitation": "Reg. 6(1)(c)"
                },
                {
                    "requirementID": "REG_6_1_D",
                    "requirement": "Net worth ≥ ₹1 crore in each of last 3 years",
                    "ICDRCitation": "Reg. 6(1)(d)"
                },
                {
                    "requirementID": "REG_6_1_E",
                    "requirement": "No name change in last 1 year or ≥50% revenue from new line",
                    "ICDRCitation": "Reg. 6(1)(e)"
                }
            ]
            
            requirements_general = [
                {
                    "requirementID": "REG_5_1",
                    "requirement": "Issuer not debarred by SEBI",
                    "ICDRCitation": "Reg. 5(1)"
                },
                {
                    "requirementID": "REG_5_2",
                    "requirement": "Promoters/Directors not wilful defaulters or fraudulent persons",
                    "ICDRCitation": "Reg. 5(2)"
                },
                {
                    "requirementID": "REG_5_3",
                    "requirement": "Directors not disqualified under Companies Act",
                    "ICDRCitation": "Reg. 5(3)"
                },
                {
                    "requirementID": "REG_5_4",
                    "requirement": "No winding-up petition accepted by court",
                    "ICDRCitation": "Reg. 5(4)"
                },
                {
                    "requirementID": "REG_5_5",
                    "requirement": "No regulatory action (RBI, SEBI, MCA, etc.)",
                    "ICDRCitation": "Reg. 5(5)"
                }
            ]
            
            requirements_disclosure = [
                {
                    "requirementID": "SCH_V_PART_A_2",
                    "requirement": "Full financial statements (restated consolidated for 5 years)",
                    "ICDRCitation": "Part A(2), Sch. V"
                },
                {
                    "requirementID": "SCH_V_PART_A_2_V",
                    "requirement": "Cash flow statements for 5 years",
                    "ICDRCitation": "Part A(2)(v)"
                },
                {
                    "requirementID": "SCH_V_PART_A_8",
                    "requirement": "Details of top 10 suppliers and customers",
                    "ICDRCitation": "Part A(8)"
                },
                {
                    "requirementID": "SCH_V_PART_A_7",
                    "requirement": "Objects of the offer and reasons for the offer",
                    "ICDRCitation": "Part A(7)"
                },
                {
                    "requirementID": "SCH_V_PART_B",
                    "requirement": "Full risk factors (specific to company, industry, regulatory)",
                    "ICDRCitation": "Part B"
                },
                {
                    "requirementID": "SCH_V_PART_A_21",
                    "requirement": "Material litigation involving issuer, promoters, group companies",
                    "ICDRCitation": "Part A(21)"
                },
                {
                    "requirementID": "SCH_V_PART_A_23",
                    "requirement": "Material contracts affecting financials or control",
                    "ICDRCitation": "Part A(23)"
                },
                {
                    "requirementID": "SCH_V_PART_A_9",
                    "requirement": "Management and board bios, directorships, and no disqualifications",
                    "ICDRCitation": "Part A(9)"
                },
                {
                    "requirementID": "REG_50_SCH_V_FULL",
                    "requirement": "Statement on internal controls and corporate governance (full Schedule V)",
                    "ICDRCitation": "Reg. 50 & Schedule V"
                },
                {
                    "requirementID": "SCH_V_CAPITALISATION",
                    "requirement": "Statement of tax benefits available to the company and investors",
                    "ICDRCitation": "Schedule V"
                },
                {
                    "requirementID": "SCH_V_FINANCIAL_RATIOS",
                    "requirement": "Financial ratios and comparative analysis with industry peers",
                    "ICDRCitation": "Schedule V"
                }
            ]
            
            # Add compliance status to each requirement
            for req in requirements_eligibility + requirements_general + requirements_disclosure:
                status_info = get_compliance_status(req["requirementID"])
                req.update(status_info)
            
            return {
                "regulationType": "Regulation 6(1) Main Board",
                "checks": [
                    {
                        "checkType": "Eligibility", 
                        "requirements": requirements_eligibility
                    },
                    {
                        "checkType": "General Conditions",
                        "requirements": requirements_general
                    },
                    {
                        "checkType": "Disclosures – Schedule V",
                        "requirements": requirements_disclosure
                    }
                ]
            }

        def get_regulation_6_2_data():
            """Main Board Regulation 6(2) compliance data"""
            requirements_eligibility = [
                {
                    "requirementID": "REG_6_2_A_1",
                    "requirement": "Issue made through book-building method",
                    "ICDRCitation": "Reg. 6(2)(a)"
                },
                {
                    "requirementID": "REG_6_2_A_2", 
                    "requirement": "At least 75% of net offer allocated to QIBs",
                    "ICDRCitation": "Reg. 6(2)(a)"
                },
                {
                    "requirementID": "REG_6_2_B",
                    "requirement": "Net worth of at least ₹1 crore in each of the preceding 3 full financial years",
                    "ICDRCitation": "Proviso to 6(1)(b)"
                },
                {
                    "requirementID": "REG_6_2_C",
                    "requirement": "Company has track record of 3 years OR project appraised and partly financed by bank/DFI/PFI",
                    "ICDRCitation": "Reg. 6(2)(c)"
                },
                {
                    "requirementID": "REG_6_2_D",
                    "requirement": "Minimum 10% of issue size subscribed by QIBs (not underwritten)",
                    "ICDRCitation": "Reg. 6(2)(d)"
                },
                {
                    "requirementID": "REG_6_2_E",
                    "requirement": "At least 50 allottees",
                    "ICDRCitation": "Reg. 6(2)(e)"
                }
            ]
            
            requirements_general = [
                {
                    "requirementID": "REG_5_1",
                    "requirement": "Issuer not debarred by SEBI",
                    "ICDRCitation": "Reg. 5(1)"
                },
                {
                    "requirementID": "REG_5_2",
                    "requirement": "Promoters/Directors not wilful defaulters or fraudulent persons",
                    "ICDRCitation": "Reg. 5(2)"
                },
                {
                    "requirementID": "REG_5_3",
                    "requirement": "Directors not disqualified under Companies Act",
                    "ICDRCitation": "Reg. 5(3)"
                },
                {
                    "requirementID": "REG_5_4",
                    "requirement": "No winding-up petition accepted by court",
                    "ICDRCitation": "Reg. 5(4)"
                },
                {
                    "requirementID": "REG_5_5",
                    "requirement": "No regulatory action (RBI, SEBI, MCA, etc.)",
                    "ICDRCitation": "Reg. 5(5)"
                }
            ]
            
            requirements_disclosure = [
                {
                    "requirementID": "SCH_VI_PART_A_2",
                    "requirement": "Full financial statements (restated consolidated for 3 years)",
                    "ICDRCitation": "Part A(2), Sch. VI"
                },
                {
                    "requirementID": "SCH_VI_PART_A_2_V",
                    "requirement": "Cash flow statements for 3 years",
                    "ICDRCitation": "Part A(2)(v)"
                },
                {
                    "requirementID": "SCH_VI_PART_A_8",
                    "requirement": "Details of top 5 suppliers and customers",
                    "ICDRCitation": "Part A(8)"
                },
                {
                    "requirementID": "SCH_VI_PART_A_7",
                    "requirement": "Summary of objects of the offer, if any (or OFS details)",
                    "ICDRCitation": "Part A(7)"
                },
                {
                    "requirementID": "SCH_VI_PART_B",
                    "requirement": "Full risk factors (specific to company, industry, regulatory)",
                    "ICDRCitation": "Part B"
                },
                {
                    "requirementID": "SCH_VI_PART_A_21",
                    "requirement": "Material litigation involving issuer, promoters, group companies",
                    "ICDRCitation": "Part A(21)"
                },
                {
                    "requirementID": "SCH_VI_PART_A_23",
                    "requirement": "Material contracts affecting financials or control",
                    "ICDRCitation": "Part A(23)"
                },
                {
                    "requirementID": "SCH_VI_PART_A_9",
                    "requirement": "Management and board bios, directorships, and no disqualifications",
                    "ICDRCitation": "Part A(9)"
                },
                {
                    "requirementID": "REG_50_SCH_V",
                    "requirement": "Statement on internal controls and corporate governance",
                    "ICDRCitation": "Reg. 50 & Schedule V"
                },
                {
                    "requirementID": "REG_6_1_B_PROVISO",
                    "requirement": "Use of proceeds and firm commitments if monetary assets >50%",
                    "ICDRCitation": "Reg. 6(1)(b) proviso, Sch. V"
                }
            ]
            
            # Add compliance status to each requirement
            for req in requirements_eligibility + requirements_general + requirements_disclosure:
                status_info = get_compliance_status(req["requirementID"])
                req.update(status_info)
            
            return {
                "regulationType": "Regulation 6(2) Main Board",
                "checks": [
                    {
                        "checkType": "Eligibility", 
                        "requirements": requirements_eligibility
                    },
                    {
                        "checkType": "General Conditions",
                        "requirements": requirements_general
                    },
                    {
                        "checkType": "Disclosures – Schedules V & VI",
                        "requirements": requirements_disclosure
                    }
                ]
            }

        def get_regulation_228_1_data():
            """SME Regulation 228(1) compliance data"""
            requirements_eligibility = [
                {
                    "requirementID": "REG_228_1_A",
                    "requirement": "Net tangible assets ≥ ₹1 crore (latest FY)",
                    "ICDRCitation": "Reg. 228(1)(a)"
                },
                {
                    "requirementID": "REG_228_1_B",
                    "requirement": "Net worth ≥ ₹1 crore (latest FY)",
                    "ICDRCitation": "Reg. 228(1)(b)"
                },
                {
                    "requirementID": "REG_228_1_C",
                    "requirement": "Distributable profits in at least 2 of last 3 financial years",
                    "ICDRCitation": "Reg. 228(1)(c)"
                },
                {
                    "requirementID": "REG_228_1_C_PROVISO",
                    "requirement": "OR: Net worth + tangible assets ≥ ₹3 crore (in absence of profits)",
                    "ICDRCitation": "Proviso to Reg. 228(1)(c)"
                },
                {
                    "requirementID": "REG_228_1_D",
                    "requirement": "Meets conditions of SME platform (as specified by NSE Emerge/BSE SME)",
                    "ICDRCitation": "Reg. 228(1)(d)"
                }
            ]
            
            requirements_general = [
                {
                    "requirementID": "REG_5_1",
                    "requirement": "Issuer not debarred by SEBI",
                    "ICDRCitation": "Reg. 5(1)"
                },
                {
                    "requirementID": "REG_5_2",
                    "requirement": "Promoters/Directors not wilful defaulters or fraudulent persons",
                    "ICDRCitation": "Reg. 5(2)"
                },
                {
                    "requirementID": "REG_5_3",
                    "requirement": "Directors not disqualified under Companies Act",
                    "ICDRCitation": "Reg. 5(3)"
                },
                {
                    "requirementID": "REG_5_4",
                    "requirement": "No winding-up petition accepted by court",
                    "ICDRCitation": "Reg. 5(4)"
                },
                {
                    "requirementID": "REG_5_5",
                    "requirement": "No regulatory action (RBI, SEBI, MCA, etc.)",
                    "ICDRCitation": "Reg. 5(5)"
                }
            ]
            
            requirements_disclosure = [
                {
                    "requirementID": "SCH_VI_PART_A_2",
                    "requirement": "Restated financial statements (3 years)",
                    "ICDRCitation": "Schedule VI, Part A(2)"
                },
                {
                    "requirementID": "SCH_VI_PART_B",
                    "requirement": "Risk factors",
                    "ICDRCitation": "Schedule VI, Part B"
                },
                {
                    "requirementID": "SCH_VI_PART_A_9",
                    "requirement": "Promoter/director details",
                    "ICDRCitation": "Schedule VI, Part A(9)"
                },
                {
                    "requirementID": "SCH_VI_PART_A_4",
                    "requirement": "Offer structure",
                    "ICDRCitation": "Schedule VI, Part A(4)"
                },
                {
                    "requirementID": "SCH_VI_PART_A_21",
                    "requirement": "Material litigation",
                    "ICDRCitation": "Schedule VI, Part A(21)"
                }
            ]
            
            # Add compliance status to each requirement
            for req in requirements_eligibility + requirements_general + requirements_disclosure:
                status_info = get_compliance_status(req["requirementID"])
                req.update(status_info)
            
            return {
                "regulationType": "Regulation 228(1) SME IPO",
                "checks": [
                    {
                        "checkType": "Eligibility",
                        "requirements": requirements_eligibility
                    },
                    {
                        "checkType": "General Conditions",
                        "requirements": requirements_general
                    },
                    {
                        "checkType": "Disclosures – Schedules V & VI",
                        "requirements": requirements_disclosure
                    }
                ]
            }

        def get_regulation_228_2_data():
            """SME Regulation 228(2) compliance data"""
            requirements_eligibility = [
                {
                    "requirementID": "REG_228_2_A",
                    "requirement": "IPO conducted via book-building or market making",
                    "ICDRCitation": "Reg. 228(2)(a)"
                },
                {
                    "requirementID": "REG_228_2_B",
                    "requirement": "Project appraised and funded by bank/DFI/PFI",
                    "ICDRCitation": "Reg. 228(2)(b)"
                },
                {
                    "requirementID": "REG_228_2_C",
                    "requirement": "Post-issue face value capital ≥ ₹1 crore",
                    "ICDRCitation": "Reg. 228(2)(c)"
                },
                {
                    "requirementID": "REG_228_2_D",
                    "requirement": "Minimum application size of ₹1 lakh",
                    "ICDRCitation": "Reg. 228(2)(d)"
                },
                {
                    "requirementID": "REG_228_2_E",
                    "requirement": "Issue is 100% underwritten, with 15% by merchant banker",
                    "ICDRCitation": "Reg. 228(2)(e)"
                }
            ]
            
            requirements_general = [
                {
                    "requirementID": "REG_5_1",
                    "requirement": "Issuer not debarred by SEBI",
                    "ICDRCitation": "Reg. 5(1)"
                },
                {
                    "requirementID": "REG_5_2",
                    "requirement": "Promoters/Directors not wilful defaulters or fraudulent persons",
                    "ICDRCitation": "Reg. 5(2)"
                },
                {
                    "requirementID": "REG_5_3",
                    "requirement": "Directors not disqualified under Companies Act",
                    "ICDRCitation": "Reg. 5(3)"
                },
                {
                    "requirementID": "REG_5_4",
                    "requirement": "No winding-up petition accepted by court",
                    "ICDRCitation": "Reg. 5(4)"
                },
                {
                    "requirementID": "REG_5_5",
                    "requirement": "No regulatory action (RBI, SEBI, MCA, etc.)",
                    "ICDRCitation": "Reg. 5(5)"
                }
            ]
            
            requirements_disclosure = [
                {
                    "requirementID": "SCH_VI_PART_A_2",
                    "requirement": "Restated financial statements (3 years)",
                    "ICDRCitation": "Schedule VI, Part A(2)"
                },
                {
                    "requirementID": "SCH_VI_PART_B",
                    "requirement": "Risk factors",
                    "ICDRCitation": "Schedule VI, Part B"
                },
                {
                    "requirementID": "SCH_VI_PART_A_9",
                    "requirement": "Promoter/director details",
                    "ICDRCitation": "Schedule VI, Part A(9)"
                },
                {
                    "requirementID": "SCH_VI_PART_A_4",
                    "requirement": "Offer structure",
                    "ICDRCitation": "Schedule VI, Part A(4)"
                },
                {
                    "requirementID": "SCH_VI_PART_A_21",
                    "requirement": "Material litigation",
                    "ICDRCitation": "Schedule VI, Part A(21)"
                }
            ]
            
            # Add compliance status to each requirement
            for req in requirements_eligibility + requirements_general + requirements_disclosure:
                status_info = get_compliance_status(req["requirementID"])
                req.update(status_info)
            
            return {
                "regulationType": "Regulation 228(2) SME IPO",
                "checks": [
                    {
                        "checkType": "Eligibility",
                        "requirements": requirements_eligibility
                    },
                    {
                        "checkType": "General Conditions", 
                        "requirements": requirements_general
                    },
                    {
                        "checkType": "Disclosures – Schedules V & VI",
                        "requirements": requirements_disclosure
                    }
                ]
            }

        # Determine which compliance data to return based on listing type and eligibility
        if listing_type and listing_type.lower() == "sme":
            if eligibility_type and "228(2)" in eligibility_type:
                return get_regulation_228_2_data()
            else:
                return get_regulation_228_1_data()
        else:
            # Main Board IPO - determine between 6(1) and 6(2) based on eligibility_type
            if eligibility_type and "6(1)" in eligibility_type:
                return get_regulation_6_1_data()
            elif eligibility_type and "6(2)" in eligibility_type:
                return get_regulation_6_2_data()
            else:
                # Default to 6(1) for main board if eligibility_type is not specified
                # This covers cases where eligibility_type might be None or doesn't contain specific regulation numbers
                logger.warning(f"Eligibility type '{eligibility_type}' not recognized for main board IPO. Defaulting to Regulation 6(1)")
                return get_regulation_6_1_data()
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching compliance details for company {company_id} and listing {listing_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
         

def validate_uuid(uuid_str: str, param_name: str) -> str:
    """Validate that a string is a valid UUID format"""
    try:
        UUID(uuid_str)
        return uuid_str
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid UUID format for {param_name}: {uuid_str}")

@router.get("/service-providers", response_model=AllServiceProvidersResponse)
async def get_all_service_providers(
    listing_id: Optional[str] = Query(None, description="Filter by specific listing ID"),
    company_id: Optional[str] = Query(None, description="Filter by specific company ID"),
    db: Session = Depends(get_db)
):
    """
    Get all service providers (BRLM, Registrars, Legal Counsels, Auditors, etc.)
    along with the listings they are associated with.
    
    Optional filters:
    - listing_id: Filter service providers associated with a specific listing
    - company_id: Filter service providers associated with a specific company
    """
    try:
        # Validate UUID parameters if provided
        if listing_id:
            listing_id = validate_uuid(listing_id, "listing_id")
        if company_id:
            company_id = validate_uuid(company_id, "company_id")
        # Base queries for all service providers
        brlms_query = db.query(BookRunningLeadManagers)
        registrars_query = db.query(Registrar)
        legal_counsels_query = db.query(LegalCounsel)
        statutory_auditors_query = db.query(StatutoryAuditor)

        # Apply filters if provided
        if listing_id:
            # Filter by specific listing through the many-to-many relationship
            brlms_query = brlms_query.join(BookRunningLeadManagers.listings).filter(IPOOfferings.id == listing_id)
            registrars_query = registrars_query.join(Registrar.listings).filter(IPOOfferings.id == listing_id)
            legal_counsels_query = legal_counsels_query.join(LegalCounsel.listings).filter(IPOOfferings.id == listing_id)
            statutory_auditors_query = statutory_auditors_query.join(StatutoryAuditor.listings).filter(IPOOfferings.id == listing_id)

        if company_id:
            # Filter by specific company through the listings relationship
            brlms_query = brlms_query.join(BookRunningLeadManagers.listings).filter(IPOOfferings.company_id == company_id)
            registrars_query = registrars_query.join(Registrar.listings).filter(IPOOfferings.company_id == company_id)
            legal_counsels_query = legal_counsels_query.join(LegalCounsel.listings).filter(IPOOfferings.company_id == company_id)
            statutory_auditors_query = statutory_auditors_query.join(StatutoryAuditor.listings).filter(IPOOfferings.company_id == company_id)

        # Execute queries
        brlms = brlms_query.all()
        registrars = registrars_query.all()
        legal_counsels = legal_counsels_query.all()
        statutory_auditors = statutory_auditors_query.all()

        # Format service providers with associated listings
        def format_service_providers(service_providers):
            formatted = []
            for provider in service_providers:
                # Get listings with company details
                enriched_listings = []
                provider_listings = provider.listings
                
                # Apply additional filtering on the listings if needed
                if listing_id:
                    provider_listings = [listing for listing in provider_listings if str(listing.id) == listing_id]
                if company_id:
                    provider_listings = [listing for listing in provider_listings if str(listing.company_id) == company_id]
                
                for listing in provider_listings:
                    # Fetch company information for each listing
                    company = db.query(ListingCompanies).filter(
                        ListingCompanies.id == listing.company_id
                    ).first()
                    
                    # Create enriched listing with company name
                    listing_dict = {
                        "id": listing.id,
                        "company_id": listing.company_id,
                        "company_name": company.legal_name if company else "N/A",
                        "listing_date": listing.listing_date,
                        "listing_type": listing.listing_type,
                        "eligibility_type": listing.eligibility_type,
                        "document_type": listing.document_type,
                        "offer_size": listing.offer_size,
                        "document_id": listing.document_id,
                        "listing_details": listing.listing_details,
                        "created_at": listing.created_at,
                        "updated_at": listing.updated_at
                    }
                    enriched_listings.append(listing_dict)
                
                formatted.append({
                    "provider": provider,
                    "associated_listings": enriched_listings
                })
            return formatted

        return {
            "book_running_lead_managers": format_service_providers(brlms),
            "registrars": format_service_providers(registrars),
            "legal_counsels": format_service_providers(legal_counsels),
            "statutory_auditors": format_service_providers(statutory_auditors)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching all service providers: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/get-all-entities", response_model=AllServiceProvidersWithCompaniesResponse)
async def get_all_service_providers_with_companies(
    listing_id: Optional[str] = Query(None, description="Filter by specific listing ID"),
    company_id: Optional[str] = Query(None, description="Filter by specific company ID"),
    db: Session = Depends(get_db)
):
    """
    Get all service providers along with company data including linked offerings.
    
    Optional filters:
    - listing_id: Filter service providers associated with a specific listing
    - company_id: Filter service providers associated with a specific company
    """
    try:
        # Validate UUID parameters if provided
        if listing_id:
            listing_id = validate_uuid(listing_id, "listing_id")
        if company_id:
            company_id = validate_uuid(company_id, "company_id")
        # Get service providers data using existing logic
        service_providers_response = await get_all_service_providers(listing_id, company_id, db)
        
        # Get all unique companies from the service provider listings
        unique_company_ids = set()
        
        # Extract company IDs from all service provider listings
        for provider_list in [
            service_providers_response["book_running_lead_managers"],
            service_providers_response["registrars"], 
            service_providers_response["legal_counsels"],
            service_providers_response["statutory_auditors"]
        ]:
            for provider_data in provider_list:
                for listing in provider_data["associated_listings"]:
                    unique_company_ids.add(str(listing["company_id"]))
        
        # Fetch company data with linked offerings
        companies_data = []
        
        # If no company_id filter is provided, get all companies that have listings
        # Otherwise, only get companies from the service provider listings
        if not company_id:
            # Get all companies that have IPO offerings
            all_companies = db.query(ListingCompanies).join(IPOOfferings).distinct().all()
            company_ids_to_process = [str(comp.id) for comp in all_companies]
            print(f"Processing all companies with listings: {company_ids_to_process}")
        else:
            company_ids_to_process = list(unique_company_ids)
        
        for comp_id in company_ids_to_process:
            company = db.query(ListingCompanies).filter(ListingCompanies.id == comp_id).first()
            if company:
                # Get all listings for this company
                company_listings_query = db.query(IPOOfferings).filter(IPOOfferings.company_id == comp_id)
                
                # Apply listing_id filtering if provided
                if listing_id:
                    company_listings_query = company_listings_query.filter(IPOOfferings.id == listing_id)
                
                company_listings = company_listings_query.all()
                
                # Format linked offerings
                linked_offerings = []
                for listing in company_listings:
                    offering_id = f"IPO-{company.legal_name[:3].upper()}-{listing.listing_date.year if listing.listing_date else 'XXXX'}-{str(listing.id)[-4:]}"
                    offer_size_formatted = f"₹{listing.offer_size} Crores" if listing.offer_size else "N/A"
                    
                    linked_offerings.append({
                        "offering_id": offering_id,
                        "legal_name": company.legal_name or "N/A",
                        "total_issue_size": offer_size_formatted,
                        "offering_date": listing.listing_date.isoformat() if listing.listing_date else None
                    })
                
                # Only include companies that have linked offerings after filtering
                if linked_offerings:
                    companies_data.append({
                        "company_id": str(company.id),
                        "legal_name": company.legal_name or "N/A",
                        "org_type": "Listing Company",
                        "website": company.website,
                        "phone": company.contact_phone,
                        "email": company.email or company.contact_email,
                        "linked_offerings": linked_offerings
                    })
        
        # Combine service providers data with companies data
        return {
            "book_running_lead_managers": service_providers_response["book_running_lead_managers"],
            "registrars": service_providers_response["registrars"],
            "legal_counsels": service_providers_response["legal_counsels"],
            "statutory_auditors": service_providers_response["statutory_auditors"],
            "companies": companies_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_all_service_providers_with_companies: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# Configuration constants for S3
AWS_REGION = os.environ.get("AWS_REGION", "ap-south-1")
DEFAULT_BUCKET_NAME = os.environ.get("S3_BUCKET_NAME", "documents-modus")
DEFAULT_EXPIRATION = 3600
MAX_FILE_SIZE = 104857600  # 100MB in bytes

def create_presigned_post_for_excel(bucket_name: str, object_name: str, expiration: int = DEFAULT_EXPIRATION):
    """
    Generates a presigned URL for uploading Excel files to S3.
    """
    s3_client = boto3.client('s3', region_name=AWS_REGION, config=Config(signature_version='s3v4'))
    try:
        response = s3_client.generate_presigned_post(
            Bucket=bucket_name,
            Key=object_name,
            Fields={
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            },
            Conditions=[
                {"Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
                ["content-length-range", 1, MAX_FILE_SIZE]
            ],
            ExpiresIn=expiration
        )
        return response
    except ClientError as e:
        logger.error(f"Error generating presigned URL: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate presigned URL: {str(e)}")

def download_file_from_s3(bucket_name: str, s3_key: str, local_path: str):
    """
    Download a file from S3 to local storage.
    """
    s3_client = boto3.client('s3', region_name=AWS_REGION)
    try:
        s3_client.download_file(bucket_name, s3_key, local_path)
        return True
    except ClientError as e:
        logger.error(f"Error downloading file from S3: {e}")
        return False

def extract_financials_with_llm(excel_data: dict, financials_table_structure: dict) -> List[dict]:
    """
    Use LLM to extract and map financial data from Excel to database structure.
    """
    # Create the system prompt for financial data extraction
    system_prompt = f"""
You are a financial data extraction expert. You need to extract financial data from Excel sheet data and map it to a database structure.

Here is the target database table structure for financials:
{json.dumps(financials_table_structure, indent=2)}

Your task:
1. Analyze the provided Excel data from the "Standalone Financial Data" sheet
2. Extract relevant financial information
3. Map the data to the database structure fields
4. Return a JSON array of objects that can be directly inserted into the financials table
5. Convert all monetary values to appropriate decimal format (remove commas, currency symbols)
6. Handle missing values by setting them to null
7. Ensure all date fields are in YYYY-MM-DD format
8. Each object should represent one financial record (typically one financial year)

Important mapping guidelines:
- Map revenue fields to appropriate revenue columns
- Map balance sheet items to their corresponding asset/liability fields
- Map P&L items to profit and loss fields
- Extract dates from headers or sheet information
- If multiple years are present, create separate objects for each year
- Only include fields that have corresponding data in the Excel
- Ensure numerical values are properly formatted without currency symbols or commas
- For date fields like financial_year and stated_on, use ISO date format (YYYY-MM-DD)
- Always include id field as null (it will be auto-generated)
- Include created_at and updated_at as null (they will be auto-set)

Return only valid JSON array format without any additional text or explanation.
"""

    user_input = f"""
Please extract financial data from this Excel sheet data:

{json.dumps(excel_data, indent=2)}

Extract and map this data to create financial records for database insertion.
"""

    try:
        # Use existing LLM function
        response = create_and_run_pipeline_without_parser(system_prompt, user_input)

        # Parse the response to get the JSON array
        if isinstance(response, dict) and 'content' in response:
            content = response['content']
            # Clean up the content - remove markdown code blocks if present
            if content.startswith('```json'):
                content = content.replace('```json', '').replace('```', '').strip()
            elif content.startswith('```'):
                content = content.replace('```', '').strip()

            # Try to parse as JSON
            try:
                financial_records = json.loads(content)
                if isinstance(financial_records, list):
                    return financial_records
                else:
                    return [financial_records]  # Wrap single record in list
            except json.JSONDecodeError:
                logger.error(f"Failed to parse LLM response as JSON: {content}")
                return []
        else:
            logger.error(f"Unexpected LLM response format: {response}")
            return []

    except Exception as e:
        logger.error(f"Error in LLM extraction: {e}")
        return []



@router.get("/companies/{company_id}/listings/{listing_id}/financial-metrics", response_model=FinancialMetricsResponse)
async def get_financial_metrics(
    company_id: UUID = Path(..., description="The UUID of the company"),
    listing_id: UUID = Path(..., description="The UUID of the IPO listing"),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive financial metrics for an IPO company's specific listing.

    Calculates key financial ratios including:
    - Working Capital
    - Current Ratio
    - Quick Ratio
    - Gross Profit Margin
    - Operating Profit Margin (EBIT Margin)
    - Inventory Turnover Ratio
    - Debtors Turnover Ratio
    - Creditors Turnover Ratio

    Returns financial data specific to the given listing if available,
    otherwise falls back to company-level financial data.
    """
    try:
        # Check if company exists
        company = db.query(ListingCompanies).filter(ListingCompanies.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")

        # Verify that the listing belongs to the company
        listing = db.query(IPOOfferings).filter(
            IPOOfferings.id == listing_id,
            IPOOfferings.company_id == company_id
        ).first()
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found for this company")

        # Get latest financial data (first try listing-specific, then company-level)
        latest_financial = db.query(Financials).filter(
            Financials.company_id == company_id,
            Financials.listing_id == listing_id
        ).order_by(Financials.financial_year.desc()).first()

        # If no listing-specific data, fall back to company-level data
        if not latest_financial:
            latest_financial = db.query(Financials).filter(
                Financials.company_id == company_id,
            ).order_by(Financials.financial_year.desc()).first()

        if not latest_financial:
            raise HTTPException(status_code=404, detail="Financial data not found for this company")

        # Get previous year financial data for turnover calculations
        previous_financial = db.query(Financials).filter(
            Financials.company_id == company_id,
            Financials.financial_year < latest_financial.financial_year
        ).order_by(Financials.financial_year.desc()).first()

        metrics = []

        def format_currency(value):
            """Format currency in Indian style"""
            if value is None:
                return "N/A"
            if abs(value) >= 10000000:  # 1 Crore
                return f"₹{value/10000000:.2f} Cr"
            elif abs(value) >= 100000:  # 1 Lakh
                return f"₹{value/100000:.2f} L"
            else:
                return f"₹{value:.2f}"

        def safe_division(numerator, denominator, default=None):
            """Safely perform division with null handling"""
            if numerator is None or denominator is None or denominator == 0:
                return default
            return float(numerator) / float(denominator)

        def get_color(value, good_threshold, comparison="gt"):
            """Get color based on threshold comparison"""
            if value is None:
                return "text-gray-500"
            if comparison == "gt":
                return "text-green-500" if value > good_threshold else "text-red-500"
            else:  # lt
                return "text-green-500" if value < good_threshold else "text-red-500"

        # 1. Working Capital
        if latest_financial.total_current_assets and latest_financial.total_current_liabilities:
            working_capital = float(latest_financial.total_current_assets) - float(latest_financial.total_current_liabilities)
            color = get_color(working_capital, 0)
            metrics.append({
                "label": "Working Capital",
                "value": format_currency(working_capital),
                "numerical_value": working_capital,
                "icon": f"<Wallet className=\"h-5 w-5 {color}\" />",
                "description": "Current Assets - Current Liabilities"
            })

        # 2. Current Ratio
        current_ratio = safe_division(latest_financial.total_current_assets, latest_financial.total_current_liabilities)
        if current_ratio is not None:
            color = get_color(current_ratio, 1)
            metrics.append({
                "label": "Current Ratio",
                "value": f"{current_ratio:.2f}",
                "numerical_value": current_ratio,
                "icon": f"<Scale className=\"h-5 w-5 {color}\" />",
                "description": "Current Assets / Current Liabilities"
            })

        # 3. Quick Ratio (Conservative estimate: Current Assets - Inventories)
        if latest_financial.total_current_assets and latest_financial.total_current_liabilities and latest_financial.inventories:
            quick_assets = float(latest_financial.total_current_assets) - float(latest_financial.inventories or 0)
            quick_ratio = safe_division(quick_assets, latest_financial.total_current_liabilities)
            if quick_ratio is not None:
                color = get_color(quick_ratio, 1)
                metrics.append({
                    "label": "Quick Ratio",
                    "value": f"{quick_ratio:.2f}",
                    "numerical_value": quick_ratio,
                    "icon": f"<Zap className=\"h-5 w-5 {color}\" />",
                    "description": "(Current Assets - Inventories) / Current Liabilities"
                })

        # 4. Gross Profit Margin
        if latest_financial.net_revenue and latest_financial.total_operating_cost:
            gross_profit = float(latest_financial.net_revenue) - float(latest_financial.total_operating_cost or 0)
            gross_profit_margin = safe_division(gross_profit, latest_financial.net_revenue) * 100
            if gross_profit_margin is not None:
                color = get_color(gross_profit_margin, 20)
                metrics.append({
                    "label": "Gross Profit Margin",
                    "value": f"{gross_profit_margin:.2f}%",
                    "numerical_value": gross_profit_margin,
                    "icon": f"<Percent className=\"h-5 w-5 {color}\" />",
                    "description": "(Revenue - Operating Costs) / Revenue * 100"
                })

        # 5. Operating Profit Margin (EBIT Margin)
        ebit_margin = safe_division(latest_financial.profit_before_interest_and_tax, latest_financial.net_revenue)
        if ebit_margin is not None:
            ebit_margin_percent = ebit_margin * 100
            color = get_color(ebit_margin_percent, 10)
            metrics.append({
                "label": "Operating Profit Margin (EBIT)",
                "value": f"{ebit_margin_percent:.2f}%",
                "numerical_value": ebit_margin_percent,
                "icon": f"<TrendingUp className=\"h-5 w-5 {color}\" />",
                "description": "EBIT / Revenue * 100"
            })

        # 6. Inventory Turnover Ratio
        if latest_financial.total_cost_of_materials_consumed and previous_financial:
            avg_inventory = safe_division(
                (float(latest_financial.inventories or 0) + float(previous_financial.inventories or 0)),
                2
            )
            inventory_turnover = safe_division(latest_financial.total_cost_of_materials_consumed, avg_inventory)
            if inventory_turnover is not None:
                color = get_color(inventory_turnover, 4)
                metrics.append({
                    "label": "Inventory Turnover Ratio",
                    "value": f"{inventory_turnover:.2f}x",
                    "numerical_value": inventory_turnover,
                    "icon": f"<Package className=\"h-5 w-5 {color}\" />",
                    "description": "Cost of Materials Consumed / Average Inventory"
                })

        # 7. Debtors Turnover Ratio (Trade Receivables Turnover)
        if latest_financial.net_revenue and previous_financial:
            avg_receivables = safe_division(
                (float(latest_financial.trade_receivables or 0) + float(previous_financial.trade_receivables or 0)),
                2
            )
            debtors_turnover = safe_division(latest_financial.net_revenue, avg_receivables)
            if debtors_turnover is not None:
                color = get_color(debtors_turnover, 6)
                metrics.append({
                    "label": "Debtors Turnover Ratio",
                    "value": f"{debtors_turnover:.2f}x",
                    "numerical_value": debtors_turnover,
                    "icon": f"<Users className=\"h-5 w-5 {color}\" />",
                    "description": "Revenue / Average Trade Receivables"
                })

        # 8. Creditors Turnover Ratio (Trade Payables Turnover)
        if latest_financial.total_cost_of_materials_consumed and previous_financial:
            avg_payables = safe_division(
                (float(latest_financial.trade_payables or 0) + float(previous_financial.trade_payables or 0)),
                2
            )
            creditors_turnover = safe_division(latest_financial.total_cost_of_materials_consumed, avg_payables)
            if creditors_turnover is not None:
                color = get_color(creditors_turnover, 8)
                metrics.append({
                    "label": "Creditors Turnover Ratio",
                    "value": f"{creditors_turnover:.2f}x",
                    "numerical_value": creditors_turnover,
                    "icon": f"<CreditCard className=\"h-5 w-5 {color}\" />",
                    "description": "Cost of Materials Consumed / Average Trade Payables"
                })

        # Additional metrics for comprehensive analysis

        # Net Profit Margin
        if latest_financial.profit_after_tax and latest_financial.net_revenue:
            net_profit_margin = safe_division(latest_financial.profit_after_tax, latest_financial.net_revenue) * 100
            if net_profit_margin is not None:
                color = get_color(net_profit_margin, 5)
                metrics.append({
                    "label": "Net Profit Margin",
                    "value": f"{net_profit_margin:.2f}%",
                    "numerical_value": net_profit_margin,
                    "icon": f"<Target className=\"h-5 w-5 {color}\" />",
                    "description": "Net Profit / Revenue * 100"
                })

        # Debt-to-Equity Ratio
        if latest_financial.total_debt and latest_financial.total_equity:
            debt_equity_ratio = safe_division(latest_financial.total_debt, latest_financial.total_equity)
            if debt_equity_ratio is not None:
                color = get_color(debt_equity_ratio, 1, "lt")  # Lower is better
                metrics.append({
                    "label": "Debt-to-Equity Ratio",
                    "value": f"{debt_equity_ratio:.2f}",
                    "numerical_value": debt_equity_ratio,
                    "icon": f"<BarChart className=\"h-5 w-5 {color}\" />",
                    "description": "Total Debt / Total Equity"
                })

        # Return on Assets (ROA)
        if latest_financial.profit_after_tax and latest_financial.given_assets_total:
            roa = safe_division(latest_financial.profit_after_tax, latest_financial.given_assets_total) * 100
            if roa is not None:
                color = get_color(roa, 2)
                metrics.append({
                    "label": "Return on Assets (ROA)",
                    "value": f"{roa:.2f}%",
                    "numerical_value": roa,
                    "icon": f"<PieChart className=\"h-5 w-5 {color}\" />",
                    "description": "Net Profit / Total Assets * 100"
                })

        return {
            "success": True,
            "message": "Financial metrics calculated successfully",
            "data": {
                "company_id": str(company_id),
                "listing_id": str(listing_id),
                "company_name": company.legal_name,
                "listing_type": listing.listing_type,
                "financial_year": latest_financial.financial_year.isoformat() if latest_financial.financial_year else None,
                "metrics": metrics,
                "data_source": {
                    "nature": latest_financial.nature,
                    "filing_standard": latest_financial.filing_standard,
                    "filing_type": latest_financial.filing_type
                }
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating financial metrics for company {company_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error while calculating financial metrics")


@router.get("/companies/{company_id}/listings/{listing_id}/valuation-metrics", response_model=FinancialMetricsResponse)
async def get_valuation_metrics(
    company_id: UUID = Path(..., description="The UUID of the company"),
    listing_id: UUID = Path(..., description="The UUID of the IPO listing"),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive valuation metrics for an IPO company's specific listing.

    Calculates key valuation ratios including:
    - Earnings Per Share (EPS)
    - Return on Equity (ROE)
    - Return on Capital Employed (ROCE)
    - Return on Assets (ROA)
    - Financial Leverage Ratio (Asset/Equity)
    - Interest Coverage Ratio
    - Net Worth
    - Debt to Equity Ratio

    Returns valuation data specific to the given listing if available,
    otherwise falls back to company-level financial data.
    """
    try:
        # Check if company exists
        company = db.query(ListingCompanies).filter(ListingCompanies.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")

        # Verify that the listing belongs to the company
        listing = db.query(IPOOfferings).filter(
            IPOOfferings.id == listing_id,
            IPOOfferings.company_id == company_id
        ).first()
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found for this company")

        # Get latest financial data (first try listing-specific, then company-level)
        latest_financial = db.query(Financials).filter(
            Financials.company_id == company_id,
            Financials.listing_id == listing_id,
        ).order_by(Financials.financial_year.desc()).first()

        # If no listing-specific data, fall back to company-level data
        if not latest_financial:
            latest_financial = db.query(Financials).filter(
                Financials.company_id == company_id,
            ).order_by(Financials.financial_year.desc()).first()

        if not latest_financial:
            raise HTTPException(status_code=404, detail="Financial data not found for this company")

        # Get previous year financial data for comparative analysis
        previous_financial = db.query(Financials).filter(
            Financials.company_id == company_id,
            Financials.financial_year < latest_financial.financial_year
        ).order_by(Financials.financial_year.desc()).first()

        metrics = []

        def format_currency(value):
            """Format currency in Indian style"""
            if value is None:
                return "N/A"
            if abs(value) >= 10000000:  # 1 Crore
                return f"₹{value/10000000:.2f} Cr"
            elif abs(value) >= 100000:  # 1 Lakh
                return f"₹{value/100000:.2f} L"
            else:
                return f"₹{value:.2f}"

        def safe_division(numerator, denominator, default=None):
            """Safely perform division with null handling"""
            if numerator is None or denominator is None or denominator == 0:
                return default
            return float(numerator) / float(denominator)

        def get_color(value, good_threshold, comparison="gt"):
            """Get color based on threshold comparison"""
            if value is None:
                return "text-gray-500"
            if comparison == "gt":
                return "text-green-500" if value > good_threshold else "text-red-500"
            else:  # lt
                return "text-green-500" if value < good_threshold else "text-red-500"

        # Calculate number of shares (estimate from share capital and face value if available)
        # This is a simplified calculation - in real scenarios, you'd get this from share capital structure
        estimated_shares = None
        if latest_financial.share_capital:
            # Assuming face value of ₹10 per share (common in India)
            # In real implementation, this should come from actual share data
            face_value = 10  # Default assumption
            estimated_shares = float(latest_financial.share_capital) / face_value

        # 1. Earnings Per Share (EPS)
        if latest_financial.profit_after_tax and estimated_shares:
            eps = safe_division(latest_financial.profit_after_tax, estimated_shares)
            if eps is not None:
                color = get_color(eps, 10)  # EPS > 10 is generally good
                metrics.append({
                    "label": "Earnings Per Share (EPS)",
                    "value": f"₹{eps:.2f}",
                    "numerical_value": eps,
                    "icon": f"<DollarSign className=\"h-5 w-5 {color}\" />",
                    "description": "Net Profit / Number of Outstanding Shares"
                })

        # 2. Return on Equity (ROE)
        if latest_financial.profit_after_tax and latest_financial.total_equity:
            roe = safe_division(latest_financial.profit_after_tax, latest_financial.total_equity) * 100
            if roe is not None:
                color = get_color(roe, 15)  # ROE > 15% is generally good
                metrics.append({
                    "label": "Return on Equity (ROE)",
                    "value": f"{roe:.2f}%",
                    "numerical_value": roe,
                    "icon": f"<TrendingUp className=\"h-5 w-5 {color}\" />",
                    "description": "Net Profit / Shareholders' Equity * 100"
                })

        # 3. Return on Capital Employed (ROCE)
        if latest_financial.profit_before_interest_and_tax and latest_financial.total_equity:
            # Capital Employed = Total Equity + Long-term Debt
            capital_employed = float(latest_financial.total_equity) + float(latest_financial.long_term_borrowings or 0)
            roce = safe_division(latest_financial.profit_before_interest_and_tax, capital_employed) * 100
            if roce is not None:
                color = get_color(roce, 12)  # ROCE > 12% is generally good
                metrics.append({
                    "label": "Return on Capital Employed (ROCE)",
                    "value": f"{roce:.2f}%",
                    "numerical_value": roce,
                    "icon": f"<BarChart className=\"h-5 w-5 {color}\" />",
                    "description": "EBIT / (Total Equity + Long-term Debt) * 100"
                })

        # 4. Return on Assets (ROA)
        if latest_financial.profit_after_tax and latest_financial.given_assets_total:
            roa = safe_division(latest_financial.profit_after_tax, latest_financial.given_assets_total) * 100
            if roa is not None:
                color = get_color(roa, 5)  # ROA > 5% is generally good
                metrics.append({
                    "label": "Return on Assets (ROA)",
                    "value": f"{roa:.2f}%",
                    "numerical_value": roa,
                    "icon": f"<PieChart className=\"h-5 w-5 {color}\" />",
                    "description": "Net Profit / Total Assets * 100"
                })

        # 5. Financial Leverage Ratio (Asset/Equity)
        if latest_financial.given_assets_total and latest_financial.total_equity:
            leverage_ratio = safe_division(latest_financial.given_assets_total, latest_financial.total_equity)
            if leverage_ratio is not None:
                color = get_color(leverage_ratio, 2, "lt")  # Lower leverage is generally better
                metrics.append({
                    "label": "Financial Leverage Ratio",
                    "value": f"{leverage_ratio:.2f}x",
                    "numerical_value": leverage_ratio,
                    "icon": f"<Scale className=\"h-5 w-5 {color}\" />",
                    "description": "Total Assets / Shareholders' Equity"
                })

        # 6. Interest Coverage Ratio
        if latest_financial.profit_before_interest_and_tax and latest_financial.interest:
            interest_coverage = safe_division(latest_financial.profit_before_interest_and_tax, latest_financial.interest)
            if interest_coverage is not None:
                color = get_color(interest_coverage, 2.5)  # Coverage > 2.5x is generally safe
                metrics.append({
                    "label": "Interest Coverage Ratio",
                    "value": f"{interest_coverage:.2f}x",
                    "numerical_value": interest_coverage,
                    "icon": f"<Shield className=\"h-5 w-5 {color}\" />",
                    "description": "EBIT / Interest Expense"
                })

        # 7. Net Worth
        if latest_financial.total_equity:
            net_worth = float(latest_financial.total_equity)
            color = get_color(net_worth, 0)  # Positive net worth is good
            metrics.append({
                "label": "Net Worth",
                "value": format_currency(net_worth),
                "numerical_value": net_worth,
                "icon": f"<Wallet className=\"h-5 w-5 {color}\" />",
                "description": "Total Shareholders' Equity"
            })

        # 8. Debt to Equity Ratio
        if latest_financial.total_debt and latest_financial.total_equity:
            debt_equity_ratio = safe_division(latest_financial.total_debt, latest_financial.total_equity)
            if debt_equity_ratio is not None:
                color = get_color(debt_equity_ratio, 0.5, "lt")  # Lower D/E is generally better
                metrics.append({
                    "label": "Debt to Equity Ratio",
                    "value": f"{debt_equity_ratio:.2f}",
                    "numerical_value": debt_equity_ratio,
                    "icon": f"<AlertTriangle className=\"h-5 w-5 {color}\" />",
                    "description": "Total Debt / Total Equity"
                })

        # Additional valuation metrics for comprehensive analysis

        # Book Value Per Share
        if latest_financial.total_equity and estimated_shares:
            book_value_per_share = safe_division(latest_financial.total_equity, estimated_shares)
            if book_value_per_share is not None:
                color = get_color(book_value_per_share, 100)  # BVPS > ₹100 is generally good
                metrics.append({
                    "label": "Book Value Per Share (BVPS)",
                    "value": f"₹{book_value_per_share:.2f}",
                    "numerical_value": book_value_per_share,
                    "icon": f"<BookOpen className=\"h-5 w-5 {color}\" />",
                    "description": "Total Equity / Number of Outstanding Shares"
                })

        # Asset Turnover Ratio
        if latest_financial.net_revenue and latest_financial.given_assets_total:
            asset_turnover = safe_division(latest_financial.net_revenue, latest_financial.given_assets_total)
            if asset_turnover is not None:
                color = get_color(asset_turnover, 0.5)  # Asset turnover > 0.5 is generally good
                metrics.append({
                    "label": "Asset Turnover Ratio",
                    "value": f"{asset_turnover:.2f}x",
                    "numerical_value": asset_turnover,
                    "icon": f"<RotateCcw className=\"h-5 w-5 {color}\" />",
                    "description": "Revenue / Total Assets"
                })

        # Equity Multiplier
        if latest_financial.given_assets_total and latest_financial.total_equity:
            equity_multiplier = safe_division(latest_financial.given_assets_total, latest_financial.total_equity)
            if equity_multiplier is not None:
                color = get_color(equity_multiplier, 2, "lt")  # Lower multiplier indicates less leverage
                metrics.append({
                    "label": "Equity Multiplier",
                    "value": f"{equity_multiplier:.2f}x",
                    "numerical_value": equity_multiplier,
                    "icon": f"<Layers className=\"h-5 w-5 {color}\" />",
                    "description": "Total Assets / Total Equity (Leverage Indicator)"
                })

        return {
            "success": True,
            "message": "Valuation metrics calculated successfully",
            "data": {
                "company_id": str(company_id),
                "listing_id": str(listing_id),
                "company_name": company.legal_name,
                "listing_type": listing.listing_type,
                "financial_year": latest_financial.financial_year.isoformat() if latest_financial.financial_year else None,
                "metrics": metrics,
                "data_source": {
                    "nature": latest_financial.nature,
                    "filing_standard": latest_financial.filing_standard,
                    "filing_type": latest_financial.filing_type
                }
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating valuation metrics for company {company_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error while calculating valuation metrics")


@router.get("/companies/{company_id}/valuation-metrics", response_model=FinancialMetricsResponse)
async def get_valuation_metrics_company_only(
    company_id: UUID = Path(..., description="The UUID of the company"),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive valuation metrics for an IPO company (backward-compatible endpoint).

    This endpoint is maintained for backward compatibility. It returns valuation metrics
    for the company without requiring a specific listing_id.

    Calculates key valuation ratios including:
    - Earnings Per Share (EPS)
    - Return on Equity (ROE)
    - Return on Capital Employed (ROCE)
    - Return on Assets (ROA)
    - Financial Leverage Ratio (Asset/Equity)
    - Interest Coverage Ratio
    - Net Worth
    - Debt to Equity Ratio
    """
    try:
        # Check if company exists
        company = db.query(ListingCompanies).filter(ListingCompanies.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")

        # Get the first listing for this company
        listing = db.query(IPOOfferings).filter(IPOOfferings.company_id == company_id).first()

        if listing:
            # If company has listings, use the listing-specific endpoint
            return await get_valuation_metrics(company_id, listing.id, db)
        else:
            # If no listings, return error message
            return {
                "success": False,
                "message": "No IPO listings found for this company. Please use the listing-specific endpoint.",
                "data": {
                    "company_id": str(company_id),
                    "listing_id": None,
                    "company_name": company.legal_name,
                    "listing_type": None,
                    "financial_year": None,
                    "metrics": [],
                    "data_source": {
                        "nature": None,
                        "filing_standard": None,
                        "filing_type": None
                    }
                }
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating valuation metrics for company {company_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error while calculating valuation metrics")

@router.get("/companies/{company_id}/financialsTable")
async def get_financials_table(
    company_id: UUID = Path(..., description="The UUID of the company"),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    """
    Get financial data from financials table organized by data source and financial year.
    Returns the whole table as the response, similar to the existing financialsTable endpoint.
    """
    try:
        # Check if company exists
        company = db.query(ListingCompanies).filter(ListingCompanies.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        # Get all financials data for the company
        financials_query = db.query(Financials).filter(
            Financials.company_id == company_id
        ).order_by(Financials.data_source, Financials.financial_year.desc())
        
        financials_data = financials_query.all()

        if not financials_data:
            return {
                "success": False, 
                "message": "No financial data found for this company", 
                "data": None
            }

        # Group data by data_source as hierarchy key, then by financial year
        grouped_data = {}
        for financial_record in financials_data:
            source = financial_record.data_source or "Unknown"
            # Extract only the year from the financial_year date
            if financial_record.financial_year:
                year = str(financial_record.financial_year.year)
            else:
                year = "Unknown"
            
            if source not in grouped_data:
                grouped_data[source] = {}
            
            grouped_data[source][year] = financial_record.data

        return {
            "success": True, 
            "message": "Financial data fetched successfully", 
            "data": grouped_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching financial data for company {company_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error while fetching financial data")
