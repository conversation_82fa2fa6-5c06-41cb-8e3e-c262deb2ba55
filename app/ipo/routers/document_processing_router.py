import json
import os
import requests
import ast
import asyncio
from datetime import datetime
from zoneinfo import ZoneInfo

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select 
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm.attributes import flag_modified
from uuid import uuid4
from pydantic import BaseModel

from ...models import models
from ...database import get_db_async
from ...routers.apiProtection import get_current_user
from ...routers.ares.ipo_engine.file_upload import create_presigned_post, list_s3_files
from ...routers.ares.ipo_engine.preprocessor import full_preprocess_pdf, preprocess_icdr_doc
from ...routers.ares.ipo_engine.retriever import get_bm25_retriever, get_vector_retriever
from ...routers.ares.ipo_engine.listingRetrievalAgent import DataRetrievalAgent as ListingDataRetrievalAgent
from ...routers.ares.ipo_engine.icdrRetrievalAgent import DataRetrievalAgent as ICDRDataRetrievalAgent
from ...routers.ares.ipo_engine.gptwrapper import GPTWrapper
from ...routers.ares.ipo_engine import data_ingestion_table as data_ingestion

router = APIRouter()

print("OPENAI_API_KEY:", os.environ.get("OPENAI_API_KEY"))

@router.get("/get-presigned-url-icdr")
async def get_presigned_url_icdr(
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get a presigned URL for uploading a file to S3 for ICDR.
    """
    file_name = str(uuid4())
    object_name = f"icdr/{file_name}.pdf"
    url = create_presigned_post("documents-modus", object_name)
    if not url:
        raise HTTPException(status_code=500, detail="Error generating presigned URL")
    else:
        upload_data = models.CompanyDocumentUploaded(
            id=file_name,
            company_id="",
            upload_timestamp=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
            uploaded_by=str(current_user.id),
            aws_data={"bucket": "documents-modus", "key": object_name},
            upload_status="pending"
        )
        db.add(upload_data)
        await db.commit()

        return JSONResponse({
            "success": True,
            "message": "Presigned URL generated successfully",
            "data": {
                "upload_info": url,
                "upload_id": file_name,
            }
        })

@router.get("/get-presigned-url")
async def get_presigned_url(
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get a presigned URL for uploading a file to S3.
    """
    file_name = str(uuid4())
    object_name = f"listings/{file_name}.pdf"
    url = create_presigned_post("documents-modus", object_name)

    if not url:
        raise HTTPException(status_code=500, detail="Error generating presigned URL")
    else:
        upload_data = models.CompanyDocumentUploaded(
            id=file_name,
            company_id="",
            upload_timestamp=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
            uploaded_by=str(current_user.id),
            aws_data={"bucket": "documents-modus", "key": object_name},
            upload_status="pending"
        )
        db.add(upload_data)
        await db.commit()

        return JSONResponse({
            "success": True,
            "message": "Presigned URL generated successfully",
            "data": {
                "upload_info": url,
                "upload_id": file_name,
            }
        })

class StatusUpdateRequest(BaseModel):
    upload_id: str
    status: str

@router.post("/update-upload-status")
async def update_upload_status(
    request: StatusUpdateRequest,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Update the status of an upload.
    """
    upload_id = request.upload_id
    status = request.status
    upload_data = await db.execute(
        select(models.CompanyDocumentUploaded).where(models.CompanyDocumentUploaded.id == upload_id)
    )
    upload_data = upload_data.scalars().first()

    if not upload_data:
        raise HTTPException(status_code=404, detail="Upload not found")
    else:
        upload_data.upload_status = status
        await db.commit()

        return JSONResponse({
            "success": True,
            "message": "Upload status updated successfully"
        })
    
@router.get("/{upload_id}/start-processing")
async def start_processing(
    upload_id: str,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Start processing an uploaded file.
    """
    job_id = str(uuid4())

    asyncio.create_task(full_preprocess_pdf(job_id, upload_id))

    return JSONResponse({
        "success": True,
        "message": "Processing started successfully",
        "data": {
            "job_id": job_id
        }
    })

@router.get("/start-icdr-processing")
async def start_icdr_processing(
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Start processing an ICDR uploaded file.
    """
    job_id = str(uuid4())

    asyncio.create_task(preprocess_icdr_doc(job_id, "01c08f3f-b4f9-4ebe-a715-c28259a81ab4"))

    return JSONResponse({
        "success": True,
        "message": "ICDR processing started successfully",
        "data": {
            "job_id": job_id
        }
    })

@router.get("/list_files")
async def list_files():
    files = list_s3_files("documents-modus", "chunked_data/")
    return JSONResponse({
        "success": True,
        "message": "Files listed successfully",
        "data": files
    })

@router.post("/bm25retrievertesting")
def bm25_retriever_testing(
    query: str
):
    doc_id = "f66b1351-ba44-41b5-baa6-d71ccf4d0b6c"
    bm25 = get_bm25_retriever(doc_id)
    if not bm25:
        raise HTTPException(status_code=500, detail="BM25 retriever not found")
    
    results = bm25.query(query, top_k=5)
    print(results)
    return JSONResponse({
        "success": True,
        "message": "BM25 retriever working",
        "data": results
    })

@router.post("/vectorretrievertesting")
def vector_retriever_testing(
    query: str
):
    doc_id = "f66b1351-ba44-41b5-baa6-d71ccf4d0b6c"
    vector_retriever = get_vector_retriever("modus2", "rag_retriever1")
    if not vector_retriever:
        raise HTTPException(status_code=500, detail="Vector retriever not found")
    
    results = vector_retriever.invoke(query)
    print(results)
    return JSONResponse({
        "success": True,
        "message": "Vector retriever working",
        "data": len(results)
    })

class RetrieverQuery(BaseModel):
    query: str
    doc_id: str

@router.post("/retriever/")
def retriever_agent(
    query: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    agent = ListingDataRetrievalAgent(researcher_llm_str="openai:o4-mini", supervisor_llm_str="openai:gpt-4.1-mini", doc_id=query.doc_id)
    results = agent.invoke(query.query)
    print("======= RESULTS =======")
    print(results)
    return JSONResponse({
        "success": True,
        "message": "Retriever agent working",
        "data": results
    })

@router.post("/icdrretriever/")
def icdr_retriever_agent(
    query: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    agent = ICDRDataRetrievalAgent(researcher_llm_str="openai:o4-mini", supervisor_llm_str="openai:gpt-4.1-mini", doc_id=query.doc_id)
    results = agent.invoke(query.query)
    return JSONResponse({
        "success": True,
        "message": "ICDR Retriever agent working",
        "data": results
    })

@router.post("/chat/")
def chat_with_bot(
    request: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Chat with the bot to get ICDR and Listing queries.
    """
    gpt_wrapper = GPTWrapper(doc_id=request.doc_id, iterations=2)
    response = gpt_wrapper.chat(request.query)

    if not response:
        raise HTTPException(status_code=500, detail="Error generating queries")
    
    print("======= GPT RESPONSE =======")
    content = response.content
    if content:
        try:
            content = ast.literal_eval(repr(content))
        except (SyntaxError, ValueError) as e:
            print(f"Error parsing response content: {e}")
    
    return JSONResponse({
        "success": True,
        "message": "Chat with bot successful",
        "data": content
    })

@router.post("/populate-company-info/")
async def populate_company_info_endpoint(
    request: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Populate company information from the uploaded document.
    """
    try:
        await data_ingestion.populate_company_info(request.doc_id)
        return JSONResponse({
            "success": True,
            "message": "Company information populated successfully"
        })
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/populate-ipo-offering/")
async def populate_ipo_offering_endpoint(
    request: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Populate IPO offering information from the uploaded document.
    """
    try:
        await data_ingestion.populate_ipo_offering(request.doc_id)
        return JSONResponse({
            "success": True,
            "message": "IPO offering information populated successfully"
        })
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/populate-peer-group/")
async def populate_peer_group_endpoint(
    request: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Populate peer group information from the uploaded document.
    """
    try:
        await data_ingestion.populate_peer_groups(request.doc_id)
        return JSONResponse({
            "success": True,
            "message": "Peer group information populated successfully"
        })
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/populate-book-running-lead-manager/")
async def populate_book_running_lead_manager_endpoint(
    request: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Populate book running lead manager information from the uploaded document.
    """
    try:
        await data_ingestion.populate_book_running_lead_manger(request.doc_id)
        return JSONResponse({
            "success": True,
            "message": "Book running lead manager information populated successfully"
        })
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/populate-registrar/")
async def populate_registrar_endpoint(
    request: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Populate registrar information from the uploaded document.
    """
    try:
        await data_ingestion.populate_registrar_table(request.doc_id)
        return JSONResponse({
            "success": True,
            "message": "Registrar information populated successfully"
        })
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/populate-legal-counsel/")
async def populate_legal_counsel_endpoint(
    request: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Populate legal counsel information from the uploaded document.
    """
    try:
        await data_ingestion.populate_legal_counsel_table(request.doc_id)
        return JSONResponse({
            "success": True,
            "message": "Legal counsel information populated successfully"
        })
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/populate-statutory-auditors/")
async def populate_statutory_auditors_endpoint(
    request: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Populate statutory auditors information from the uploaded document.
    """
    try:
        await data_ingestion.populate_statutory_auditor_table(request.doc_id)
        return JSONResponse({
            "success": True,
            "message": "Statutory auditors information populated successfully"
        })
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/populate-chartered-accountant/")
async def populate_chartered_accountant_endpoint(
    request: RetrieverQuery,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    """
    Populate chartered accountant information from the uploaded document.
    """
    try:
        await data_ingestion.populate_chartered_accountant(request.doc_id)
        return JSONResponse({
            "success": True,
            "message": "Chartered accountant information populated successfully"
        })
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/populate_financial_data/")
async def populate_finance_data(
    request: RetrieverQuery,
):
    try:
        await data_ingestion.populate_financials_data(request.doc_id)
        return JSONResponse({
            "success": True,
            "message": "Chartered accountant information populated successfully"
        })
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
