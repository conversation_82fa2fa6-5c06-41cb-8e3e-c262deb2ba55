from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from uuid import UUID
from ...models import models
from ..models.listing_models import IPOExternalInsights, ListingCompanies


async def get_external_data_internal(company_id: UUID, db: AsyncSession):
    """
    Get external data for a given IPO company.
    """
    try:
        # Check if company exists
        query = select(ListingCompanies).filter(ListingCompanies.id == company_id)
        result = await db.execute(query)
        company = result.scalars().first()
        
        if not company:
            return {"success": False, "message": "Company not found", "data": None}
        
        # Get external data insights
        query = select(IPOExternalInsights).filter(
            IPOExternalInsights.company_id == company_id,
            IPOExternalInsights.insight_type == "external_data"
        ).order_by(IPOExternalInsights.created_at.desc())
        result = await db.execute(query)
        external_data = result.scalars().all()

        if not external_data:
            return {"success": False, "message": "External data not found", "data": None}

        return {"success": True, "message": "External data fetched successfully", "data": external_data}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}


async def get_flags_from_auditor_disclosures_internal(company_id: UUID, db: AsyncSession):
    """
    Get flags from auditor disclosures for a given IPO company.
    """
    try:
        # Check if company exists
        query = select(ListingCompanies).filter(ListingCompanies.id == company_id)
        result = await db.execute(query)
        company = result.scalars().first()
        
        if not company:
            return {"success": False, "message": "Company not found", "data": None}
        
        # Get audit flags insights
        query = select(models.ExternalInsights).filter(
            models.ExternalInsights.merchant_id == company_id,
            models.ExternalInsights.insight_type == "audit_flags",
            models.ExternalInsights.product == "ipo"
        ).order_by(models.ExternalInsights.created_at.desc()).limit(3)
        result = await db.execute(query)
        flags = result.scalars().all()
        
        if not flags:
            return {"success": True, "message": "No flags found", "data": None}

        return {"success": True, "message": "Flags fetched successfully", "data": flags}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}


async def get_annual_report_insights_internal(company_id: UUID, db: AsyncSession):
    """
    Get annual report insights for a given IPO company.
    """
    try:
        # Check if company exists
        query = select(ListingCompanies).filter(ListingCompanies.id == company_id)
        result = await db.execute(query)
        company = result.scalars().first()
        
        if not company:
            return {"success": False, "message": "Company not found", "data": None}
        
        # Fetch annual report insights
        query = select(models.ExternalInsights).filter(
            models.ExternalInsights.merchant_id == company_id,
            models.ExternalInsights.insight_type == "annual_report_insights",
            models.ExternalInsights.product == "ipo"
        ).order_by(models.ExternalInsights.created_at.desc())
        result = await db.execute(query)
        insights = result.scalars().all()
        if not insights:
            return {"success": True, "message": "No insights found", "data": None}

        return {"success": True, "message": "Annual report insights fetched successfully", "data": insights}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}
