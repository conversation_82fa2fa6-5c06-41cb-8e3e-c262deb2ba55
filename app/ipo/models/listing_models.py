"""
IPO Listing Models Module

This module contains SQLAlchemy models for managing IPO (Initial Public Offering) listings,
company information, regulatory compliance, and related financial data.

The models support the complete IPO lifecycle including:
- Company registration and basic information
- IPO offering details and documentation
- Peer group comparisons
- Intermediary relationships (managers, registrars, auditors, etc.)
- Regulatory compliance tracking
- Financial data and reporting
- Management and promoter information
- External insights and analytics
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Date, Text, DECIMAL, ForeignKey, Table
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from uuid import uuid4
from ...database import Base


# Association tables for many-to-many relationships
brlm_listing_association = Table(
    'brlm_listing_association',
    Base.metadata,
    Column('brlm_id', UUID(as_uuid=True), ForeignKey('ipo.book_running_lead_managers.id'), primary_key=True),
    Column('listing_id', UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), primary_key=True),
    Column('company_id', UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
           comment="Reference to the issuing company for tracking purposes"),
    Column('created_at', DateTime, default=datetime.now, comment="Record creation timestamp"),
    Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp"),
    schema='ipo'
)

registrar_listing_association = Table(
    'registrar_listing_association',
    Base.metadata,
    Column('registrar_id', UUID(as_uuid=True), ForeignKey('ipo.registrar.id'), primary_key=True),
    Column('listing_id', UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), primary_key=True),
    Column('company_id', UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
           comment="Reference to the issuing company for tracking purposes"),
    Column('created_at', DateTime, default=datetime.now, comment="Record creation timestamp"),
    Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp"),
    schema='ipo'
)

legal_counsel_listing_association = Table(
    'legal_counsel_listing_association',
    Base.metadata,
    Column('legal_counsel_id', UUID(as_uuid=True), ForeignKey('ipo.legal_counsel.id'), primary_key=True),
    Column('listing_id', UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), primary_key=True),
    Column('company_id', UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
           comment="Reference to the issuing company for tracking purposes"),
    Column('created_at', DateTime, default=datetime.now, comment="Record creation timestamp"),
    Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp"),
    schema='ipo'
)

statutory_auditor_listing_association = Table(
    'statutory_auditor_listing_association',
    Base.metadata,
    Column('statutory_auditor_id', UUID(as_uuid=True), ForeignKey('ipo.statutory_auditor.id'), primary_key=True),
    Column('listing_id', UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), primary_key=True),
    Column('company_id', UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
           comment="Reference to the issuing company for tracking purposes"),
    Column('created_at', DateTime, default=datetime.now, comment="Record creation timestamp"),
    Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp"),
    schema='ipo'
)

independent_ca_listing_association = Table(
    'independent_ca_listing_association',
    Base.metadata,
    Column('independent_ca_id', UUID(as_uuid=True), ForeignKey('ipo.independent_charted_accountant.id'), primary_key=True),
    Column('listing_id', UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), primary_key=True),
    Column('company_id', UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
           comment="Reference to the issuing company for tracking purposes"),
    Column('created_at', DateTime, default=datetime.now, comment="Record creation timestamp"),
    Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp"),
    schema='ipo'
)


class ListingCompanies(Base):
    """
    Core company information for entities involved in IPO listings.

    This table stores comprehensive information about companies that are either
    planning to go public, currently in the IPO process, or have completed their
    public listing. It includes corporate details, compliance status, and
    contact information required for regulatory filings.
    """
    __tablename__ = 'listing_companies'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Corporate identification
    cin = Column(String(50), nullable=True, comment="Corporate Identification Number - unique identifier assigned by MCA")
    legal_name = Column(String(255), nullable=True, comment="Official legal name of the company as registered")

    # Filing and compliance status
    efiling_status = Column(String(100), nullable=True, comment="Electronic filing status with regulatory authorities")
    incorporation_date = Column(Date, nullable=True, comment="Date when the company was incorporated")

    # Capital structure
    paid_up_capital = Column(DECIMAL(20,2), nullable=True, comment="Total paid-up share capital in INR")
    sum_of_charges = Column(DECIMAL(20,2), nullable=True, comment="Total value of charges/mortgages on company assets")
    authorized_capital = Column(DECIMAL(20,2), nullable=True, comment="Maximum share capital authorized by MOA")

    # Compliance indicators
    active_compliance = Column(Boolean, default=True, comment="Whether company is actively compliant with regulations")

    # Address information (stored as JSON for flexibility)
    registered_address = Column(JSONB, nullable=True, comment="Registered office address with all components")
    business_address = Column(JSONB, nullable=True, comment="Principal business address if different from registered")

    # Tax and regulatory identifiers
    pan = Column(String(20), nullable=True, comment="Permanent Account Number for tax purposes")

    # Contact and business information
    website = Column(String(255), nullable=True, comment="Official company website URL")
    classification = Column(String(100), nullable=True, comment="Business classification or industry category")
    status = Column(String(50), nullable=True, comment="Current operational status of the company")

    # Corporate governance dates
    last_agm_date = Column(Date, nullable=True, comment="Date of last Annual General Meeting")
    last_filing_date = Column(Date, nullable=True, comment="Date of most recent regulatory filing")

    # Communication details
    email = Column(String(255), nullable=True, comment="Primary email address for company communications")
    description = Column(Text, nullable=True, comment="Brief description of company's business activities")
    contact_email = Column(String(255), nullable=True, comment="Contact email for investor relations or queries")
    contact_phone = Column(String(20), nullable=True, comment="Primary contact phone number")

    # Legal Entity Identifier (LEI) information
    lei_number = Column(String(50), nullable=True, comment="Legal Entity Identifier for global entity identification")
    lei_status = Column(String(50), nullable=True, comment="Current status of LEI registration")
    lei_registration_date = Column(Date, nullable=True, comment="Date when LEI was first registered")
    lei_last_updated_date = Column(Date, nullable=True, comment="Date when LEI information was last updated")
    lei_next_renewal_date = Column(Date, nullable=True, comment="Next scheduled LEI renewal date")

    # Historical information
    name_history = Column(JSONB, nullable=True, comment="Array of previous company names with effective dates")
    last_updated_date = Column(DateTime, nullable=True, comment="Last update to company information")

    # Business descriptions
    about_company = Column(Text, nullable=True, comment="Detailed description of company's business model and operations")
    about_industry = Column(Text, nullable=True, comment="Description of the industry sector and market position")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    ipo_offerings = relationship("IPOOfferings", back_populates="company")
    peer_groups = relationship("PeerGroup", back_populates="company")
    issuer_compliance_statuses = relationship("IssuerComplianceStatus", back_populates="company")
    management_promoters = relationship("ManagementPromoters", back_populates="company")
    offer_document_reviews = relationship("OfferDocumentReview", back_populates="company")
    financials = relationship("Financials", back_populates="company")
    external_insights = relationship("IPOExternalInsights", back_populates="company")


class IPOOfferings(Base):
    """
    IPO offering details and listing information.

    This table tracks specific IPO offerings, including listing dates, offer sizes,
    document types, and regulatory classifications. Each company can have multiple
    offerings (e.g., mainboard, SME, rights issue, etc.).
    """
    __tablename__ = 'ipo_offerings'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Company reference
    company_id = Column(UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
                       comment="Reference to the issuing company")

    # Listing details
    listing_date = Column(Date, nullable=True, comment="Actual or planned date for stock exchange listing")
    listing_type = Column(String(100), nullable=True, comment="Type of listing (Mainboard, SME, etc.)")

    # Offering identification
    offering_id = Column(String(100), nullable=True, comment="Unique identifier for this specific offering")
    eligibility_type = Column(String(100), nullable=True, comment="Regulatory eligibility category for the offering")
    document_type = Column(String(100), nullable=True, comment="Type of offering document (DRHP, RHP, Prospectus)")

    # Offering structure
    listing_details = Column(JSONB, nullable=True, comment="Detailed listing parameters and exchange information")
    offer_size = Column(DECIMAL(20,2), nullable=True, comment="Total offer size in INR (fresh issue + OFS)")
    aggregating_amount = Column(DECIMAL(20,2), nullable=True, comment="Total amount being raised through the IPO")
    # Document reference
    document_id = Column(String(100), nullable=True, comment="Reference ID for regulatory filing documents")
    
    # Extra artifacts data
    data = Column(JSONB, nullable=True, comment="Flexible JSON data for additional artifacts or metadata")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    company = relationship("ListingCompanies", back_populates="ipo_offerings")
    peer_groups = relationship("PeerGroup", back_populates="listing")
    book_running_lead_managers = relationship("BookRunningLeadManagers", secondary=brlm_listing_association, back_populates="listings")
    registrars = relationship("Registrar", secondary=registrar_listing_association, back_populates="listings")
    legal_counsels = relationship("LegalCounsel", secondary=legal_counsel_listing_association, back_populates="listings")
    statutory_auditors = relationship("StatutoryAuditor", secondary=statutory_auditor_listing_association, back_populates="listings")
    independent_charted_accountants = relationship("IndependentChartedAccountant", secondary=independent_ca_listing_association, back_populates="listings")
    issuer_compliance_statuses = relationship("IssuerComplianceStatus", back_populates="listing")
    management_promoters = relationship("ManagementPromoters", back_populates="listing")
    offer_document_reviews = relationship("OfferDocumentReview", back_populates="listing")
    financials = relationship("Financials", back_populates="listing")


class PeerGroup(Base):
    """
    Peer group comparison data for IPO valuation analysis.

    This table stores financial metrics and valuation parameters for companies
    in the same industry sector, used for benchmarking and comparative analysis
    during IPO pricing and evaluation.
    """
    __tablename__ = 'peer_group'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # References
    company_id = Column(UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
                       comment="Reference to the main company being analyzed")
    listing_id = Column(UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), nullable=True,
                       comment="Reference to the specific IPO offering")

    # Peer identification
    is_self = Column(Boolean, default=False, comment="Whether this entry represents the IPO company itself")
    name = Column(String(255), nullable=True, comment="Name of the peer company")

    # Company information
    website = Column(String(255), nullable=True, comment="Official website URL of the peer company")
    description = Column(Text, nullable=True, comment="Brief description of the peer company's business")
    valuation = Column(DECIMAL(20,2), nullable=True, comment="Current market valuation of the peer company")
    geography = Column(String(255), nullable=True, comment="Geographic presence or primary market of the peer company")
    primary_products_services = Column(JSONB, nullable=True, comment="Array of primary products or services offered by the peer company")

    # Financial metrics
    revenue_from_operations_in_million_inr = Column(DECIMAL(15,2), nullable=True,
                                                   comment="Annual revenue from operations (in millions INR)")
    face_value_per_equity_share = Column(DECIMAL(10,2), nullable=True,
                                        comment="Face value of each equity share")

    # Market data
    closing_price_inr = Column(DECIMAL(10,2), nullable=True, comment="Last closing price of the stock")
    closing_price_date = Column(Date, nullable=True, comment="Date of the closing price data")

    # Valuation ratios
    pe_ratio = Column(DECIMAL(8,2), nullable=True, comment="Price-to-Earnings ratio")
    eps_basic_inr = Column(DECIMAL(10,2), nullable=True, comment="Basic Earnings Per Share in INR")
    eps_diluted_inr = Column(DECIMAL(10,2), nullable=True, comment="Diluted Earnings Per Share in INR")
    ronw_percent = Column(DECIMAL(5,2), nullable=True, comment="Return on Net Worth as percentage")
    nav_per_equity_share_inr = Column(DECIMAL(10,2), nullable=True,
                                     comment="Net Asset Value per equity share in INR")

    # Status and classification
    disclosure_status = Column(String(100), nullable=True, comment="Disclosure status of the peer company")
    listing_status = Column(String(100), nullable=True, comment="Current listing status of the peer company")
    is_external = Column(Boolean, default=False, comment="Whether this is an external peer company")
    additional_data = Column(JSONB, nullable=True, comment="Additional flexible data for peer company")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    company = relationship("ListingCompanies", back_populates="peer_groups")
    listing = relationship("IPOOfferings", back_populates="peer_groups")


class BookRunningLeadManagers(Base):
    """
    Book Running Lead Managers (BRLMs) information for IPO offerings.

    BRLMs are investment banks or financial institutions responsible for managing
    the IPO process, including pricing, marketing, and distribution of shares.
    This table stores their contact details and regulatory information.
    """
    __tablename__ = 'book_running_lead_managers'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Institution details
    type = Column(String(100), nullable=True, comment="Type of BRLM (Lead Manager, Co-Lead Manager, etc.)")
    name = Column(String(255), nullable=True, comment="Name of the BRLM institution")
    unique_id = Column(String(100), nullable=True, comment="Internal unique identifier for the BRLM")

    # Contact information
    address = Column(JSONB, nullable=True, comment="Complete address details of the BRLM")
    mobile = Column(String(20), nullable=True, comment="Primary mobile contact number")
    email = Column(String(255), nullable=True, comment="Primary email for communication")
    grievance_email = Column(String(255), nullable=True, comment="Email for investor grievances and complaints")
    website = Column(String(255), nullable=True, comment="Official website URL")

    # Contact persons
    contact_person = Column(JSONB, nullable=True, comment="Details of key contact persons with roles")

    # Regulatory information
    sebi_registration_number = Column(String(100), nullable=True, comment="SEBI registration number for the BRLM")

    # Branding
    logo = Column(String(500), nullable=True, comment="URL or path to institution logo")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    listings = relationship("IPOOfferings", secondary=brlm_listing_association, back_populates="book_running_lead_managers")


class Registrar(Base):
    """
    Registrar and Transfer Agent information for IPO offerings.

    Registrars handle share transfer activities, maintain shareholder records,
    and manage post-listing services including dividend payments and
    corporate actions.
    """
    __tablename__ = 'registrar'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Institution details
    type = Column(String(100), nullable=True, comment="Type of registrar service (Share Transfer Agent, Registrar, etc.)")
    name = Column(String(255), nullable=True, comment="Name of the registrar and transfer agent")
    unique_id = Column(String(100), nullable=True, comment="Internal unique identifier for the registrar")

    # Contact information
    address = Column(JSONB, nullable=True, comment="Complete address details of the registrar")
    mobile = Column(String(20), nullable=True, comment="Primary mobile contact number")
    email = Column(String(255), nullable=True, comment="Primary email for communication")
    grievance_email = Column(String(255), nullable=True, comment="Email for investor grievances and complaints")
    website = Column(String(255), nullable=True, comment="Official website URL")

    # Contact persons
    contact_person = Column(JSONB, nullable=True, comment="Details of key contact persons with roles")

    # Regulatory information
    sebi_registration_number = Column(String(100), nullable=True, comment="SEBI registration number for the registrar")

    # Branding
    logo = Column(String(500), nullable=True, comment="URL or path to institution logo")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    listings = relationship("IPOOfferings", secondary=registrar_listing_association, back_populates="registrars")


class LegalCounsel(Base):
    """
    Legal advisors and counsels involved in IPO offerings.

    This table tracks legal firms representing either the company or
    underwriters during the IPO process, providing legal advice on
    regulatory compliance and documentation.
    """
    __tablename__ = 'legal_counsel'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Counsel classification
    type = Column(String(100), nullable=True, comment="Type of legal counsel (Company Counsel, Underwriter Counsel, Securities Counsel, etc.)")

    # Institution details
    name = Column(String(255), nullable=True, comment="Name of the legal firm or counsel")
    unique_id = Column(String(100), nullable=True, comment="Internal unique identifier for the legal counsel")

    # Contact information
    address = Column(JSONB, nullable=True, comment="Complete address details of the legal firm")
    mobile = Column(String(20), nullable=True, comment="Primary mobile contact number")
    email = Column(String(255), nullable=True, comment="Primary email for communication")
    grievance_email = Column(String(255), nullable=True, comment="Email for grievances and complaints")
    website = Column(String(255), nullable=True, comment="Official website URL")

    # Contact persons
    contact_person = Column(JSONB, nullable=True, comment="Details of key contact persons with roles")

    # Regulatory information
    sebi_registration_number = Column(String(100), nullable=True, comment="SEBI registration number if applicable")

    # Branding
    logo = Column(String(500), nullable=True, comment="URL or path to firm logo")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    listings = relationship("IPOOfferings", secondary=legal_counsel_listing_association, back_populates="legal_counsels")


class StatutoryAuditor(Base):
    """
    Statutory auditors responsible for auditing IPO company financials.

    These are chartered accountants or audit firms that conduct mandatory
    audits of the company's financial statements as required by law
    and for IPO compliance.
    """
    __tablename__ = 'statutory_auditor'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Auditor details
    type = Column(String(100), nullable=True, comment="Type of auditor (Statutory Auditor, Internal Auditor, Joint Auditor, etc.)")
    name = Column(String(255), nullable=True, comment="Name of the statutory auditor or audit firm")
    unique_id = Column(String(100), nullable=True, comment="Internal unique identifier for the auditor")

    # Contact information
    address = Column(JSONB, nullable=True, comment="Complete address details of the audit firm")
    mobile = Column(String(20), nullable=True, comment="Primary mobile contact number")
    email = Column(String(255), nullable=True, comment="Primary email for communication")
    grievance_email = Column(String(255), nullable=True, comment="Email for grievances and complaints")
    website = Column(String(255), nullable=True, comment="Official website URL")

    # Contact persons
    contact_person = Column(JSONB, nullable=True, comment="Details of key contact persons with roles")

    # Regulatory information
    sebi_registration_number = Column(String(100), nullable=True, comment="SEBI registration number if applicable")

    # Branding
    logo = Column(String(500), nullable=True, comment="URL or path to firm logo")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    listings = relationship("IPOOfferings", secondary=statutory_auditor_listing_association, back_populates="statutory_auditors")


class IndependentChartedAccountant(Base):
    """
    Independent chartered accountants providing specialized services.

    These are independent CAs who provide specific certifications,
    valuations, or specialized reports required for IPO compliance
    beyond regular statutory auditing.
    """
    __tablename__ = 'independent_charted_accountant'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Accountant details
    type = Column(String(100), nullable=True, comment="Type of service (Valuation, Certification, Tax Advisory, etc.)")
    name = Column(String(255), nullable=True, comment="Name of the independent chartered accountant")
    unique_id = Column(String(100), nullable=True, comment="Internal unique identifier for the accountant")

    # Contact information
    address = Column(JSONB, nullable=True, comment="Complete address details of the accountant")
    mobile = Column(String(20), nullable=True, comment="Primary mobile contact number")
    email = Column(String(255), nullable=True, comment="Primary email for communication")
    grievance_email = Column(String(255), nullable=True, comment="Email for grievances and complaints")
    website = Column(String(255), nullable=True, comment="Official website URL")

    # Contact persons
    contact_person = Column(JSONB, nullable=True, comment="Details of key contact persons with roles")

    # Regulatory information
    sebi_registration_number = Column(String(100), nullable=True, comment="SEBI registration number if applicable")

    # Branding
    logo = Column(String(500), nullable=True, comment="URL or path to firm logo")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    listings = relationship("IPOOfferings", secondary=independent_ca_listing_association, back_populates="independent_charted_accountants")


class Regulations(Base):
    """
    Regulatory framework and rules governing IPO processes.

    This table stores information about various regulations, guidelines,
    and rules issued by regulatory authorities like SEBI, that companies
    must comply with during the IPO process.
    """
    __tablename__ = 'regulations'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Regulation identification
    code = Column(String(100), nullable=True, comment="Official regulation code or reference number")
    title = Column(String(255), nullable=True, comment="Title or name of the regulation")
    description = Column(Text, nullable=True, comment="Detailed description of the regulation")

    # Regulatory authority
    authority = Column(String(255), nullable=True, comment="Issuing authority (SEBI, MCA, etc.)")

    # Temporal information
    effective_date = Column(Date, nullable=True, comment="Date when the regulation becomes effective")
    status = Column(String(50), nullable=True, comment="Current status (Active, Superseded, Draft)")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    compliance_clauses = relationship("ComplianceClauses", back_populates="regulation")


class ComplianceClauses(Base):
    """
    Specific compliance clauses and requirements within regulations.

    This table breaks down regulations into specific clauses and requirements
    that companies must comply with, enabling granular tracking of
    compliance obligations.
    """
    __tablename__ = 'compliance_clauses'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Parent regulation reference
    regulation_id = Column(UUID(as_uuid=True), ForeignKey('ipo.regulations.id'), nullable=True,
                          comment="Reference to the parent regulation")


    clause_id = Column(String(50), nullable=True, comment="Unique identifier for this specific clause")

    # Clause identification
    clause_id = Column(String(50), nullable=True, comment="Unique identifier for the compliance clause")
    clause_reference = Column(String(100), nullable=True, comment="Specific clause reference number or code")
    requirement = Column(Text, nullable=True, comment="Detailed requirement or obligation text")

    # Classification
    obligation_type = Column(String(100), nullable=True, comment="Type of obligation (Mandatory, Optional, etc.)")
    category = Column(String(100), nullable=True, comment="Category of requirement (Financial, Disclosure, etc.)")

    # Applicability
    applicability = Column(String(255), nullable=True, comment="Scope of applicability (All companies, specific sectors)")

    # Temporal information
    effective_date = Column(Date, nullable=True, comment="Date when this clause becomes effective")
    status = Column(String(50), nullable=True, comment="Current status of the clause")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    regulation = relationship("Regulations", back_populates="compliance_clauses")
    issuer_compliance_statuses = relationship("IssuerComplianceStatus", back_populates="clause")


class IssuerComplianceStatus(Base):
    """
    Compliance status tracking for IPO issuers against specific clauses.

    This table tracks how well each company complies with specific regulatory
    requirements, including issuer disclosures, independent verification,
    and compliance assessment results.
    """
    __tablename__ = 'issuer_compliance_status'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # References
    company_id = Column(UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
                       comment="Reference to the issuing company")
    listing_id = Column(UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), nullable=True,
                       comment="Reference to the specific IPO offering")
    clause_id = Column(UUID(as_uuid=True), ForeignKey('ipo.compliance_clauses.id'), nullable=True,
                      comment="Reference to the specific compliance clause")

    # Compliance documentation
    issuer_disclosure = Column(Text, nullable=True, comment="Company's disclosure or statement regarding compliance")
    independent_check = Column(Text, nullable=True, comment="Independent verification or audit findings")

    # Compliance assessment
    is_compliant = Column(Boolean, default=False, comment="Whether the company is compliant with this clause")
    checked_at = Column(DateTime, nullable=True, comment="Date and time when compliance was last verified")
    notes = Column(Text, nullable=True, comment="Additional notes or observations about compliance")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    company = relationship("ListingCompanies", back_populates="issuer_compliance_statuses")
    listing = relationship("IPOOfferings", back_populates="issuer_compliance_statuses")
    clause = relationship("ComplianceClauses", back_populates="issuer_compliance_statuses")


class ManagementPromoters(Base):
    """
    Management and promoter information for IPO companies.

    This table stores details about key management personnel, promoters,
    directors, and other key persons associated with the company,
    including their background checks and regulatory compliance status.
    """
    __tablename__ = 'management_promoters'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # References
    company_id = Column(UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
                       comment="Reference to the issuing company")
    listing_id = Column(UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), nullable=True,
                       comment="Reference to the specific IPO offering")

    # Personal information
    name = Column(String(255), nullable=True, comment="Full name of the person")
    age = Column(Integer, nullable=True, comment="Age of the person")

    # Identification documents
    pan = Column(String(20), nullable=True, comment="Permanent Account Number")
    aadhar = Column(String(20), nullable=True, comment="Aadhar card number")
    bank_account = Column(String(100), nullable=True, comment="Bank account details")

    # Professional details
    designation = Column(String(100), nullable=True, comment="Current designation or role in the company")
    date_of_appointment = Column(Date, nullable=True, comment="Date of appointment to current role")
    date_of_cessation = Column(Date, nullable=True, comment="Date of leaving the role (if applicable)")

    # Role classifications
    promoter_y_n = Column(Boolean, default=False, comment="Whether the person is a promoter")
    promoter_group_y_n = Column(Boolean, default=False, comment="Whether the person belongs to promoter group")
    director_y_n = Column(Boolean, default=False, comment="Whether the person is a director")
    kmp_y_n = Column(Boolean, default=False, comment="Whether the person is Key Managerial Personnel")

    # Regulatory status checks
    wilful_defaulter_list_y_n = Column(Boolean, default=False, comment="Whether listed as wilful defaulter")
    sebi_debarred_y_n = Column(Boolean, default=False, comment="Whether debarred by SEBI")
    disqualified_under_companies_act_y_n = Column(Boolean, default=False,
                                                 comment="Whether disqualified under Companies Act")

    # Relationship details
    relationship_type = Column(String(100), nullable=True, comment="Type of relationship with company/promoters")
    basis_of_inclusion = Column(String(255), nullable=True, comment="Reason for including in this list")

    additional_information = Column(Text, nullable=True, comment="Any additional information or notes about the person")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    company = relationship("ListingCompanies", back_populates="management_promoters")
    listing = relationship("IPOOfferings", back_populates="management_promoters")


class OfferDocumentReview(Base):
    """
    Review comments and feedback on IPO offer documents.

    This table captures review comments, clarification requests, and
    identified issues in offer documents (DRHP, RHP, Prospectus) during
    the regulatory review process.
    """
    __tablename__ = 'offer_document_review'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # References
    company_id = Column(UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
                       comment="Reference to the issuing company")
    listing_id = Column(UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), nullable=True,
                       comment="Reference to the specific IPO offering")

    # Document location
    section_of_offer_document = Column(String(255), nullable=True,
                                     comment="Section or chapter of the document being reviewed")
    page_no = Column(String(20), nullable=True, comment="Page number where the issue is located")

    # Review content
    original_text_excerpt = Column(Text, nullable=True, comment="Excerpt of original text being reviewed")
    review_content = Column(Text, nullable=True, comment="Review content including additional information needed, clarity issues, or issue explanations")
    review_type = Column(String(100), nullable=True, comment="Type of review content: 'additional_information', 'clarity_issue', or 'issue_reason'")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    company = relationship("ListingCompanies", back_populates="offer_document_reviews")
    listing = relationship("IPOOfferings", back_populates="offer_document_reviews")


class Financials(Base):
    """
    Comprehensive financial data for IPO companies.

    This table stores detailed financial information including balance sheet,
    profit & loss, cash flow statements, and auditor information. It supports
    multiple financial standards and filing types required for IPO compliance.
    """
    __tablename__ = 'financials'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # References
    company_id = Column(UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True,
                       comment="Reference to the issuing company")
    listing_id = Column(UUID(as_uuid=True), ForeignKey('ipo.ipo_offerings.id'), nullable=True,
                       comment="Reference to the specific IPO offering")

    # Financial statement metadata
    nature = Column(String(100), nullable=True, comment="Nature of financial statement (Standalone/Consolidated)")
    financial_year = Column(Date, nullable=True, comment="Financial year for which the statement is prepared")
    stated_on = Column(Date, nullable=True, comment="Date for which the financial statement is prepared")
    filing_type = Column(String(100), nullable=True, comment="Type of filing (Annual/Quarterly/Half-yearly)")
    filing_standard = Column(String(100), nullable=True, comment="Accounting standard used (Ind AS/GAAP/IFRS)")

    # ===========================================
    # BALANCE SHEET - ASSETS
    # ===========================================

    # Non-Current Assets
    tangible_assets = Column(DECIMAL(20,2), nullable=True, comment="Property, plant and equipment (net)")
    producing_properties = Column(DECIMAL(20,2), nullable=True, comment="Operational producing properties")
    intangible_assets = Column(DECIMAL(20,2), nullable=True, comment="Intangible assets (net)")
    preproducing_properties = Column(DECIMAL(20,2), nullable=True, comment="Properties under development")
    tangible_assets_capital_work_in_progress = Column(DECIMAL(20,2), nullable=True,
                                                    comment="Capital work in progress for tangible assets")
    intangible_assets_under_development = Column(DECIMAL(20,2), nullable=True,
                                               comment="Intangible assets under development")
    noncurrent_investments = Column(DECIMAL(20,2), nullable=True, comment="Long-term investments")
    deferred_tax_assets_net = Column(DECIMAL(20,2), nullable=True, comment="Net deferred tax assets")
    foreign_curr_monetary_item_trans_diff_asset_account = Column(DECIMAL(20,2), nullable=True,
                                                               comment="Foreign currency translation differences (assets)")
    long_term_loans_and_advances = Column(DECIMAL(20,2), nullable=True, comment="Long-term loans and advances")
    other_noncurrent_assets = Column(DECIMAL(20,2), nullable=True, comment="Other non-current assets")

    # Current Assets
    current_investments = Column(DECIMAL(20,2), nullable=True, comment="Short-term investments")
    inventories = Column(DECIMAL(20,2), nullable=True, comment="Total inventories")
    trade_receivables = Column(DECIMAL(20,2), nullable=True, comment="Trade receivables")
    cash_and_bank_balances = Column(DECIMAL(20,2), nullable=True, comment="Cash and bank balances")
    short_term_loans_and_advances = Column(DECIMAL(20,2), nullable=True, comment="Short-term loans and advances")
    other_current_assets = Column(DECIMAL(20,2), nullable=True, comment="Other current assets")
    given_assets_total = Column(DECIMAL(20,2), nullable=True, comment="Total assets as given in financial statements")

    # ===========================================
    # BALANCE SHEET - LIABILITIES & EQUITY
    # ===========================================

    # Equity
    share_capital = Column(DECIMAL(20,2), nullable=True, comment="Issued share capital")
    reserves_and_surplus = Column(DECIMAL(20,2), nullable=True, comment="Reserves and surplus")
    money_received_against_share_warrants = Column(DECIMAL(20,2), nullable=True,
                                                  comment="Money received against share warrants")
    share_application_money_pending_allotment = Column(DECIMAL(20,2), nullable=True,
                                                     comment="Share application money pending allotment")
    deferred_government_grants = Column(DECIMAL(20,2), nullable=True, comment="Deferred government grants")
    minority_interest = Column(DECIMAL(20,2), nullable=True, comment="Minority interest in subsidiaries")

    # Non-Current Liabilities
    long_term_borrowings = Column(DECIMAL(20,2), nullable=True, comment="Long-term borrowings")
    deferred_tax_liabilities_net = Column(DECIMAL(20,2), nullable=True, comment="Net deferred tax liabilities")
    foreign_curr_monetary_item_trans_diff_liability_account = Column(DECIMAL(20,2), nullable=True,
                                                                   comment="Foreign currency translation differences (liabilities)")
    other_long_term_liabilities = Column(DECIMAL(20,2), nullable=True, comment="Other long-term liabilities")
    long_term_provisions = Column(DECIMAL(20,2), nullable=True, comment="Long-term provisions")

    # Current Liabilities
    short_term_borrowings = Column(DECIMAL(20,2), nullable=True, comment="Short-term borrowings")
    trade_payables = Column(DECIMAL(20,2), nullable=True, comment="Trade payables")
    other_current_liabilities = Column(DECIMAL(20,2), nullable=True, comment="Other current liabilities")
    short_term_provisions = Column(DECIMAL(20,2), nullable=True, comment="Short-term provisions")
    given_liabilities_total = Column(DECIMAL(20,2), nullable=True,
                                   comment="Total liabilities as given in financial statements")

    # ===========================================
    # CALCULATED BALANCE SHEET ITEMS
    # ===========================================
    total_equity = Column(DECIMAL(20,2), nullable=True, comment="Calculated total equity")
    total_current_liabilities = Column(DECIMAL(20,2), nullable=True, comment="Calculated total current liabilities")
    total_non_current_liabilities = Column(DECIMAL(20,2), nullable=True, comment="Calculated total non-current liabilities")
    net_fixed_assets = Column(DECIMAL(20,2), nullable=True, comment="Calculated net fixed assets")
    total_current_assets = Column(DECIMAL(20,2), nullable=True, comment="Calculated total current assets")
    capital_wip = Column(DECIMAL(20,2), nullable=True, comment="Capital work in progress")
    total_debt = Column(DECIMAL(20,2), nullable=True, comment="Total debt (short-term + long-term)")
    gross_fixed_assets = Column(DECIMAL(20,2), nullable=True, comment="Gross fixed assets before depreciation")
    trade_receivable_exceeding_six_months = Column(DECIMAL(20,2), nullable=True,
                                                  comment="Trade receivables outstanding for more than 6 months")

    # ===========================================
    # PROFIT & LOSS STATEMENT
    # ===========================================

    # Revenue
    net_revenue = Column(DECIMAL(20,2), nullable=True, comment="Net revenue after adjustments")

    # Cost of Goods/Services
    total_cost_of_materials_consumed = Column(DECIMAL(20,2), nullable=True, comment="Total cost of materials consumed")
    total_purchases_of_stock_in_trade = Column(DECIMAL(20,2), nullable=True, comment="Purchases of stock-in-trade")
    total_changes_in_inventories_or_finished_goods = Column(DECIMAL(20,2), nullable=True,
                                                          comment="Changes in inventories of finished goods and WIP")
    total_employee_benefit_expense = Column(DECIMAL(20,2), nullable=True, comment="Total employee benefit expenses")
    total_other_expenses = Column(DECIMAL(20,2), nullable=True, comment="Other operating expenses")

    # Profitability
    operating_profit = Column(DECIMAL(20,2), nullable=True, comment="Operating profit (EBITDA)")
    other_income = Column(DECIMAL(20,2), nullable=True, comment="Other income (non-operating)")
    depreciation = Column(DECIMAL(20,2), nullable=True, comment="Depreciation and amortization")
    profit_before_interest_and_tax = Column(DECIMAL(20,2), nullable=True, comment="EBIT - Earnings before interest and tax")
    interest = Column(DECIMAL(20,2), nullable=True, comment="Interest expense")
    profit_before_tax_and_exceptional_items_before_tax = Column(DECIMAL(20,2), nullable=True,
                                                              comment="Profit before tax and exceptional items")
    exceptional_items_before_tax = Column(DECIMAL(20,2), nullable=True, comment="Exceptional items before tax")
    profit_before_tax = Column(DECIMAL(20,2), nullable=True, comment="Profit before tax")
    income_tax = Column(DECIMAL(20,2), nullable=True, comment="Income tax expense")
    profit_for_period_from_continuing_operations = Column(DECIMAL(20,2), nullable=True,
                                                        comment="Profit from continuing operations")
    profit_from_discontinuing_operation_after_tax = Column(DECIMAL(20,2), nullable=True,
                                                          comment="Profit from discontinued operations")
    minority_interest_and_profit_from_associates_and_joint_ventures = Column(DECIMAL(20,2), nullable=True,
                                                                           comment="Share of profit from associates and joint ventures")
    profit_after_tax = Column(DECIMAL(20,2), nullable=True, comment="Net profit after tax (PAT)")
    total_operating_cost = Column(DECIMAL(20,2), nullable=True, comment="Total operating costs")

    # ===========================================
    # REVENUE BREAKDOWN
    # ===========================================
    revenue_from_operations = Column(DECIMAL(20,2), nullable=True, comment="Revenue from operations")
    revenue_from_interest = Column(DECIMAL(20,2), nullable=True, comment="Interest income")
    revenue_from_other_financial_services = Column(DECIMAL(20,2), nullable=True,
                                                  comment="Revenue from other financial services")
    revenue_from_sale_of_products = Column(DECIMAL(20,2), nullable=True, comment="Revenue from sale of products")
    revenue_from_sale_of_services = Column(DECIMAL(20,2), nullable=True, comment="Revenue from sale of services")
    other_operating_revenues = Column(DECIMAL(20,2), nullable=True, comment="Other operating revenues")
    
    # Tax collections
    excise_duty = Column(DECIMAL(20,2), nullable=True, comment="Excise duty collected")
    service_tax_collected = Column(DECIMAL(20,2), nullable=True, comment="Service tax collected")
    other_duties_taxes_collected = Column(DECIMAL(20,2), nullable=True, comment="Other duties and taxes collected")
    
    # Geographic revenue split
    sale_of_goods_manufactured_domestic = Column(DECIMAL(20,2), nullable=True,
                                                comment="Domestic sales of manufactured goods")
    sale_of_goods_traded_domestic = Column(DECIMAL(20,2), nullable=True, comment="Domestic sales of traded goods")
    sale_or_supply_of_services_domestic = Column(DECIMAL(20,2), nullable=True, comment="Domestic services revenue")
    sale_or_supply_of_services_export = Column(DECIMAL(20,2), nullable=True, comment="Export services revenue")
    sale_of_goods_manufactured_export = Column(DECIMAL(20,2), nullable=True,
                                              comment="Export sales of manufactured goods")
    sale_of_goods_traded_export = Column(DECIMAL(20,2), nullable=True, comment="Export sales of traded goods")
    
    # ===========================================
    # DEPRECIATION DETAILS
    # ===========================================
    depreciation_amortisation = Column(DECIMAL(20,2), nullable=True, comment="Depreciation and amortisation")
    depletion = Column(DECIMAL(20,2), nullable=True, comment="Depletion of natural resources")
    depreciation_and_amortization = Column(DECIMAL(20,2), nullable=True, comment="Total depreciation and amortization")
    
    # ===========================================
    # CASH FLOW STATEMENT
    # ===========================================
    
    # Operating Activities
    profit_before_tax_cf = Column(DECIMAL(20,2), nullable=True, comment="Profit before tax for cash flow calculation")
    adjustment_for_finance_cost_and_depreciation = Column(DECIMAL(20,2), nullable=True,
                                                         comment="Adjustments for finance costs and depreciation")
    adjustment_for_current_and_non_current_assets = Column(DECIMAL(20,2), nullable=True,
                                                          comment="Working capital changes in current and non-current assets")
    adjustment_for_current_and_non_current_liabilities = Column(DECIMAL(20,2), nullable=True,
                                                              comment="Working capital changes in current and non-current liabilities")
    other_adjustments_in_operating_activities = Column(DECIMAL(20,2), nullable=True,
                                                      comment="Other adjustments in operating activities")
    cash_flows_from_used_in_operating_activities = Column(DECIMAL(20,2), nullable=True,
                                                         comment="Net cash flows from operating activities")
    
    # Investing Activities
    cash_outflow_from_purchase_of_assets = Column(DECIMAL(20,2), nullable=True,
                                                 comment="Cash outflow for purchase of assets")
    cash_inflow_from_sale_of_assets = Column(DECIMAL(20,2), nullable=True,
                                           comment="Cash inflow from sale of assets")
    income_from_assets = Column(DECIMAL(20,2), nullable=True, comment="Income from investments and assets")
    other_adjustments_in_investing_activities = Column(DECIMAL(20,2), nullable=True,
                                                      comment="Other adjustments in investing activities")
    cash_flows_from_used_in_investing_activities = Column(DECIMAL(20,2), nullable=True,
                                                         comment="Net cash flows from investing activities")
    
    # Financing Activities
    cash_outflow_from_repayment_of_capital_and_borrowings = Column(DECIMAL(20,2), nullable=True,
                                                                  comment="Cash outflow for repayment of loans and capital")
    cash_inflow_from_raisng_capital_and_borrowings = Column(DECIMAL(20,2), nullable=True,
                                                           comment="Cash inflow from raising capital and borrowings")
    interest_and_dividends_paid = Column(DECIMAL(20,2), nullable=True, comment="Interest and dividends paid")
    other_adjustments_in_financing_activities = Column(DECIMAL(20,2), nullable=True,
                                                      comment="Other adjustments in financing activities")
    cash_flows_from_used_in_financing_activities = Column(DECIMAL(20,2), nullable=True,
                                                         comment="Net cash flows from financing activities")
    
    # Net Cash Flow
    incr_decr_in_cash_cash_equv_before_effect_of_excg_rate_changes = Column(DECIMAL(20,2), nullable=True,
                                                                           comment="Net increase/decrease in cash before exchange rate effects")
    adjustments_to_cash_and_cash_equivalents = Column(DECIMAL(20,2), nullable=True,
                                                     comment="Adjustments for exchange rate changes")
    incr_decr_in_cash_cash_equv = Column(DECIMAL(20,2), nullable=True,
                                        comment="Net increase/decrease in cash and cash equivalents")
    cash_flow_statement_at_end_of_period = Column(DECIMAL(20,2), nullable=True,
                                                 comment="Cash and cash equivalents at end of period")
    
    # ===========================================
    # OTHER EXPENSE DETAILS
    # ===========================================
    managerial_remuneration = Column(DECIMAL(20,2), nullable=True, comment="Total managerial remuneration")
    payment_to_auditors = Column(DECIMAL(20,2), nullable=True, comment="Payments made to auditors")
    insurance_expenses = Column(DECIMAL(20,2), nullable=True, comment="Insurance expenses")
    power_and_fuel = Column(DECIMAL(20,2), nullable=True, comment="Power and fuel expenses")
    
    # ===========================================
    # AUDITOR INFORMATION
    # ===========================================
    auditor_name = Column(String(255), nullable=True, comment="Name of the auditor")
    auditor_firm_name = Column(String(255), nullable=True, comment="Name of the audit firm")
    pan = Column(String(20), nullable=True, comment="PAN of the auditor/firm")
    membership_number = Column(String(100), nullable=True, comment="ICAI membership number of the auditor")
    firm_registration_number = Column(String(100), nullable=True, comment="ICAI firm registration number")
    auditor_address = Column(JSONB, nullable=True, comment="Complete address of the auditor/firm")
    report_has_adverse_remarks = Column(Boolean, default=False, comment="Whether audit report contains adverse remarks")
    auditor_comments = Column(Text, nullable=True, comment="Key comments from auditor's report")
    auditor_additional = Column(Text, nullable=True, comment="Additional auditor observations and notes")
    
    # ===========================================
    # FLEXIBLE DATA STORAGE
    # ===========================================
    additional_fields = Column(JSONB, nullable=True, comment="Additional financial fields not covered by standard schema")
    data = Column(JSONB, nullable=True, comment="Raw financial data in JSON format")
    labels_map = Column(JSONB, nullable=True, comment="Mapping of field labels to standardized field names")
    data_source = Column(String(100), nullable=True, comment="Source of the financial data (e.g., internal system, third-party provider)")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    company = relationship("ListingCompanies", back_populates="financials")
    listing = relationship("IPOOfferings", back_populates="financials")


class IPOExternalInsights(Base):
    """
    External insights and analytics data for IPO companies.

    This table stores various external insights, market intelligence,
    and analytical data about IPO companies from third-party sources,
    research reports, and internal analysis systems.
    """
    __tablename__ = 'external_insights'
    __table_args__ = {'schema': 'ipo'}

    # Primary identifier
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Company reference
    company_id = Column(UUID(as_uuid=True), ForeignKey('ipo.listing_companies.id'), nullable=True, index=True,
                       comment="Reference to the company for which insights are provided")

    # Insight classification
    insight_type = Column(String(50), index=True, comment="Type of insight (market_analysis, risk_assessment, etc.)")

    # Insight data
    insight_value = Column(JSONB, nullable=True, comment="Structured insight data in JSON format")

    # Audit trail
    created_at = Column(DateTime, default=datetime.now, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Last record update timestamp")

    # Relationships
    company = relationship("ListingCompanies", back_populates="external_insights")