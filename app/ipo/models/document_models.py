from enum import Enum as PyEnum
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    Date,
    ForeignK<PERSON>,
    Integer,
    Numeric,
    String,
    Text,
    DateTime
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from ...database import Base


# ────────────────────────────────────────────────────────────────
#  Document Processing Models
# ────────────────────────────────────────────────────────────────

class CompanyDocumentUploaded(Base):
    __tablename__ = "ipo_company_documents_uploaded"
    __table_args__ = {'schema': 'public'}

    id = Column(String(255), primary_key=True)
    company_id = Column(String(255))

    upload_timestamp = Column(DateTime)
    uploaded_by = Column(String(256))
    aws_data = Column(JSONB)
    upload_status = Column(String(32))
    
    # Define back-references for relationships
    processing = relationship("CompanyDocumentRawData", back_populates="upload", uselist=False)
    processed_data = relationship("CompanyDocumentProcessedData", back_populates="upload", uselist=False)


class CompanyDocumentRawData(Base):
    __tablename__ = "ipo_company_documents_raw_data"
    __table_args__ = {'schema': 'public'}

    id = Column(String(255), primary_key=True)
    doc_id = Column(String(255), ForeignKey("public.ipo_company_documents_uploaded.id", ondelete="CASCADE"), nullable=False)
    job_id = Column(String(255), ForeignKey("public.ipo_job_manager.job_id", ondelete="CASCADE"))

    raw_data = Column(JSONB)

    created_at = Column(DateTime)
    updated_at = Column(DateTime)

    upload = relationship("CompanyDocumentUploaded", back_populates="processing")
    job = relationship("Job", back_populates="raw_data")


class Job(Base):
    __tablename__ = "ipo_job_manager"
    __table_args__ = {'schema': 'public'}

    job_id = Column(String(255), primary_key=True)
    doc_id = Column(String(255), ForeignKey("public.ipo_company_documents_uploaded.id", ondelete="CASCADE"))
    job_type = Column(String(255))
    job_status = Column(String(255))
    job_history = Column(JSONB)
    job_data = Column(JSONB)
    job_error = Column(Text)
    job_created_at = Column(DateTime)
    job_updated_at = Column(DateTime)

    raw_data = relationship("CompanyDocumentRawData", back_populates="job", uselist=False)
    processed_data = relationship("CompanyDocumentProcessedData", back_populates="job", uselist=False)

class CompanyDocumentProcessedData(Base):
    __tablename__ = "ipo_company_documents_processed_data"
    __table_args__ = {'schema': 'public'}

    id = Column(String(255), primary_key=True)
    doc_id = Column(String(255), ForeignKey("public.ipo_company_documents_uploaded.id", ondelete="CASCADE"), nullable=False)
    job_id = Column(String(255), ForeignKey("public.ipo_job_manager.job_id", ondelete="CASCADE"))
    processed_data = Column(JSONB)
    table_of_contents = Column(JSONB)

    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    pushed_to_pinecone = Column(Boolean, default=False)

    upload = relationship("CompanyDocumentUploaded", back_populates="processed_data")
    job = relationship("Job", back_populates="processed_data")
