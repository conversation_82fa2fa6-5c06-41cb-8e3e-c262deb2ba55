from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from uuid import UUID
from pydantic import BaseModel

class CompanyBase(BaseModel):
    legal_name: Optional[str] = None
    cin: Optional[str] = None
    efiling_status: Optional[str] = None
    incorporation_date: Optional[date] = None
    paid_up_capital: Optional[float] = None
    authorized_capital: Optional[float] = None
    active_compliance: Optional[bool] = None
    website: Optional[str] = None
    email: Optional[str] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    classification: Optional[str] = None
    status: Optional[str] = None
    description: Optional[str] = None
    about_company: Optional[str] = None
    about_industry: Optional[str] = None

class CompanyResponse(CompanyBase):
    id: UUID
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class CompaniesListResponse(BaseModel):
    companies: List[CompanyResponse]
    total: int
    skip: int
    limit: int

class IPOOfferingBase(BaseModel):
    listing_date: Optional[date] = None
    listing_type: Optional[str] = None
    eligibility_type: Optional[str] = None
    document_type: Optional[str] = None
    offer_size: Optional[float] = None
    document_id: Optional[str] = None
    listing_details: Optional[Dict[str, Any]] = None

class IPOOfferingResponse(IPOOfferingBase):
    id: UUID
    company_id: Optional[UUID] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class EnrichedIPOOfferingResponse(BaseModel):
    id: UUID
    company_id: Optional[UUID] = None
    company_name: Optional[str] = None
    listing_date: Optional[date] = None
    listing_type: Optional[str] = None
    eligibility_type: Optional[str] = None
    document_type: Optional[str] = None
    offer_size: Optional[float] = None
    document_id: Optional[str] = None
    listing_details: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class CompanyListingsResponse(BaseModel):
    company_id: str
    company_name: Optional[str]
    listings: List[IPOOfferingResponse]

class CompanyAboutResponse(BaseModel):
    company_id: str
    legal_name: Optional[str]
    about_company: Optional[str]
    description: Optional[str]
    website: Optional[str]
    email: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]
    registered_address: Optional[Dict[str, Any]]
    business_address: Optional[Dict[str, Any]]

class CompanyIndustryResponse(BaseModel):
    company_id: str
    legal_name: Optional[str]
    about_industry: Optional[str]
    classification: Optional[str]

class IssueDetailsFreshIssue(BaseModel):
    numberOfShares: str
    faceValue: str
    aggregatingAmount: str

class IssueDetailsOfferForSale(BaseModel):
    numberOfShares: str
    faceValue: str
    aggregatingAmount: str

class IssueDetails(BaseModel):
    freshIssue: Optional[IssueDetailsFreshIssue] = None
    offerForSale: Optional[IssueDetailsOfferForSale] = None

class OfferingDetailsResponse(BaseModel):
    offeringId: str
    legalName: str
    securityType: str
    documentType: str
    offeringDate: Optional[str] = None
    eligibilityType: str
    issueType: str
    totalIssueSize: str
    totalOfferAmount: str
    eFilingStatus: str
    activeCompliance: str
    companyIncorporationDate: Optional[str] = None
    businessAge: str
    designatedExchange: str
    auditQualifications: int
    issueDetails: Optional[IssueDetails] = None
    data: Optional[Dict] = None

# Peer Group Comparison Schemas
# Peer Group Comparison Data Models
class DisclosedPeerData(BaseModel):
    name: str
    disclosure_status: Optional[str] = None
    listing_status: Optional[str] = None
    revenue_from_operations_in_million_inr: Optional[float] = None
    face_value_per_equity_share: Optional[str] = None
    closing_price_inr: Any  # Can be float or "NA"
    closing_price_date: Optional[str] = None
    pe_ratio: Any  # Can be float or "NA"
    eps_basic_inr: Optional[float] = None
    eps_diluted_inr: Optional[float] = None
    ronw_percent: Optional[float] = None
    nav_per_equity_share_inr: Optional[float] = None

class ExternalPeerData(BaseModel):
    name: str
    disclosure_status: Optional[str] = None
    listing_status: Optional[str] = None
    website: Optional[str] = None
    description: Optional[str] = None
    revenue: Optional[str] = None
    valuation: Optional[str] = None
    geography: Optional[str] = None
    primary_products_services: Optional[str] = None

class PeerGroupComparisonResponse(BaseModel):
    disclosed_peers: List[DisclosedPeerData]
    external_peers: List[ExternalPeerData]
    disclosed_labels_map: Dict[str, str]
    external_labels_map: Dict[str, str]

# Service Provider Base Schema
class ServiceProviderBase(BaseModel):
    id: UUID
    name: Optional[str] = None
    unique_id: Optional[str] = None
    address: Optional[Dict[str, Any]] = None
    mobile: Optional[str] = None
    email: Optional[str] = None
    grievance_email: Optional[str] = None
    website: Optional[str] = None
    contact_person: Optional[Any] = None  # Can be Dict or List[Dict]
    sebi_registration_number: Optional[str] = None
    logo: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class LegalCounselResponse(ServiceProviderBase):
    type: Optional[str] = None

class ServiceProvidersResponse(BaseModel):
    book_running_lead_managers: List[ServiceProviderBase]
    registrars: List[ServiceProviderBase]
    legal_counsels: List[LegalCounselResponse]
    statutory_auditors: List[ServiceProviderBase]
    independent_charted_accountants: Optional[List[ServiceProviderBase]] = []

class ServiceProviderWithListings(BaseModel):
    provider: ServiceProviderBase
    associated_listings: List[EnrichedIPOOfferingResponse]

class LegalCounselWithListings(BaseModel):
    provider: LegalCounselResponse
    associated_listings: List[EnrichedIPOOfferingResponse]

class AllServiceProvidersResponse(BaseModel):
    book_running_lead_managers: List[ServiceProviderWithListings]
    registrars: List[ServiceProviderWithListings]
    legal_counsels: List[LegalCounselWithListings]
    statutory_auditors: List[ServiceProviderWithListings]
    independent_charted_accountants: Optional[List[ServiceProviderWithListings]] = []

# Company Data Schemas for enhanced service provider response
class LinkedOfferingData(BaseModel):
    offering_id: str
    legal_name: str
    total_issue_size: Optional[str] = None
    offering_date: Optional[str] = None

class CompanyData(BaseModel):
    company_id: str
    legal_name: str
    org_type: str = "Listing Company"
    website: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    linked_offerings: List[LinkedOfferingData]

class AllServiceProvidersWithCompaniesResponse(BaseModel):
    book_running_lead_managers: List[ServiceProviderWithListings]
    registrars: List[ServiceProviderWithListings]
    legal_counsels: List[LegalCounselWithListings]
    statutory_auditors: List[ServiceProviderWithListings]
    independent_charted_accountants: Optional[List[ServiceProviderWithListings]] = []
    companies: List[CompanyData]

# Management Promoters Schemas
class ManagementPromoterData(BaseModel):
    name: str
    age: Optional[int] = None
    pan: Optional[str] = None
    aadhar: Optional[str] = None
    bank_account: Optional[str] = None
    designation: Optional[str] = None
    date_of_appointment: Optional[str] = None
    date_of_cessation: Optional[str] = None
    promoter_y_n: str
    promoter_group_y_n: str
    director_y_n: str
    kmp_y_n: str
    wilful_defaulter_list_y_n: str
    sebi_debarred_y_n: str
    disqualified_under_companies_act_y_n: str
    relationship: Optional[str] = None
    basis_of_inclusion: Optional[str] = None
    additional_information: Optional[str] = None

class ManagementPromotersResponse(BaseModel):
    data: List[ManagementPromoterData]
    labels_map: Dict[str, str]

# Offer Document Review Schemas
class OfferDocumentReviewData(BaseModel):
    id: str
    section_of_offer_document: str
    page_no: str
    original_text_excerpt: str
    review_content: str
    review_type: str

class OfferDocumentReviewResponse(BaseModel):
    data: List[OfferDocumentReviewData]
    labels_map: Dict[str, str]

# Financial Metrics Schemas
class FinancialMetricData(BaseModel):
    label: str
    value: str
    numerical_value: Optional[float] = None
    icon: str
    description: str

class FinancialMetricsDataSource(BaseModel):
    nature: Optional[str] = None
    filing_standard: Optional[str] = None
    filing_type: Optional[str] = None

class FinancialMetricsData(BaseModel):
    company_id: str
    listing_id: Optional[str] = None
    company_name: str
    listing_type: Optional[str] = None
    financial_year: Optional[str] = None
    metrics: List[FinancialMetricData]
    data_source: FinancialMetricsDataSource

class FinancialMetricsResponse(BaseModel):
    success: bool
    message: str
    data: FinancialMetricsData

class PresignedUrlRequest(BaseModel):
    filename: str = Field(..., description="Name of the file to be uploaded")
    content_type: Optional[str] = Field(default="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", description="MIME type of the file")

class PresignedUrlResponse(BaseModel):
    upload_url: str = Field(..., description="The presigned URL for uploading")
    file_key: str = Field(..., description="The S3 key/path for the uploaded file")
    fields: Dict[str, Any] = Field(..., description="Additional form fields required for upload")

class ExcelProcessRequest(BaseModel):
    s3_file_path: str = Field(..., description="S3 path of the uploaded Excel file")
    company_id: Optional[UUID] = Field(None, description="Company ID to associate with the financials")
    listing_id: Optional[UUID] = Field(None, description="Listing ID to associate with the financials")

class ExcelProcessResponse(BaseModel):
    success: bool = Field(..., description="Whether the processing was successful")
    message: str = Field(..., description="Status message")
    processed_records: Optional[int] = Field(None, description="Number of records processed")
    errors: Optional[List[str]] = Field(None, description="Any errors encountered during processing")
    financials_created: Optional[List[UUID]] = Field(None, description="IDs of created financial records")
