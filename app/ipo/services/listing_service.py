"""
Listing Service

Contains business logic for IPO listing operations.
"""

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from ..models.listing_models import (
    ListingCompanies, IPOOfferings, Financials, PeerGroup,
    BookRunningLeadManagers, Registrar, LegalCounsel,
    StatutoryAuditor, IndependentChartedAccountant,
    ManagementPromoters, OfferDocumentReview
)


class ListingService:
    """Service class for IPO listing operations."""
    
    @staticmethod
    def get_all_companies(db: Session, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """Get all companies with pagination."""
        companies = db.query(ListingCompanies)\
            .offset(skip)\
            .limit(limit)\
            .all()
        
        return [
            {
                "id": str(company.id),
                "legal_name": company.legal_name,
                "cin": company.cin
            }
            for company in companies
        ]
    
    @staticmethod
    def get_company_listings(db: Session, company_id: str) -> Optional[ListingCompanies]:
        """Get company with all its listings."""
        return db.query(ListingCompanies)\
            .filter(ListingCompanies.id == company_id)\
            .first()
    
    @staticmethod
    def get_company_about(db: Session, company_id: str) -> Optional[ListingCompanies]:
        """Get company about information."""
        return db.query(ListingCompanies)\
            .filter(ListingCompanies.id == company_id)\
            .first()
    
    @staticmethod
    def get_offering_details(db: Session, company_id: str, listing_id: str) -> Optional[IPOOfferings]:
        """Get offering details for a specific listing."""
        return db.query(IPOOfferings)\
            .filter(
                IPOOfferings.company_id == company_id,
                IPOOfferings.listing_id == listing_id
            )\
            .first()
    
    @staticmethod
    def get_peer_companies(db: Session, company_id: str, listing_id: str) -> List[PeerGroup]:
        """Get peer companies for comparison."""
        return db.query(PeerGroup)\
            .filter(
                PeerGroup.company_id == company_id,
                PeerGroup.listing_id == listing_id
            )\
            .all()
    
    @staticmethod
    def get_service_providers(db: Session, company_id: str, listing_id: str) -> Dict[str, List]:
        """Get all service providers for a listing."""
        # Get the listing first
        listing = db.query(IPOOfferings).filter(IPOOfferings.id == listing_id).first()
        if not listing:
            return {
                "book_running_lead_managers": [],
                "registrars": [],
                "legal_counsels": [],
                "statutory_auditors": [],
                "independent_chartered_accountants": []
            }
        
        # Use the many-to-many relationships to get service providers
        brlms = listing.book_running_lead_managers
        registrars = listing.registrars
        legal_counsels = listing.legal_counsels
        auditors = listing.statutory_auditors
        accountants = listing.independent_charted_accountants

        return {
            "book_running_lead_managers": brlms,
            "registrars": registrars,
            "legal_counsels": legal_counsels,
            "statutory_auditors": auditors,
            "independent_chartered_accountants": accountants
        }
    
    @staticmethod
    def get_management_promoters(db: Session, company_id: str, listing_id: str) -> List[ManagementPromoters]:
        """Get management and promoters information."""
        return db.query(ManagementPromoters)\
            .filter(
                ManagementPromoters.company_id == company_id,
                ManagementPromoters.listing_id == listing_id
            )\
            .all()
    
    @staticmethod
    def get_offer_document_review(db: Session, company_id: str, listing_id: str) -> List[OfferDocumentReview]:
        """Get offer document review information."""
        return db.query(OfferDocumentReview)\
            .filter(
                OfferDocumentReview.company_id == company_id,
                OfferDocumentReview.listing_id == listing_id
            )\
            .all()
    
    @staticmethod
    def get_listing_summary(db: Session, company_id: str, listing_id: str) -> Dict[str, Any]:
        """Get comprehensive listing summary."""
        # Get company info
        company = ListingService.get_company_about(db, company_id)
        if not company:
            return None
        
        # Get listing info
        listing = next((l for l in company.listings if str(l.id) == listing_id), None)
        if not listing:
            return None
        
        # Get offering details
        offering = ListingService.get_offering_details(db, company_id, listing_id)
        
        # Get counts for various data types
        peer_count = len(ListingService.get_peer_companies(db, company_id, listing_id))
        service_providers = ListingService.get_service_providers(db, company_id, listing_id)
        
        brlm_count = len(service_providers["book_running_lead_managers"])
        registrar_count = len(service_providers["registrars"])
        promoters_count = len(ListingService.get_management_promoters(db, company_id, listing_id))
        review_count = len(ListingService.get_offer_document_review(db, company_id, listing_id))
        
        # Get financials count
        financials_count = db.query(func.count(Financials.id))\
            .filter(
                Financials.company_id == company_id,
                Financials.listing_id == listing_id
            )\
            .scalar()
        
        return {
            "company_info": {
                "id": str(company.id),
                "legal_name": company.legal_name,
                "cin": company.cin,
                "industry": company.industry,
                "website": company.website,
                "email": company.email
            },
            "listing_info": {
                "id": listing_id,
                "listing_date": listing.listing_date.isoformat() if listing.listing_date else None,
                "listing_type": listing.listing_type,
                "eligibility_type": listing.eligibility_type,
                "document_type": listing.document_type,
                "offer_size": float(listing.offer_size) if listing.offer_size else None,
                "document_id": listing.document_id
            },
            "offering_details": {
                "offer_size": float(offering.offer_size) if offering and offering.offer_size else None,
                "offer_size_ipo": float(offering.offer_size_ipo) if offering and offering.offer_size_ipo else None,
                "offer_size_ofs": float(offering.offer_size_ofs) if offering and offering.offer_size_ofs else None,
                "equity_shares_offered": offering.equity_shares_offered if offering else None,
                "face_value_per_share": float(offering.face_value_per_share) if offering and offering.face_value_per_share else None,
                "price_band_lower": float(offering.price_band_lower) if offering and offering.price_band_lower else None,
                "price_band_upper": float(offering.price_band_upper) if offering and offering.price_band_upper else None,
                "minimum_bid_lot": offering.minimum_bid_lot if offering else None,
                "minimum_bid_quantity": offering.minimum_bid_quantity if offering else None
            } if offering else None,
            "data_availability": {
                "peer_companies": peer_count,
                "book_running_lead_managers": brlm_count,
                "registrars": registrar_count,
                "management_promoters": promoters_count,
                "document_reviews": review_count,
                "financial_records": financials_count
            },
            "api_endpoints": {
                "peer_comparison": f"/api/v1/ipo/companies/{company_id}/listings/{listing_id}/peer-comparison",
                "service_providers": f"/api/v1/ipo/companies/{company_id}/listings/{listing_id}/service-providers",
                "management_promoters": f"/api/v1/ipo/companies/{company_id}/listings/{listing_id}/management-promoters",
                "offer_document_review": f"/api/v1/ipo/companies/{company_id}/listings/{listing_id}/offer-document-review",
                "company_about": f"/api/v1/ipo/companies/{company_id}/about",
                "company_industry": f"/api/v1/ipo/companies/{company_id}/industry",
                "external_data": f"/api/v1/ipo/companies/{company_id}/getExternalData",
                "auditor_disclosures": f"/api/v1/ipo/companies/{company_id}/getFlagsFromAuditorDisclosures",
                "annual_report_insights": f"/api/v1/ipo/companies/{company_id}/getAnnualReportInsights"
            }
        }
    
    