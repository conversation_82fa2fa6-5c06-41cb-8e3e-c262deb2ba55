"""
External Insights Service

Contains business logic for IPO external insights operations.
"""

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
import logging

from ..models.listing_models import IPOExternalInsights, ListingCompanies

logger = logging.getLogger(__name__)


class ExternalInsightsService:
    """Service class for IPO external insights operations."""
    
    @staticmethod
    async def get_external_data(db: AsyncSession, company_id: str) -> List[IPOExternalInsights]:
        """Get external data insights for a company."""
        try:
            # Query external insights for the company
            result = await db.execute(
                select(IPOExternalInsights)
                .filter(IPOExternalInsights.company_id == company_id)
                .filter(IPOExternalInsights.insight_type == "external_data")
            )
            insights = result.scalars().all()
            return insights
        except Exception as e:
            logger.error(f"Error fetching external data for company {company_id}: {str(e)}")
            raise
    
    @staticmethod
    async def get_flags_from_auditor_disclosures(db: AsyncSession, company_id: str) -> List[IPOExternalInsights]:
        """Get flags from auditor disclosures for a company."""
        try:
            # Query auditor disclosure insights for the company
            result = await db.execute(
                select(IPOExternalInsights)
                .filter(IPOExternalInsights.company_id == company_id)
                .filter(IPOExternalInsights.insight_type == "auditor_disclosures")
            )
            insights = result.scalars().all()
            return insights
        except Exception as e:
            logger.error(f"Error fetching auditor disclosures for company {company_id}: {str(e)}")
            raise
    
    @staticmethod
    async def get_annual_report_insights(db: AsyncSession, company_id: str) -> List[IPOExternalInsights]:
        """Get annual report insights for a company."""
        try:
            # Query annual report insights for the company
            result = await db.execute(
                select(IPOExternalInsights)
                .filter(IPOExternalInsights.company_id == company_id)
                .filter(IPOExternalInsights.insight_type == "annual_report")
            )
            insights = result.scalars().all()
            return insights
        except Exception as e:
            logger.error(f"Error fetching annual report insights for company {company_id}: {str(e)}")
            raise
    
    @staticmethod
    async def create_external_insight(
        db: AsyncSession, 
        company_id: str, 
        insight_type: str, 
        insight_value: Dict[str, Any]
    ) -> IPOExternalInsights:
        """Create a new external insight."""
        try:
            insight = IPOExternalInsights(
                company_id=company_id,
                insight_type=insight_type,
                insight_value=insight_value
            )
            db.add(insight)
            await db.commit()
            await db.refresh(insight)
            return insight
        except Exception as e:
            await db.rollback()
            logger.error(f"Error creating external insight: {str(e)}")
            raise
    
    @staticmethod
    async def update_external_insight(
        db: AsyncSession, 
        insight_id: str, 
        insight_value: Dict[str, Any]
    ) -> Optional[IPOExternalInsights]:
        """Update an existing external insight."""
        try:
            result = await db.execute(
                select(IPOExternalInsights)
                .filter(IPOExternalInsights.id == insight_id)
            )
            insight = result.scalar_one_or_none()
            
            if insight:
                insight.insight_value = insight_value
                insight.updated_at = datetime.utcnow()
                await db.commit()
                await db.refresh(insight)
                return insight
            return None
        except Exception as e:
            await db.rollback()
            logger.error(f"Error updating external insight {insight_id}: {str(e)}")
            raise
    
    @staticmethod
    async def delete_external_insight(db: AsyncSession, insight_id: str) -> bool:
        """Delete an external insight."""
        try:
            result = await db.execute(
                select(IPOExternalInsights)
                .filter(IPOExternalInsights.id == insight_id)
            )
            insight = result.scalar_one_or_none()
            
            if insight:
                await db.delete(insight)
                await db.commit()
                return True
            return False
        except Exception as e:
            await db.rollback()
            logger.error(f"Error deleting external insight {insight_id}: {str(e)}")
            raise
