import json
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from fastapi import HTTPException
from ..models import models
from datetime import datetime, timedelta
from ..utils.llm import create_and_run_perp_pipeline
from ..static.parsers_cr import about_the_company_parser, about_the_industry_parser
from ..static.prompts_cr import PROMPT_INFORMATION_ABOUT_COMPANY, PROMPT_INFORMATION_ABOUT_INDUSTY

def format_indian_currency(value):
    if value is None:
        return None
    value = float(value)
    if value < 0:
        value = abs(value)
        prefix = "-₹"
    else:
        prefix = "₹"
    
    str_value = f"{value:,.0f}"
    parts = str_value.split(".")
    num = parts[0]
    
    last_three = num[-3:]
    other_numbers = num[:-3]
    if other_numbers:
        formatted = other_numbers[::-1].replace(",", "")
        formatted = ",".join(formatted[i:i+2] for i in range(0, len(formatted), 2))
        formatted = formatted[::-1] + "," + last_three
    else:
        formatted = last_three
    
    return prefix + formatted

async def get_financials_table_internal(merchant_id: UUID, db: AsyncSession):
    try:
        query = select(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id)
        result = await db.execute(query)
        merchant = result.scalars().first()
        
        if not merchant:
            return {"success": False, "message": "Merchant not found", "data": None}
        
        query = select(models.probe_financials).filter(
            models.probe_financials.merchant_id == merchant_id, 
            models.probe_financials.nature == "STANDALONE"
        ).order_by(models.probe_financials.year.desc()).limit(5)
        
        result = await db.execute(query)
        financials_table = result.scalars().all()

        if not financials_table:
            return {"success": False, "message": "Financials table not found", "data": None}

        return {"success": True, "message": "Financials table fetched successfully", "data": financials_table}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}

async def get_about_company_internal(merchant_id: UUID, db: AsyncSession):
    try:
        query = select(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id)
        result = await db.execute(query)
        merchant = result.scalars().first()
        
        if not merchant:
            return {"success": False, "message": "Merchant not found", "data": None}
        
        query = select(models.CompanyInsights).filter(
            models.CompanyInsights.merchant_id == merchant_id,
            models.CompanyInsights.updated_at >= datetime.utcnow() - timedelta(days=30)
        )
        result = await db.execute(query)
        cached_data = result.scalars().first()

        if cached_data:
            return {"success": True, "message": "About company data fetched from cache", "data": cached_data.insights}

        prompt = f"Company Name: {merchant.legal_name}"
        response = create_and_run_perp_pipeline(
            system_prompt=PROMPT_INFORMATION_ABOUT_COMPANY,
            parser=about_the_company_parser,
            user_inp=prompt,
        )

        new_insight = models.CompanyInsights(merchant_id=merchant_id, insights=response)
        db.add(new_insight)
        await db.commit()

        return {"success": True, "message": "About company data fetched successfully", "data": response}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}

async def get_about_industry_internal(merchant_id: UUID, db: AsyncSession):
    try:
        query = select(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id)
        result = await db.execute(query)
        merchant = result.scalars().first()
        
        if not merchant:
            return {"success": False, "message": "Merchant not found", "data": None}
        
        query = select(models.probe_industry_segments).filter(
            models.probe_industry_segments.merchant_id == merchant_id
        )
        result = await db.execute(query)
        industry = result.scalars().all()
        
        if not industry:
            return {"success": False, "message": "Industry data not found", "data": None}

        industry_name = industry[0].industry
        query = select(models.IndustryInsights).filter(
            models.IndustryInsights.industry_name == industry_name,
            models.IndustryInsights.updated_at >= datetime.utcnow() - timedelta(days=30)
        )
        result = await db.execute(query)
        cached_data = result.scalars().first()

        if cached_data:
            return {"success": True, "message": "About industry data fetched from cache", "data": cached_data.insights}

        prompt = f"Industry Name: {industry_name}"
        response = create_and_run_perp_pipeline(
            system_prompt=PROMPT_INFORMATION_ABOUT_INDUSTY,
            parser=about_the_industry_parser,
            user_inp=prompt,
        )

        new_insight = models.IndustryInsights(industry_name=industry_name, insights=response)
        db.add(new_insight)
        await db.commit()

        return {"success": True, "message": "About industry data fetched successfully", "data": response}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}

async def get_company_metrics_internal(merchant_id: UUID, db: AsyncSession):
    """
    Get company metrics for a specific merchant
    """
    try:
        query = select(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id)
        result = await db.execute(query)
        merchant = result.scalars().first()
        
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")
        
        response = []

        metric = {
            "label": "Legal Name",
            "value": merchant.legal_name,
            "icon": "<Building2 className=\"h-5 w-5 text-blue-500\" />"
        }
        response.append(metric)

        # E-filing status color based on status
        efiling_color = "text-green-500" if merchant.efiling_status == "ACTIVE" else "text-red-500"
        metric = {
            "label": "E-filing Status",
            "value": merchant.efiling_status,
            "icon": f"<FileCheck className=\"h-5 w-5 {efiling_color}\" />"
        }
        response.append(metric)

        # Active compliance color based on status
        compliance_color = "text-green-500" if merchant.active_compliance else "text-red-500"
        metric = {
            "label": "Active Compliance",
            "value": merchant.active_compliance,
            "icon": f"<ShieldCheck className=\"h-5 w-5 {compliance_color}\" />"
        }
        response.append(metric)

        metric = {
            "label": "Incorporation Date",
            "value": merchant.incorporation_date,
            "icon": "<Calendar className=\"h-5 w-5 text-blue-500\" />"
        }
        response.append(metric)
        
        registered_address = merchant.registered_address
        metric = {
            "label": "City",
            "value": registered_address['city'],
            "icon": "<MapPin className=\"h-5 w-5 text-blue-500\" />"
        }
        response.append(metric)

        query = select(models.probe_industry_segments).filter(models.probe_industry_segments.merchant_id == merchant_id)
        result = await db.execute(query)
        industry = result.scalars().first()
        
        if not industry:
            raise HTTPException(status_code=404, detail="Industry data not found")
        metric = {
            "label": "Industry",
            "value": industry.industry,
            "icon": "<Factory className=\"h-5 w-5 text-blue-500\" />"
        }
        response.append(metric)

        query = select(models.merchant_metrics).filter(
            models.merchant_metrics.merchant_id == merchant_id, 
            models.merchant_metrics.metric_type == "AGE_OF_BUSINESS_MONTHS"
        ).order_by(models.merchant_metrics.created_at.desc())
        result = await db.execute(query)
        merchant_metric = result.scalars().first()
        
        metric = {
            "label": "Business Age",
            "icon": "<Clock className=\"h-5 w-5 text-blue-500\" />"
        }
        if not merchant_metric:
            metric["value"] = None
        else:
            years = float(merchant_metric.metric_value)/12
            metric["value"] = f"{round(years, 1)} years"
            # Color based on business age
            metric["icon"] = f"<Clock className=\"h-5 w-5 {'text-green-500' if years > 5 else 'text-blue-500'}\" />"
        response.append(metric)

        query = select(models.probe_epfo_establishments).filter(
            models.probe_epfo_establishments.merchant_id == merchant_id
        ).order_by(models.probe_epfo_establishments.latest_date_of_credit.desc())
        result = await db.execute(query)
        employees = result.scalars().all()
        
        metric = {
            "label": "Employee Count",
            "icon": "<Users className=\"h-5 w-5 text-blue-500\" />"
        }
        if not employees:
            metric["value"] = None
        else:
            try:
                print(f"Debug - First employee record: {employees[0].__dict__}")
                print(f"Debug - latest_date_of_credit: {employees[0].latest_date_of_credit}")
                latest_year = employees[0].latest_date_of_credit.split("-")[0]
                employee_count = 0
                for employee in employees:
                    if employee.latest_date_of_credit.split("-")[0] == latest_year:
                        employee_count += employee.no_of_employees
                metric["value"] = f"{employee_count:,}"
                metric["icon"] = f"<Users className=\"h-5 w-5 {'text-green-500' if employee_count > 50 else 'text-blue-500'}\" />"
            except Exception as e:
                print(f"Error processing employee data: {str(e)}")
                metric["value"] = None
        response.append(metric)

        query = select(models.probe_financials).filter(
            models.probe_financials.merchant_id == merchant_id, 
            models.probe_financials.nature == "STANDALONE"
        ).order_by(models.probe_financials.year.desc())
        result = await db.execute(query)
        financials = result.scalars().first()
        
        metric = {
            "label": "Net Revenue",
            "icon": "<TrendingUp className=\"h-5 w-5 text-blue-500\" />"
        }
        if not financials:
            metric["value"] = None
        else:
            revenue = financials.net_revenue
            metric["value"] = format_indian_currency(revenue)
            # Color based on revenue
            metric["icon"] = f"<TrendingUp className=\"h-5 w-5 {'text-green-500' if revenue > 1000000 else 'text-blue-500'}\" />"
        response.append(metric)

        query = select(models.probe_financials).filter(
            models.probe_financials.merchant_id == merchant_id, 
            models.probe_financials.nature == "STANDALONE"
        ).order_by(models.probe_financials.year.desc())
        result = await db.execute(query)
        financials = result.scalars().first()
        
        metric = {
            "label": "Net Worth",
            "icon": "<Wallet className=\"h-5 w-5 text-blue-500\" />"
        }
        if not financials:
            metric["value"] = None
        else:
            networth = financials.given_assets_total - financials.total_current_liabilities - financials.total_non_current_liabilities
            metric["value"] = format_indian_currency(networth)
            # Color based on net worth
            metric["icon"] = f"<Wallet className=\"h-5 w-5 {'text-green-500' if networth > 0 else 'text-red-500'}\" />"
        response.append(metric)

        query = select(models.merchant_metrics).filter(
            models.merchant_metrics.merchant_id == merchant_id, 
            models.merchant_metrics.metric_type == "AUDIT_QUALIFICATIONS_LAST_3_YEARS"
        ).order_by(models.merchant_metrics.created_at.desc())
        result = await db.execute(query)
        merchant_metric = result.scalars().first()
        
        metric = {
            "label": "Audit Qualifications (Past 3 Years)",
            "icon": "<FileWarning className=\"h-5 w-5 text-blue-500\" />"
        }
        if not merchant_metric:
            metric["value"] = None
        else:
            value = float(merchant_metric.metric_value)
            metric["value"] = value
            # Color based on audit qualifications
            metric["icon"] = f"<FileWarning className=\"h-5 w-5 {'text-red-500' if value > 0 else 'text-green-500'}\" />"
        response.append(metric)

        query = select(models.merchant_metrics).filter(
            models.merchant_metrics.merchant_id == merchant_id, 
            models.merchant_metrics.metric_type == "TOTAL_INSOLVENCY_LITIGATIONS_FILED_PENDING"
        ).order_by(models.merchant_metrics.created_at.desc())
        result = await db.execute(query)
        merchant_metric = result.scalars().first()
        
        metric = {
            "label": "Pending Litigations (Insolvency)",
            "icon": "<AlertTriangle className=\"h-5 w-5 text-blue-500\" />"
        }
        if not merchant_metric:
            metric["value"] = None
        else:
            value = float(merchant_metric.metric_value)
            metric["value"] = value
            # Color based on litigations
            metric["icon"] = f"<AlertTriangle className=\"h-5 w-5 {'text-red-500' if value > 0 else 'text-green-500'}\" />"
        response.append(metric)

        return {
            "success": True,
            "message": "Company metrics fetched successfully",
            "data": response
        }
    except Exception as e:
        print("Error in get_company_metrics:", str(e))
        return {
            "success": False,
            "message": str(e),
            "data": None
        }

async def get_external_data_internal(merchant_id: UUID, product: str, db: AsyncSession):
    try:
        
        if product == "insolvency":
            query = select(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id)
            result = await db.execute(query)
            merchant = result.scalars().first()
        elif product == "ipo":
            query = select(models.ListingCompanies).filter(
                models.ListingCompanies.id == merchant_id
            )
            result = await db.execute(query)
            merchant = result.scalars().first()
        else:
            raise HTTPException(status_code=400, detail="Invalid product type")
        
        if not merchant:
            return {"success": False, "message": "Merchant not found", "data": None}
        
        query = select(models.ExternalInsights).filter(
            models.ExternalInsights.merchant_id == merchant_id,
            models.ExternalInsights.insight_type == "external_data",
            models.ExternalInsights.product == product
        ).order_by(models.ExternalInsights.created_at.desc())
        result = await db.execute(query)
        external_data = result.scalars().all()

        if not external_data:
            return {"success": False, "message": "External data not found", "data": None}

        return {"success": True, "message": "External data fetched successfully", "data": external_data}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}

async def get_flags_from_auditor_disclosures_internal(merchant_id: UUID, db: AsyncSession):
    try:
        query = select(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id)
        result = await db.execute(query)
        merchant = result.scalars().first()
        
        if not merchant:
            return {"success": False, "message": "Merchant not found", "data": None}
        
        query = select(models.ExternalInsights).filter(
            models.ExternalInsights.merchant_id == merchant_id,
            models.ExternalInsights.insight_type == "audit_flags"
        ).order_by(models.ExternalInsights.created_at.desc()).limit(3)
        result = await db.execute(query)
        flags = result.scalars().all()
        
        if not flags:
            return {"success": True, "message": "No flags found", "data": None}

        return {"success": True, "message": "Flags fetched successfully", "data": flags}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}

async def get_annual_report_insights_internal(merchant_id: UUID, db: AsyncSession):
    try:
        query = select(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id)
        result = await db.execute(query)
        merchant = result.scalars().first()
        
        if not merchant:
            return {"success": False, "message": "Merchant not found", "data": None}
        
        query = select(models.ExternalInsights).filter(
            models.ExternalInsights.merchant_id == merchant_id,
            models.ExternalInsights.insight_type == "annual_report_insights"
        ).order_by(models.ExternalInsights.created_at.desc())
        result = await db.execute(query)
        insights = result.scalars().all()
        
        if not insights:
            return {"success": True, "message": "No insights found", "data": None}

        return {"success": True, "message": "Annual report insights fetched successfully", "data": insights}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}

async def get_risk_metrics_internal(merchant_id: UUID, db: AsyncSession):
    try:
        query = select(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id)
        result = await db.execute(query)
        merchant = result.scalars().first()
        
        if not merchant:
            return {"success": False, "message": "Merchant not found", "data": None}

        query = select(models.merchant_metrics).filter(
            models.merchant_metrics.merchant_id == merchant_id,
            models.merchant_metrics.metric_type == "RISK_SEGMENTATION"
        ).order_by(models.merchant_metrics.year.desc())
        result = await db.execute(query)
        risk_seg = result.scalars().first()

        query = select(models.merchant_metrics).filter(
            models.merchant_metrics.merchant_id == merchant_id,
            models.merchant_metrics.metric_type == "RECOVERY_RATE_ON_DEFAULT"
        ).order_by(models.merchant_metrics.year.desc())
        result = await db.execute(query)
        lgd = result.scalars().first()

        query = select(models.merchant_metrics).filter(
            models.merchant_metrics.merchant_id == merchant_id,
            models.merchant_metrics.metric_type == "EXPOSURE_AT_DEFAULT"
        ).order_by(models.merchant_metrics.year.desc())
        result = await db.execute(query)
        ead = result.scalars().first()

        query = select(models.merchant_metrics).filter(
            models.merchant_metrics.merchant_id == merchant_id,
            models.merchant_metrics.metric_type == "PD_SCORE"
        ).order_by(models.merchant_metrics.year.desc())
        result = await db.execute(query)
        pd_score = result.scalars().first()

        lgd_value = 1 - float(lgd.metric_value) if lgd else 0
        ead_value = float(ead.metric_value) if ead else 0
        pd_value = float(pd_score.metric_value) if pd_score else 0

        eld = lgd_value * ead_value
        el = pd_value * lgd_value * ead_value

        query = select(models.merchant_metrics).filter(
            models.merchant_metrics.merchant_id == merchant_id,
            models.merchant_metrics.metric_type == "UNEARNED_REVENUE"
        ).order_by(models.merchant_metrics.year.desc())
        result = await db.execute(query)
        unearned_revenue = result.scalars().first()

        query = select(models.probe_financials).filter(
            models.probe_financials.merchant_id == merchant_id
        ).order_by(models.probe_financials.year.desc())
        result = await db.execute(query)
        net_revenue = result.scalars().first()

        unearned_revenue_value = float(unearned_revenue.metric_value) if unearned_revenue else 0
        net_revenue_value = float(net_revenue.net_revenue) if net_revenue and net_revenue.net_revenue else 0
        add = (unearned_revenue_value / net_revenue_value * 365) if net_revenue_value != 0 else 0

        response = {
            "risk_segmentation": risk_seg.metric_value if risk_seg else None,
            "loss_given_default": round(lgd_value, 2),
            "exposure_at_default": round(ead_value, 2),
            "expected_loss_at_default": round(eld, 2),
            "expected_loss": round(el, 2),
            "average_days_deferred": round(add, 2),
            "pd_score": round(pd_value, 2)
        }

        return {"success": True, "message": "Risk metrics fetched successfully", "data": response}
    except Exception as e:
        return {"success": False, "message": str(e), "data": None}