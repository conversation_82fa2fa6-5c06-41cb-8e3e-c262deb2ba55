from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select 
from sqlalchemy.exc import SQLAlchemyError # Added import
from sqlalchemy.orm.attributes import flag_modified # Added import

from uuid import UUID, uuid4
from zoneinfo import ZoneInfo
import asyncio
from decimal import Decimal
from datetime import date, datetime

from ..models import models
from ..database import get_db
from .apiProtection import get_current_user
from .ares.modus_agent.get_db import get_db_async
from .ares.modus_agent.modus_agent import workflow
from .ares.modus_agent.modus_agent_edit_vis import workflow as edit_vis_workflow
from .ares.modus_agent.html_utils import main as generate_html_report
from langchain_core.messages import HumanMessage, AIMessage

import json
from pydantic import BaseModel

from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"

router = APIRouter()

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        if isinstance(obj, UUID):
            return str(obj)
        return super().default(obj)

class NewChatRequest(BaseModel):
    merchant_id: str
    has_visualization: bool
    visualization_id: str | None = None

class ChatMessage(BaseModel):
    chat_id: str
    message: str


@router.post("/new-chat")
async def new_chat(
    request: NewChatRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    if request.has_visualization and not request.visualization_id:
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "success": False,
                "message": "Visualization ID is required when has_visualization is true"
            }
        )
    
    if not request.has_visualization:
        request.visualization_id = None
    
    """Create a new chat"""
    chat_id = str(uuid4())

    # Create new active chat record
    new_chat = models.ActiveChatIds(
        chat_id=chat_id,
        user_id=str(current_user.id),
        merchant_id=request.merchant_id,
        has_visualization=request.has_visualization,
        visualization_id=request.visualization_id,
        created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
        updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
    )
    db.add(new_chat)
    db.commit()
    db.refresh(new_chat)  # Refresh to get the latest state

    # if it has visualization, create a new chat with two messages preinserted
    if request.has_visualization:
        first_message = models.ChatMessages(
            message_id=str(uuid4()),
            chat_id=chat_id,
            merchant_id=request.merchant_id,
            user_id=str(current_user.id),
            visualization=False,
            visualization_id=None,
            sender="user",
            message=json.dumps({"message": f"Editing visualization: {request.visualization_id}"}),
            status="recieved",
            steps=[],
            created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
            updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
            meta_data=None
        )

        # add title to the chat
        new_chat.chat_title = f"Editing Visualization: {request.visualization_id}"
        db.add(new_chat)
        db.commit()
        db.refresh(new_chat)  # Refresh to get the latest state

        second_message = models.ChatMessages(
            message_id=str(uuid4()),
            chat_id=chat_id,
            merchant_id=request.merchant_id,
            user_id=str(current_user.id),
            visualization=False,
            visualization_id=None,
            sender="assistant",
            message=json.dumps({"message": "I am happy to help you with editing this visualization. Please provide the details of the changes you want to make."}),
            status="completed",
            steps=[],
            created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
            updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
            meta_data=None
        )
        db.add(first_message)
        db.add(second_message)
        db.commit()
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "success": True,
            "message": "New chat created",
            "data": {
                "chat_id": chat_id,
            }
        }
    )

@router.get("/{chat_id}/delete-chat")
async def delete_chat(
    chat_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Delete a chat by chat_id"""
    try:
        # Get the chat record
        chat_record = db.query(models.ActiveChatIds).filter(models.ActiveChatIds.chat_id == chat_id).first()

        if not chat_record:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "Chat not found"
                }
            )

        # Delete the chat record
        db.delete(chat_record)
        db.commit()

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Chat deleted successfully"
            }
        )
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in delete_chat for chat_id {chat_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
    except Exception as e:
        print(f"Unexpected error in delete_chat for chat_id {chat_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{merchant_id}/get-active-chats")
async def get_active_chats(
    merchant_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Get all active chats for a merchant"""
    try:
        # Get all active chat records for the merchant
        active_chats = db.query(models.ActiveChatIds).filter(models.ActiveChatIds.merchant_id == merchant_id).order_by(models.ActiveChatIds.updated_at.desc()).all()

        if not active_chats:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "No active chats found for this merchant"
                }
            )

        # Convert to list of dictionaries
        chat_list = []
        for chat in active_chats:
            chat_list.append({
                "chat_id": chat.chat_id,
                "user_id": chat.user_id,
                "merchant_id": chat.merchant_id,
                "chat_title": chat.chat_title,
                "has_visualization": chat.has_visualization,
                "has_report": chat.has_report,
                "created_at": chat.created_at.isoformat(),
                "updated_at": chat.updated_at.isoformat()
            })

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Active chats retrieved successfully",
                "data": chat_list
            }
        )
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in get_active_chats for merchant_id {merchant_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
    except Exception as e:
        print(f"Unexpected error in get_active_chats for merchant_id {merchant_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{chat_id}/chatHistory")
async def get_chat_history(
    chat_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Get chat history for a specific chat"""
    try:

        # Get the chat record
        chat_record = db.query(models.ActiveChatIds).filter(models.ActiveChatIds.chat_id == chat_id).first()
        if not chat_record:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "Chat not found"
                }
            )
        # Get all messages for the chat
        chat_messages = db.query(models.ChatMessages).filter(models.ChatMessages.chat_id == chat_id).order_by(models.ChatMessages.created_at.asc()).all()

        # Convert to list of dictionaries
        message_list = []
        for message in chat_messages:
            message_list.append({
                "message_id": message.message_id,
                "chat_id": message.chat_id,
                "merchant_id": message.merchant_id,
                "user_id": message.user_id,
                "visualization": message.visualization,
                "visualization_id": message.visualization_id,
                "sender": message.sender,
                "message": json.loads(message.message),
                "status": message.status,
                "created_at": message.created_at.isoformat(),
                "updated_at": message.updated_at.isoformat(),
                "meta_data": json.loads(message.meta_data) if message.meta_data else None
            })

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Chat history retrieved successfully",
                "data": message_list
            }
        )
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in get_chat_history for chat_id {chat_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
    except Exception as e:
        print(f"Unexpected error in get_chat_history for chat_id {chat_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{message_id}/getStepUdates")
async def get_step_updates(
    message_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Get step updates for a specific message"""
    try:
        # Get the message from the database
        message = db.query(models.ChatMessages).filter(models.ChatMessages.message_id == message_id).first()

        if not message:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "Message not found"
                }
            )
        if message.status == "completed":
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "Message updates retrieved successfully",
                    "data": {
                        "steps": message.steps[-1],
                        "current_status": message.status,
                        "message": json.loads(message.message)
                    }
                }
            )
        else:
            return JSONResponse(
                status_code=status.HTTP_201_CREATED,
                content={
                    "success": True,
                    "message": "Message is still processing",
                    "data": {
                        "steps": message.steps[-1],
                        "current_status": message.status
                    }
                }
            )
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in get_step_updates for message_id {message_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
    except Exception as e:
        print(f"Unexpected error in get_step_updates for message_id {message_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
@router.get("/{chat_id}/get-report-pdf")
async def get_report_pdf(
    chat_id: str,
    current_user: models.User = Depends(get_current_user)
):
    # get all the messages from the chat ordered by created_at in descending order
    db: AsyncSession | None = None
    async_session_gen = None
    try:
        # Acquire a new database session for this operation
        async_session_gen = get_db_async()
        db = await async_session_gen.__anext__()  # type: ignore # Get the actual session from the generator

        chat_message_query = select(models.ChatMessages).where(
            models.ChatMessages.chat_id == chat_id,
            models.ChatMessages.visualization == False,  # Only get messages that are not visualizations
            models.ChatMessages.status == "completed"  # Only get completed messages
        ).order_by(models.ChatMessages.created_at.desc())
        chat_message_result = await db.execute(chat_message_query)
        chat_messages = chat_message_result.scalars().all()

        if not chat_messages:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "No report found for this chat",
                }
            )
        
        # get the last message in the chat that has a report key
        last_message = None
        for message in chat_messages:
            message_content = json.loads(message.message)
            if "report" in message_content:
                last_message = message
                break

        # now we convert this report into a html template and then covert it to pdf and save it
        if not last_message:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "No report found for this chat",
                }
            )
        
        report_content = json.loads(last_message.message).get("report", None)
        if not report_content:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "No report found for this chat",
                }
            )
        
        # Generate the HTML report
        presigned_url = generate_html_report(report_content)
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Report retrieved successfully",
                "data": {
                    "output_path": presigned_url,
                }
            }
        )
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in get_report_pdf for chat_id {chat_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
    except Exception as e:
        print(f"Unexpected error in get_report_pdf for chat_id {chat_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
    finally:
        # Ensure the async session is closed properly
        if db:
            try:
                await db.close()
            except Exception as e:
                print(f"Error closing async session in get_report_pdf for chat_id {chat_id}: {e}")

async def generate_response(
    message_id: str,
    reply_message_id: str
):
    """This function will compile the agent and generate a response for the chat message."""
    async_session_gen = None
    db: AsyncSession | None = None
    try:
        # Acquire a new database session for this background task
        async_session_gen = get_db_async()
        db = await async_session_gen.__anext__() # type: ignore # Get the actual session from the generator

        if not db:
            print(f"Error: Failed to acquire DB session in generate_response for message_id {message_id}")
            return

        # Get the chat message from the database
        chat_message_result = await db.execute(
            select(models.ChatMessages).where(models.ChatMessages.message_id == message_id)
        )
        message = chat_message_result.scalars().first()
        
        if not message:
            print(f"Message with ID {message_id} not found in the database (within generate_response).")
            return

        message_content = message.message
        chat_id = message.chat_id
        merchant_id = message.merchant_id
        user_id = message.user_id

        # get reply message from the database
        reply_message_result = await db.execute(
            select(models.ChatMessages).where(models.ChatMessages.message_id == reply_message_id)
        )
        reply_message = reply_message_result.scalars().first()

        async with AsyncPostgresSaver.from_conn_string(DATABASE_URL) as checkpointer:
            # await checkpointer.setup()
            agent = workflow.compile(checkpointer=checkpointer)

            inputs = {"messages": [HumanMessage(content=message_content)], "merchant_id": merchant_id, "chat_id": chat_id}

            print(f"\\nThinking... (for message_id: {message_id}, chat_id: {chat_id})")
            # last_ai_message_content = None # This variable is not used
            config = {
                "configurable": {
                    "thread_id": chat_id
                }
            }
            # Stream through the agent's execution
            async for event in agent.astream(inputs, config=config):
                for node_name, node_data in event.items():  
                    if node_name == "check_relevance":  
                        print(f"Checking relevance of the query... (chat_id: {chat_id})")
                        step = {
                            "stage": "check_relevance",
                            "status": "completed",
                            "message": "Relevance check completed successfully."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()

                        if node_data.get("is_relevant", False):
                            step = {
                                "stage": "context_check",
                                "status": "loading",
                                "message": "Checking context of the query..."
                            }
                            reply_message.steps.append(step)
                            flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                            reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                            db.add(reply_message)
                            await db.commit()
                    elif node_name == "context_check":
                        print(f"Checking context of the query... (chat_id: {chat_id})")
                        step = {
                            "stage": "context_check",
                            "status": "completed",
                            "message": "Context check completed successfully."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()
                        if node_data.get("in_context", False):
                            step = {
                                "stage": "improve_query",
                                "status": "loading",
                                "message": "Improving the query..."
                            }
                            reply_message.steps.append(step)
                            flag_modified(reply_message, "steps")
                            reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                            db.add(reply_message)
                            await db.commit()
                        print("node_data in context check: ", node_data)
                    elif node_name == "improve_query":
                        print(f"Improving the query... (chat_id: {chat_id})")
                        step = {
                            "stage": "improve_query",
                            "status": "completed",
                            "message": "Query improved successfully."
                        }
                        reply_message.steps.append(step)
                        step = {
                            "stage": "classify_query",
                            "status": "loading",
                            "message": "Classifying the query..."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()
                    elif node_name == "classify_query":
                        print(f"Classifying the query... (chat_id: {chat_id})")
                        step = {
                            "stage": "classify_query",
                            "status": "completed",
                            "message": "Query classified successfully."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()

                        # Check the classification result and route accordingly
                        classification = node_data.get("classification", "NONE")
                        if classification == "LLM_QUERY":
                            step = {
                                "stage": "llm_query",
                                "status": "loading",
                                "message": "Generating answer for your question..."
                            }
                        elif classification == "DB_QUERY":
                            step = {
                                "stage": "db_query",
                                "status": "loading",
                                "message": "Querying the database..."
                            }
                        elif classification == "VISUALIZATION_QUERY":
                            step = {
                                "stage": "visualization_query",
                                "status": "loading",
                                "message": "Generating visualization..."
                            }
                        elif classification == "REPORT_QUERY":
                            step = {
                                "stage": "report_query",
                                "status": "loading",
                                "message": "Generating report..."
                            }
                        else:
                            step = {
                                "stage": "handle_ooc_messages",
                                "status": "loading",
                                "message": "Handling out-of-context messages..."
                            }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()
                    elif node_name == "llm_query":
                        print(f"Processing LLM query... (chat_id: {chat_id})")
                        step = {
                            "stage": "llm_query",
                            "status": "completed",
                            "message": "LLM query processed successfully."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()
                    elif node_name == "db_query":
                        print(f"Processing database query... (chat_id: {chat_id})")
                        step = {
                            "stage": "db_query",
                            "status": "completed",
                            "message": "Database query processed."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()                        
                    elif node_name == "visualization_query":
                        print(f"Processing visualization query... (chat_id: {chat_id})")
                        step = {
                            "stage": "visualization_query",
                            "status": "completed",
                            "message": "Visualization query processed successfully."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()
                    elif node_name == "report_query":
                        print(f"Processing report query... (chat_id: {chat_id})")
                        step = {
                            "stage": "report_query",
                            "status": "completed",
                            "message": "Report created successfully."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()

                        # mark the chatid with has_report as True
                        chat_record = await db.execute(
                            select(models.ActiveChatIds).where(models.ActiveChatIds.chat_id == chat_id)
                        )
                        chat_info = chat_record.scalars().first()
                        chat_info.has_report = True
                        db.add(chat_info)
                        await db.commit()
                    elif node_name == "__end__":
                        print(f"End of the agent's execution. (chat_id: {chat_id})")
                        print("END MESSAGE: ", node_data)
            
            history_iterator = agent.aget_state_history(config) 
            full_final_ai_message = None
            
            reply_message_msg = {}
            # Iterate over the async iterator
            classification = None
            async for state in history_iterator: 
                state_dict = state.values
                if not state_dict:
                    continue
                if "sources" in state_dict and state_dict["sources"]:
                    reply_message_msg["sources"] = state_dict["sources"]
                if "classification" in state_dict and state_dict["classification"] and state_dict["classification"] == "REPORT_QUERY":
                    reply_message_msg["report"] = state_dict["report"]
                if "code_status" in state_dict and state_dict["code_status"]:
                    reply_message_msg["code_status"] = state_dict["code_status"]
                    reply_message_msg["code"] = state_dict.get("code", "")
                    reply_message_msg["data"] = state_dict.get("data", [])
                    reply_message_msg["vis_specs"] = state_dict.get("vis_specs", {})
                if "classification" in state_dict and state_dict["classification"]:
                    reply_message_msg["type"] = state_dict["classification"]
                    classification = state_dict["classification"]
                if isinstance(state_dict, dict) and "messages" in state_dict:
                    messages = state_dict["messages"]
                    for message in reversed(messages): # Iterate in reverse to find the latest AI message
                        if isinstance(message, AIMessage):
                            full_final_ai_message = message.content
                            reply_message_msg["message"] = full_final_ai_message
                            break 
                if full_final_ai_message:
                    break
            
            if not full_final_ai_message:
                full_final_ai_message = "Sorry, I couldn't generate a response for your query at this time. Please try again later."
                reply_message_msg["message"] = full_final_ai_message
                reply_message.message = json.dumps(reply_message_msg, ensure_ascii=False, cls=CustomJSONEncoder)  # Use custom encoder to handle Decimal and datetime
                reply_message.status = "completed"
                reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                db.add(reply_message)
                await db.commit()
            else:
                reply_message.message = json.dumps(reply_message_msg, ensure_ascii=False, cls=CustomJSONEncoder)  # Use custom encoder to handle Decimal and datetime
                reply_message.status = "completed"
                reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
                db.add(reply_message)
                await db.commit()

                # Now create a new graph spec object
                if classification == "VISUALIZATION_QUERY" and reply_message_msg.get("code_status") == "success":
                    code = reply_message_msg.get("code", "")
                    graph_spec = reply_message.get("vis_specs", {})
                    graph_spec = models.GraphSpecs(
                        visualization_id=reply_message_id,
                        dashboard_id=None,  # Assuming no dashboard ID is provided
                        chat_id=chat_id,
                        title=graph_spec.get("title", "Generated Visualization"),
                        description=graph_spec.get("description", "This visualization was generated based on the chat query."),
                        config=json.dumps(graph_spec),
                        code_type="query",
                        query=json.dumps(code),
                        code=None,
                        created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
                        created_by=user_id,  # Assuming user_id is the creator
                        updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
                        updated_by=user_id  # Assuming user_id is the updater
                    )
                    db.add(graph_spec)
                    await db.commit()

                    reply_message.visualization = True
                    reply_message.visualization_id = reply_message_id
                    db.add(reply_message)
                    await db.commit()
        return
    except SQLAlchemyError as e:
        sync_db = next(get_db())
        reply_message = sync_db.query(models.ChatMessages).filter(models.ChatMessages.message_id == reply_message_id).first()
        if reply_message:
            reply_message.status = "completed"
            reply_message.message = json.dumps({"message": "An error occurred while processing your request. Please try again later.", "type": "error", "error": str(e)})
            reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
            sync_db.add(reply_message)
            sync_db.commit()
            sync_db.refresh(reply_message)  # Refresh to get the latest state
        print(f"SQLAlchemyError in generate_response for message_id {message_id}: {e}")
        # Potentially update message status to 'error' here
    except Exception as e:
        sync_db = next(get_db())
        reply_message = sync_db.query(models.ChatMessages).filter(models.ChatMessages.message_id == reply_message_id).first()
        if reply_message:
            reply_message.status = "completed"
            reply_message.message = json.dumps({"message": "An unexpected error occurred while processing your request. Please try again later.", "type": "error", "error": str(e)})
            reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
            sync_db.add(reply_message)
            sync_db.commit()
            sync_db.refresh(reply_message)
        print(f"Unexpected error in generate_response for message_id {message_id}: {e}")
        # Potentially update message status to 'error' here
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose() # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in generate_response for message_id {message_id}: {e}")
    

@router.post("/initiate-chat")
async def initiate_chat(
    chat_message: ChatMessage,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    try:
        chat_id = chat_message.chat_id
        message = chat_message.message

        # get the active chat record
        active_chat = await db.execute(
            select(models.ActiveChatIds).where(models.ActiveChatIds.chat_id == chat_id)
        )
        chat_info = active_chat.scalars().first()
        if not chat_info:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "Chat not found",
                    "data": None
                }
            )
        
        # if no chat title then set it to the first message
        if not chat_info.chat_title:
            chat_info.chat_title = message[:50]
            db.add(chat_info)
            await db.commit()
        
        message_id = str(uuid4())
        reply_message_id = str(uuid4())
        merchant_id = chat_info.merchant_id
        user_id = chat_info.user_id

        # insert the user message into the chat 
        new_message = models.ChatMessages(
            message_id=message_id,
            chat_id=chat_id,
            merchant_id=merchant_id,
            user_id=user_id,
            visualization=False,
            visualization_id=None,
            sender="user",
            message=json.dumps({"message": message}),
            status="recieved",
            steps=[],
            created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None), # Made datetime offset-naive
            updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None), # Made datetime offset-naive
            meta_data=None
        )

        # insert the message into the database asynchronously
        db.add(new_message)
        await db.commit()
        print(f"Message {message_id} added to the database")

        # insert a reply message with the status processing
        reply_message = models.ChatMessages(
            message_id=reply_message_id,
            chat_id=chat_id,
            merchant_id=merchant_id,
            user_id=user_id,
            visualization=None,
            visualization_id=None,
            sender="assistant",
            message=json.dumps({"message": "Processing your request..."}),
            status="processing",
            steps=[],
            created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None), # Made datetime offset-naive
            updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None), # Made datetime offset-naive
            meta_data=None
        )
        db.add(reply_message)
        await db.commit()
        # Run the agent as a background task
        asyncio.create_task(generate_response(message_id, reply_message_id)) # Pass only message_id

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "message": "Chat initiated successfully",
                "data": {
                    "message_id": reply_message_id,
                    }
            }
        )
    except Exception as e:
        print(f"Error initiating chat: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
async def edit_visual(
    message_id: str,
    reply_message_id: str,
    configs: dict,
    data: list,
    code: str
):
    """This function will compile the agent and make changes to the visualization."""
    async_session_gen = None
    db: AsyncSession | None = None
    try:
        # Acquire a new database session for this background task
        async_session_gen = get_db_async()
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        if not db:
            print(f"Error: Failed to acquire DB session in edit_visual for message_id {message_id}")
            return

        # Get the chat message from the database
        chat_message_result = await db.execute(
            select(models.ChatMessages).where(models.ChatMessages.message_id == message_id)
        )
        message = chat_message_result.scalars().first()
        
        if not message:
            print(f"Message with ID {message_id} not found in the database (within edit_visual).")
            return

        chat_id = message.chat_id

        # get reply message from the database
        reply_message_result = await db.execute(
            select(models.ChatMessages).where(models.ChatMessages.message_id == reply_message_id)
        )
        reply_message = reply_message_result.scalars().first()

        # update the steps in the reply message
        step = {
            "stage": "identify_relevance",
            "status": "loading",
            "message": "Checking the relevance..."
        }
        reply_message.steps.append(step)
        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
        db.add(reply_message)
        await db.commit()

        async with AsyncPostgresSaver.from_conn_string(DATABASE_URL) as checkpointer:
            # await checkpointer.setup()
            agent = edit_vis_workflow.compile(checkpointer=checkpointer)

            inputs = {
                "messages": [HumanMessage(content=message.message)],
                "vis_specs": configs,
                "data": data[:5],
                "existing_query": code
            }
            print(f"\\nThinking... (for message_id: {message_id}, chat_id: {chat_id})")
            config = {
                "configurable": {
                    "thread_id": chat_id
                }
            }
            # Stream through the agent's execution
            async for event in agent.astream(inputs, config=config):
                for node_name, node_data in event.items():
                    if node_name == "identify_relevance":
                        print(f"Checking relevance of the query... (chat_id: {chat_id})")
                        step = {
                            "stage": "identify_relevance",
                            "status": "completed",
                            "message": "Relevance check completed successfully."
                        }
                        reply_message.steps.append(step)
                        step = {
                            "stage": "style_or_data",
                            "status": "loading",
                            "message": "Identifying if the query is related to style or data..."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()
                    elif node_name == "style_or_data":
                        print(f"Identifying if the query is related to style or data... (chat_id: {chat_id})")
                        step = {
                            "stage": "style_or_data",
                            "status": "completed",
                            "message": "Style or data identification completed successfully."
                        }
                        reply_message.steps.append(step)
                        if not node_data.get("is_data", False):
                            step = {
                                "stage": "handle_style",
                                "status": "loading",
                                "message": "Making requested changes to style..."
                            }
                        else:
                            step = {
                                "stage": "extracting_schema",
                                "status": "loading",
                                "message": "Extracting schema from the database..."
                            }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()
                    elif node_name == "handle_style":
                        print(f"Made requested changes to style... (chat_id: {chat_id})")
                        step = {
                            "stage": "handle_style",
                            "status": "completed",
                            "message": "Style changes made successfully."
                        }
                        reply_message.steps.append(step)
                        step = {
                            "stage": "handle_vis_end",
                            "status": "loading",
                            "message": "Finalizing the visualization..."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")  # Flag the steps attribute as modified
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()
                    elif node_name == "handle_vis_end_vis_edit":
                        print(f"Finalizing the visualization... (chat_id: {chat_id})")
                        step = {
                            "stage": "handle_vis_end",
                            "status": "completed",
                            "message": "Visualization generated successfully."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()
                    elif node_name == "extract_schema_vis_edit":
                        print(f"Extracting schema from the database... (chat_id: {chat_id})")
                        step = {
                            "stage": "extracting_schema",
                            "status": "completed",
                            "message": "Schema extracted successfully."
                        }
                        reply_message.steps.append(step)
                        step = {
                            "stage": "extract_data",
                            "status": "loading",
                            "message": "Extracting data for visualization..."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")
                    elif node_name == "extract_data_vis_edit":
                        print(f"Extracting data for visualization... (chat_id: {chat_id})")
                        step = {
                            "stage": "extract_data",
                            "status": "completed",
                            "message": "Data extracted successfully."
                        }
                        reply_message.steps.append(step)
                        step = {
                            "stage": "visualization_query_vis_edit",
                            "status": "loading",
                            "message": "Generating visualization..."
                        }
                        reply_message.steps.append(step)
                        flag_modified(reply_message, "steps")
                        reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
                        db.add(reply_message)
                        await db.commit()

            history = agent.aget_state_history(config)
            full_final_ai_message = None
            reply_message_msg = {"type": "VISUALIZATION_QUERY"}

            # Iterate over the async iterator
            existing_data = None
            async for state in history:
                state_dict = state.values
                if not state_dict:
                    continue
                if "data" in state_dict and state_dict["data"]:
                    existing_data = state_dict["data"]
                if "new_query" in state_dict and state_dict["new_query"]:
                    reply_message_msg["query"] = state_dict["new_query"]
                if "new_data" in state_dict and state_dict["new_data"]:
                    reply_message_msg["data"] = state_dict["new_data"]
                if "new_specs" in state_dict and state_dict["new_specs"]:
                    reply_message_msg["config"] = state_dict["new_specs"]
                if isinstance(state_dict, dict) and "messages" in state_dict:
                    messages = state_dict["messages"]
                    for message in reversed(messages): # Iterate in reverse to find the latest AI message
                        if isinstance(message, AIMessage):
                            full_final_ai_message = message.content
                            reply_message_msg["message"] = full_final_ai_message
                            break 
                if full_final_ai_message:
                    break
        if not full_final_ai_message:
            full_final_ai_message = "Sorry, I couldn't generate a response for your query at this time. Please try again later."
            reply_message_msg["message"] = full_final_ai_message
            reply_message.message = json.dumps(reply_message_msg, ensure_ascii=False, cls=CustomJSONEncoder)
            reply_message.status = "completed"
            reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)  # Made datetime offset-naive
            db.add(reply_message)
            await db.commit()
        else:
            # see if the data is present in the reply_message_msg
            if "data" not in reply_message_msg:
                reply_message_msg["data"] = existing_data if existing_data else []
            reply_message.message = json.dumps(reply_message_msg, ensure_ascii=False, cls=CustomJSONEncoder)
            reply_message.status = "completed"
            reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
            db.add(reply_message)
            await db.commit()
    except SQLAlchemyError as e:
        sync_db = next(get_db())
        reply_message = sync_db.query(models.ChatMessages).filter(models.ChatMessages.message_id == reply_message_id).first()
        if reply_message:
            reply_message.status = "completed"
            reply_message.message = json.dumps({"message": "An error occurred while processing your request. Please try again later.", "type": "error", "error": str(e)})
            reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
            sync_db.add(reply_message)
            sync_db.commit()
            sync_db.refresh(reply_message)
        print(f"SQLAlchemyError in edit_visual for message_id {message_id}: {e}")
        # Potentially update message status to 'error' here
    except Exception as e:
        sync_db = next(get_db())
        reply_message = sync_db.query(models.ChatMessages).filter(models.ChatMessages.message_id == reply_message_id).first()
        if reply_message:
            reply_message.status = "completed"
            reply_message.message = json.dumps({"message": "An unexpected error occurred while processing your request. Please try again later.", "type": "error", "error": str(e)})
            reply_message.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
            sync_db.add(reply_message)
            sync_db.commit()
            sync_db.refresh(reply_message)
        print(f"Unexpected error in edit_visual for message_id {message_id}: {e}")
        # Potentially update message status to 'error' here
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()
            except Exception as e:
                print(f"Error closing session generator in edit_visual for message_id {message_id}: {e}")
    return
    
class EditVisualizationRequest(BaseModel):
    chat_id: str
    message: str
    configs: dict
    data: list
    code: str

@router.post("/edit-visualization/")
async def edit_visualization(
    request: EditVisualizationRequest,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    try:
        chat_id = request.chat_id
        message = request.message
        configs = request.configs
        data = request.data
        print("data type:", type(data))
        code = request.code

        # get the active chat record
        active_chat = await db.execute(
            select(models.ActiveChatIds).where(models.ActiveChatIds.chat_id == chat_id)
        )
        chat_info = active_chat.scalars().first()
        if not chat_info:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "Chat not found",
                    "data": None
                }
            )
        
        visualization_id = chat_info.visualization_id
        user_id = str(current_user.id)
        message_id = str(uuid4())
        reply_message_id = str(uuid4())

        # insert the user message into the chat
        new_message = models.ChatMessages(
            message_id=message_id,
            chat_id=chat_id,
            merchant_id=None,
            user_id=user_id,
            visualization=False,
            visualization_id=None,
            sender="user",
            message=json.dumps({"message": message}),
            status="recieved",
            steps=[],
            created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
            updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
            meta_data=None
        )

        # insert the message into the database asynchronously
        db.add(new_message)
        await db.commit()
        print(f"Message {message_id} added to the database")

        # insert a reply message with the status processing
        reply_message = models.ChatMessages(
            message_id=reply_message_id,
            chat_id=chat_id,
            merchant_id=None,
            user_id=user_id,
            visualization=False,
            visualization_id=None,
            sender="assistant",
            message=json.dumps({"message": "Processing your request..."}),
            status="processing",
            steps=[],
            created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
            updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),  # Made datetime offset-naive
            meta_data=None
        )

        db.add(reply_message)
        await db.commit()

        # Run the agent as a background task
        asyncio.create_task(edit_visual(
            message_id=message_id,
            reply_message_id=reply_message_id,
            configs=configs,
            data=data,
            code=code
        ))  # Pass only message_id
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "message": "Visualization edit initiated successfully",
                "data": {
                    "message_id": reply_message_id,
                }
            }
        )
    except Exception as e:
        print(f"Error editing visualization: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

