# This file will contain the functions to extract data from the pdf and populate into the database.
import json
import re
from .gptwrapper import <PERSON><PERSON><PERSON><PERSON>per
from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

llm = init_chat_model("openai:gpt-4.1-mini")

from ....ipo.models.listing_models import (
    ListingCompanies,
    IPOOfferings,
    PeerGroup,
    BookRunningLeadManagers,
    Registrar,
    LegalCounsel,
    StatutoryAuditor,
    IndependentChartedAccountant,
    Financials,
    ComplianceClauses,
    IssuerComplianceStatus
)
from ....ipo.models.document_models import (
    CompanyDocumentUploaded,
    CompanyDocumentRawData,
    CompanyDocumentProcessedData
)

from uuid import uuid4, UUID
from .utils import parse_llm_response, call_llm_with_retry
from .file_upload import load_json_from_s3
from .prompts_schema import *
from ....database import get_db_async
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, date
from sqlalchemy.exc import SQLAlchemyError

def parse_date(date_str):
    """Convert string date to Python date object if not None."""
    if not date_str:
        return None
    try:
        if isinstance(date_str, str):
            # If it's just a year, convert to January 1st of that year
            if date_str.isdigit() and len(date_str) == 4:
                return datetime(int(date_str), 1, 1).date()
                
            # Try common date formats
            formats = [
                "%Y-%m-%d",
                "%d-%m-%Y", 
                "%Y/%m/%d",
                "%d/%m/%Y"
            ]
            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt).date()
                except ValueError:
                    continue
        return date_str
    except Exception as e:
        print(f"Error parsing date {date_str}: {e}")
        return None

async def populate_company_info(doc_id: str):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        gpt = GPTWrapper(doc_id)
        response = gpt.chat(PROMPT_COMPANY_INFO)

        if not response:
            raise ValueError("Failed to get response from GPT for company info extraction")
        response_data = parse_llm_response(response)

        if not response_data:
            raise ValueError("Parsed response data is empty or invalid")

        cin_number = response_data.get("cin", "")
        if not cin_number:
            raise ValueError("CIN number is missing in the response data")

        # check if company already exists
        existing_company = await db.execute(
            select(ListingCompanies).where(ListingCompanies.cin == cin_number)
        )
        existing_company = existing_company.scalars().first()
        new_company = ListingCompanies(
            id=uuid4(),
            cin=response_data.get("cin", ""),
            legal_name=response_data.get("legal_name", ""),
            efiling_status=response_data.get("efiling_status", ""),
            incorporation_date=parse_date(response_data.get("incorporation_date")),
            paid_up_capital=response_data.get("paid_up_capital"),
            sum_of_charges=response_data.get("sum_of_charges"),
            authorized_capital=response_data.get("authorized_capital"),
            active_compliance=response_data.get("active_compliance"),
            registered_address=response_data.get("registered_address", {}),
            business_address=response_data.get("business_address", {}),
            pan=response_data.get("pan", ""),
            website=response_data.get("website", ""),
            classification=response_data.get("classification", ""),
            status=response_data.get("status", ""),
            last_agm_date=parse_date(response_data.get("last_agm_date")),
            last_filing_date=parse_date(response_data.get("last_filing_date")),
            email=response_data.get("email", ""),
            description=response_data.get("description", ""),
            contact_email=response_data.get("contact_email", ""),
            contact_phone=response_data.get("contact_phone", ""),
            lei_number=response_data.get("lei_number", ""),
            lei_status=response_data.get("lei_status", ""),
            lei_registration_date=parse_date(response_data.get("lei_registration_date")),
            lei_last_updated_date=parse_date(response_data.get("lei_last_updated_date")),
            lei_next_renewal_date=parse_date(response_data.get("lei_next_renewal_date")),
            name_history=response_data.get("name_history", []),
            last_updated_date=response_data.get("last_updated_date"),
            about_company=response_data.get("about_company", ""),
            about_industry=response_data.get("about_industry", "")
        )
        if existing_company:
            print(f"Company with CIN {cin_number} already exists, updating existing record.")
            new_company.id = existing_company.id  # Use existing ID
            db.merge(new_company)
        else:
            print(f"Creating new company record with CIN {cin_number}.")
            db.add(new_company)
        await db.commit()
        print(f"Company data for CIN {cin_number} populated successfully.")
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in populating company data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in populating company data: {e}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose() # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in populating company data: {e}")

async def populate_ipo_offering(doc_id: str):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        # find company based on the doc_id
        document = await db.execute(
            select(CompanyDocumentUploaded).where(CompanyDocumentUploaded.id == doc_id)
        )
        document = document.scalars().first()
        if not document:
            raise ValueError(f"No document found with ID {doc_id}")
        
        company_id = document.company_id
        if not company_id:
            raise ValueError(f"Document with ID {doc_id} does not have a valid company_id")
        # get company using company_id
        company = await db.execute(
            select(ListingCompanies).where(ListingCompanies.id == company_id)
        )

        company = company.scalars().first()
        if not company:
            raise ValueError(f"No company found with ID {company_id}")
        
        gpt = GPTWrapper(doc_id, iterations=7)
        usser_query = f"""
        USER REQUEST: {PROMPT_IPO_OFFERING}
        """
        response = gpt.chat(usser_query)

        if not response:
            raise ValueError("Failed to get response from GPT for IPO offering extraction")
        response_data = parse_llm_response(response)

        if not response_data:
            raise ValueError("Parsed response data is empty or invalid")
        
        # populate IPO offering data
        offering = IPOOfferings(
            id=UUID(doc_id),
            company_id=UUID(company_id),  # or fetch the related ListingCompanies UUID if you have it, else keep None
            listing_date=parse_date(response_data.get("listing_date")),
            listing_type=response_data.get("listing_type"),
            offering_id=response_data.get("offering_id"),
            eligibility_type=response_data.get("eligibility_type"),
            document_type=response_data.get("document_type"),
            listing_details=response_data.get("listing_details", {}),
            offer_size=response_data.get("offer_size"),
            document_id=doc_id,
            data=response_data.get("data", {}),
        )

        # save response_data to a json file
        with open(f"{doc_id}_ipo_offering.json", "w") as f:
            json.dump(response_data, f, indent=4)
        # db.add(offering)
        # await db.commit()
        print(f"IPO offering data for document ID {doc_id} populated successfully.")
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in populating IPO offering data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in populating IPO offering data: {e}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()  # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in populating IPO offering data: {e}")   

async def populate_peer_groups(doc_id: str):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        # find company based on the doc_id
        document = await db.execute(
            select(CompanyDocumentUploaded).where(CompanyDocumentUploaded.id == doc_id)
        )
        document = document.scalars().first()
        if not document:
            raise ValueError(f"No document found with ID {doc_id}")
        company_id = document.company_id
        if not company_id:
            raise ValueError(f"Document with ID {doc_id} does not have a valid company_id")
        # get company using company_id
        company = await db.execute(
            select(ListingCompanies).where(ListingCompanies.id == company_id)
        )
        company = company.scalars().first()
        if not company:
            raise ValueError(f"No company found with ID {company_id}")
        
        gpt = GPTWrapper(doc_id)
        response = gpt.chat(PROMPT_PEER_GROUPS_INFO)

        if not response:
            raise ValueError("Failed to get response from GPT for peer groups extraction")
        response_data = parse_llm_response(response)

        if not response_data or not isinstance(response_data, list):
            raise ValueError("Parsed response data is empty or invalid")
        
        # populate peer group data
        for peer in response_data:
            if not isinstance(peer, dict):
                print(f"Skipping invalid peer data: {peer}")
                continue
            
            peer_data = PeerGroup(
                id=uuid4(),
                company_id=UUID(company_id),  # or fetch the related ListingCompanies UUID if you have it, else keep None
                listing_id=UUID(doc_id),  # Assuming doc_id is the listing ID
                is_self = peer.get("is_self", False),
                name = peer.get("name"),
                revenue_from_operations_in_million_inr = peer.get("revenue_from_operations_in_million_inr"),
                face_value_per_equity_share = peer.get("face_value_per_equity_share"),
                closing_price_inr = peer.get("closing_price_inr"),
                closing_price_date = parse_date(peer.get("closing_price_date")),
                pe_ratio = peer.get("pe_ratio"),
                eps_basic_inr = peer.get("eps_basic_inr"),
                eps_diluted_inr = peer.get("eps_diluted_inr"),
                ronw_percent = peer.get("ronw_percent"),
                nav_per_equity_share_inr = peer.get("nav_per_equity_share_inr")
            )
            if not peer_data.name:
                print(f"Skipping peer with missing name: {peer}")
                continue
            db.add(peer_data)
        await db.commit()
        # Commit the session to save all peer group data             
        print(f"Peer groups data for document ID {doc_id} populated successfully.")
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in populating peer groups data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in populating peer groups data: {e}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()  # Ensure the generator's finally block is     
            except Exception as e:
                print(f"Error closing session generator in populating peer groups data: {e}")

async def populate_book_running_lead_manger(doc_id: str):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        # find company based on the doc_id
        document = await db.execute(
            select(CompanyDocumentUploaded).where(CompanyDocumentUploaded.id == doc_id)
        )
        document = document.scalars().first()
        if not document:
            raise ValueError(f"No document found with ID {doc_id}")
        company_id = document.company_id
        if not company_id:
            raise ValueError(f"Document with ID {doc_id} does not have a valid company_id")
        # get company using company_id
        company = await db.execute(
            select(ListingCompanies).where(ListingCompanies.id == company_id)
        )
        company = company.scalars().first()
        if not company:
            raise ValueError(f"No company found with ID {company_id}")
        
        gpt = GPTWrapper(doc_id)
        response = gpt.chat(PROMPT_BOOKRUNNING_MANAGERS)

        if not response:
            raise ValueError("Failed to get response from GPT for peer groups extraction")
        response_data = parse_llm_response(response)

        if not response_data or not isinstance(response_data, list):
            raise ValueError("Parsed response data is empty or invalid")
        
        # populate book running lead managers data
        for manager in response_data:
            if not isinstance(manager, dict):
                print(f"Skipping invalid manager data: {manager}")
                continue
            
            manager_data = BookRunningLeadManagers(
                id=uuid4(),
                company_id=UUID(company_id),  # or fetch the related ListingCompanies UUID if you have it, else keep None
                listing_id=UUID(doc_id),  # Assuming doc_id is the listing ID
                name=manager.get("name"),
                unique_id=manager.get("unique_id"),
                address=manager.get("address", {}),
                mobile=manager.get("mobile"),
                email=manager.get("email"),
                grievance_email=manager.get("grievance_email"),
                website=manager.get("website"),
                contact_person=manager.get("contact_person", {}),
                sebi_registration_number=manager.get("sebi_registration_number"),
                logo = manager.get("logo"),
            )
            if not manager_data.name:
                print(f"Skipping manager with missing name: {manager}")
                continue
            db.add(manager_data)
        await db.commit()
        # Commit the session to save all book running lead managers data
        print(f"Book running lead managers data for document ID {doc_id} populated successfully.")
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in populating book running lead managers data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in populating book running lead managers data: {e}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()  # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in populating book running lead managers data: {e}")    

async def populate_registrar_table(doc_id: str):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        # find company based on the doc_id
        document = await db.execute(
            select(CompanyDocumentUploaded).where(CompanyDocumentUploaded.id == doc_id)
        )
        document = document.scalars().first()
        if not document:
            raise ValueError(f"No document found with ID {doc_id}")
        company_id = document.company_id
        if not company_id:
            raise ValueError(f"Document with ID {doc_id} does not have a valid company_id")
        # get company using company_id
        company = await db.execute(
            select(ListingCompanies).where(ListingCompanies.id == company_id)
        )
        company = company.scalars().first()
        if not company:
            raise ValueError(f"No company found with ID {company_id}")
        
        gpt = GPTWrapper(doc_id)
        response = gpt.chat(PROMPT_REGISTRAR)
        if not response:
            raise ValueError("Failed to get response from GPT for registrar extraction")
        response_data = parse_llm_response(response)
        if not response_data or not isinstance(response_data, list):
            raise ValueError("Parsed response data is empty or invalid")
        # populate registrar data
        for registrar in response_data:
            if not isinstance(registrar, dict):
                print(f"Skipping invalid registrar data: {registrar}")
                continue
            
            registrar_data = Registrar(
                id=uuid4(),
                company_id=UUID(company_id),  # or fetch the related ListingCompanies UUID if you have it, else keep None
                listing_id=UUID(doc_id),  # Assuming doc_id is the listing ID
                name=registrar.get("name"),
                unique_id=registrar.get("unique_id"),
                address=registrar.get("address", {}),
                mobile=registrar.get("mobile"),
                email=registrar.get("email"),
                grievance_email=registrar.get("grievance_email"),
                website=registrar.get("website"),
                contact_person=registrar.get("contact_person", {}),
                sebi_registration_number=registrar.get("sebi_registration_number"),
            )
            if not registrar_data.name:
                print(f"Skipping registrar with missing name: {registrar}")
                continue
            db.add(registrar_data)
        await db.commit()
        # Commit the session to save all registrar data
        print(f"Registrar data for document ID {doc_id} populated successfully.")
    except SQLAlchemyError as e:    
        print(f"SQLAlchemyError in populating registrar data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in populating registrar data: {e}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()  # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in populating registrar data: {e}")
            
async def populate_legal_counsel_table(doc_id: str):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

         # find company based on the doc_id
        document = await db.execute(
            select(CompanyDocumentUploaded).where(CompanyDocumentUploaded.id == doc_id)
        )
        document = document.scalars().first()
        if not document:
            raise ValueError(f"No document found with ID {doc_id}")
        company_id = document.company_id
        if not company_id:
            raise ValueError(f"Document with ID {doc_id} does not have a valid company_id")
        # get company using company_id
        company = await db.execute(
            select(ListingCompanies).where(ListingCompanies.id == company_id)
        )
        company = company.scalars().first()
        if not company:
            raise ValueError(f"No company found with ID {company_id}")
        
        gpt = GPTWrapper(doc_id)
        response = gpt.chat(PROMPT_LEGAL_COUNSEL)
        if not response:
            raise ValueError("Failed to get response from GPT for legal counsel extraction")
        response_data = parse_llm_response(response)
        if not response_data or not isinstance(response_data, list):
            raise ValueError("Parsed response data is empty or invalid")
        # populate legal counsel data
        for counsel in response_data:
            if not isinstance(counsel, dict):
                print(f"Skipping invalid legal counsel data: {counsel}")
                continue
            
            counsel_data = LegalCounsel(
                id=uuid4(),
                company_id=UUID(company_id),  # or fetch the related ListingCompanies UUID if you have it, else keep None
                listing_id=UUID(doc_id),  # Assuming doc_id is the listing ID
                type=counsel.get("type"),
                name=counsel.get("name"),
                unique_id=counsel.get("unique_id"),
                address=counsel.get("address", {}),
                mobile=counsel.get("mobile"),
                email=counsel.get("email"),
                grievance_email=counsel.get("grievance_email"),
                website=counsel.get("website"),
                contact_person=counsel.get("contact_person", {}),
                sebi_registration_number=counsel.get("sebi_registration_number"),
                logo=counsel.get("logo")
            )
            if not counsel_data.name:
                print(f"Skipping legal counsel with missing name: {counsel}")
                continue
            db.add(counsel_data)
        await db.commit()
        # Commit the session to save all legal counsel data
        print(f"Legal counsel data for document ID {doc_id} populated successfully.")
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in populating legal counsel data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in populating legal counsel data: {e}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()  # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in populating legal counsel data: {e}")

async def populate_statutory_auditor_table(doc_id: str):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

         # find company based on the doc_id
        document = await db.execute(
            select(CompanyDocumentUploaded).where(CompanyDocumentUploaded.id == doc_id)
        )
        document = document.scalars().first()
        if not document:
            raise ValueError(f"No document found with ID {doc_id}")
        company_id = document.company_id
        if not company_id:
            raise ValueError(f"Document with ID {doc_id} does not have a valid company_id")
        # get company using company_id
        company = await db.execute(
            select(ListingCompanies).where(ListingCompanies.id == company_id)
        )
        company = company.scalars().first()
        if not company:
            raise ValueError(f"No company found with ID {company_id}")
        
        gpt = GPTWrapper(doc_id)
        response = gpt.chat(PROMPT_STATUTORY_AUDITORS)
        if not response:
            raise ValueError("Failed to get response from GPT for statutory auditors extraction")
        response_data = parse_llm_response(response)
        if not response_data or not isinstance(response_data, list):
            raise ValueError("Parsed response data is empty or invalid")

        # populate statutory auditor data
        for auditor in response_data:
            if not isinstance(auditor, dict):
                print(f"Skipping invalid statutory auditor data: {auditor}")
                continue
            
            auditor_data = StatutoryAuditor(
                id=uuid4(),
                company_id=UUID(company_id),  # or fetch the related ListingCompanies UUID if you have it, else keep None
                listing_id=UUID(doc_id),  # Assuming doc_id is the listing ID
                name=auditor.get("name"),
                unique_id=auditor.get("unique_id"),
                address=auditor.get("address", {}),
                mobile=auditor.get("mobile"),
                email=auditor.get("email"),
                grievance_email=auditor.get("grievance_email"),
                website=auditor.get("website"),
                contact_person=auditor.get("contact_person", {}),
                sebi_registration_number=auditor.get("sebi_registration_number"),
                logo=auditor.get("logo")
            )
            if not auditor_data.name:
                print(f"Skipping statutory auditor with missing name: {auditor}")
                continue
            db.add(auditor_data) 
        await db.commit()
        # Commit the session to save all statutory auditor data
        print(f"Statutory auditor data for document ID {doc_id} populated successfully.")
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in populating statutory auditor data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in populating statutory auditor data: {e}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()  # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in populating statutory auditor data: {e}")

async def populate_chartered_accountant(doc_id: str):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

         # find company based on the doc_id
        document = await db.execute(
            select(CompanyDocumentUploaded).where(CompanyDocumentUploaded.id == doc_id)
        )
        document = document.scalars().first()
        if not document:
            raise ValueError(f"No document found with ID {doc_id}")
        company_id = document.company_id
        if not company_id:
            raise ValueError(f"Document with ID {doc_id} does not have a valid company_id")
        # get company using company_id
        company = await db.execute(
            select(ListingCompanies).where(ListingCompanies.id == company_id)
        )
        company = company.scalars().first()
        if not company:
            raise ValueError(f"No company found with ID {company_id}")
        
        gpt = GPTWrapper(doc_id)
        response = gpt.chat(PROMPT_CHARTERED_ACCOUNTANTS)
        if not response:
            raise ValueError("Failed to get response from GPT for chartered accountants extraction")
        response_data = parse_llm_response(response)
        if not response_data or not isinstance(response_data, list):
            raise ValueError("Parsed response data is empty or invalid")

        # populate statutory auditor data
        for accountant in response_data:
            if not isinstance(accountant, dict):
                print(f"Skipping invalid statutory auditor data: {accountant}")
                continue
            
            accountant_data = IndependentChartedAccountant(
                id=uuid4(),
                company_id=UUID(company_id),  # or fetch the related ListingCompanies UUID if you have it, else keep None
                listing_id=UUID(doc_id),  # Assuming doc_id is the listing ID
                name=accountant.get("name"),
                unique_id=accountant.get("unique_id"),
                address=accountant.get("address", {}),
                mobile=accountant.get("mobile"),
                email=accountant.get("email"),
                grievance_email=accountant.get("grievance_email"),
                website=accountant.get("website"),
                contact_person=accountant.get("contact_person", {}),
                sebi_registration_number=accountant.get("sebi_registration_number"),
                logo=accountant.get("logo")
            )
            if not accountant_data.name:
                print(f"Skipping statutory auditor with missing name: {accountant}")
                continue
            db.add(accountant_data)
        await db.commit()
        # Commit the session to save all statutory auditor data
        print(f"Independent Chartered Accountant data for document ID {doc_id} populated successfully.")
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in populating Chartered accountant data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in populating Chartered accountant data: {e}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()  # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in populating chartered accountant data: {e}")

# Populating financials table
async def populate_financials_data(doc_id: str):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        # find company based on the doc_id
        document = await db.execute(
            select(CompanyDocumentUploaded).where(CompanyDocumentUploaded.id == doc_id)
        )
        document = document.scalars().first()
        if not document:
            raise ValueError(f"No document found with ID {doc_id}")
        company_id = document.company_id

        # Get the raw data from the document
        raw_data = await db.execute(
            select(CompanyDocumentRawData).where(CompanyDocumentRawData.doc_id == doc_id).order_by(CompanyDocumentRawData.updated_at.desc())
        )
        raw_data = raw_data.scalars().first()
        if not raw_data:
            raise ValueError(f"No raw data found for doc id: {doc_id}")
        
        raw_data_loc = raw_data.raw_data
        raw_data_json = load_json_from_s3(raw_data_loc['bucket'], raw_data_loc['key'])
        if not raw_data_json:
            raise ValueError(f"No raw data was found on s3 for doc id: {doc_id}")
        
        # Get the index from the processed data
        processed_data = await db.execute(
            select(CompanyDocumentProcessedData).where(CompanyDocumentProcessedData.doc_id == doc_id).order_by(CompanyDocumentProcessedData.updated_at.desc())
        )
        processed_data = processed_data.scalars().first()
        if not processed_data:
            raise ValueError(f"No processed data is found on db for doc_id: {doc_id}")
        
        index_data = processed_data.table_of_contents
        if not index_data:
            raise ValueError(f"No index data found for doc_id: {doc_id}")
        
        print(index_data)

        # find the page number of start and end of subsection "SUMMARY OF RESTATED FINANCIAL INFORMATION"
        system_prompt = """Given the index or table of content of a pdf, identify the subsection whose title is most similary to 'SUMMARY OF RESTATED FINANCIAL INFORMATION' it is mostly located in the introduction secton. You only need to return the name of the subsection as it is and the page number on which this section starts and ends.
        
        Return the output in the following json format:
        ```json
        {{
            "name": "Exact name of the section as it is mentioned in the pdf table of content, only keep single space between words.
            "start_page": integer,
            "end_page": integer
        }}
        ```
        """
        user_message = f"""
        Table of content: 
        {index_data}
        """
        response = llm.invoke([
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_message)
        ])

        if not response:
            ValueError("gpt call failed to get finance summary section")

        print("response: ", response)

        response = parse_llm_response(response)
        subsection_name = response["name"]
        start_page = int(response["start_page"])
        end_page = int(response["end_page"])
        number_of_pages = end_page - start_page
        print("Number of pages: ", number_of_pages)

        matched_page_number = None
        first_match = None
        # search for the name inside the pages
        for page in raw_data_json["pages"]:
            page_text = str(page["md"])
            # replace more than once space with a single space everywhere
            page_text = re.sub(r'\s+', ' ', page_text)
            matched = page_text.find(subsection_name)
            if matched == -1:
                continue
            if matched and first_match == None:
                first_match = page["page"]
                continue
            if first_match and matched:
                matched_page_number = page["page"]
                break
        
        print("Second occurance found on page number: ", matched_page_number)

        financial_pages = [page for page in raw_data_json["pages"] if page["page"]>=matched_page_number and page["page"]<=matched_page_number + number_of_pages]

        # join the content of the pages with page number, section, subsection, and content
        financial_content = []
        for page in financial_pages:
            content = {
                "page_number": page["page"],
                "content": page["md"]
            }
            financial_content.append(content)
        # make a string from the financial_content
        financial_content_str = "\n".join([f"Page {item['page_number']}: {item['content']}" for item in financial_content])
        print("Financial content extracted successfully.")

        system_prompt = PROMPT_TABLE_TO_JSON
        user_message = f"""
        Markdown table content: {financial_content_str}
        """
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_message)
        ]
        response = await call_llm_with_retry(llm, messages)
        if not response:
            raise ValueError("Failed to get response from GPT for financial data extraction")
        response_data = parse_llm_response(response)

        #save the financial data to a json file
        if not response_data or not isinstance(response_data, dict):
            raise ValueError("Parsed response data is empty or invalid")
        
        with open(f"financial_data_{doc_id}.json", "w") as f:
            json.dump(response_data, f, indent=4)

        # now we will process data on yearly basis
        for year, year_data in response_data.items():
            print(f"Processing financial data for year: {year}")
            if not isinstance(year_data, dict):
                print(f"Skipping invalid year data: {year_data}")
                continue
                
            # get data to create a financial object
            system_prompt = PROMPT_FILING_FINANCIALS_YEAR
            user_message = f"""
            Year: {year}
            Yearly financial data: {year_data}
            """
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_message)
            ]
            response = await call_llm_with_retry(llm, messages)
            if not response:
                raise ValueError(f"Failed to get response from GPT for financial data extraction for year {year}")
            parsed_json = parse_llm_response(response)
            if not parsed_json or not isinstance(parsed_json, dict):
                raise ValueError(f"Parsed response data for year {year} is empty or invalid")
            
            # create a financial object
            financials = Financials(
                id=uuid4(),
                company_id=UUID(company_id), 
                listing_id=UUID(doc_id),  # Assuming doc_id is the listing ID 
                nature=parsed_json.get("nature"),
                financial_year=parse_date(year),  # Convert the year string to a date object
                stated_on=parse_date(parsed_json.get("stated_on")),
                filing_type=parsed_json.get("filing_type"),
                filing_standard=parsed_json.get("filing_standard"),
                tangible_assets=parsed_json.get("tangible_assets"),
                producing_properties=parsed_json.get("producing_properties"),
                intangible_assets=parsed_json.get("intangible_assets"),
                preproducing_properties=parsed_json.get("preproducing_properties"),
                tangible_assets_capital_work_in_progress=parsed_json.get("tangible_assets_capital_work_in_progress"),
                intangible_assets_under_development=parsed_json.get("intangible_assets_under_development"),
                noncurrent_investments=parsed_json.get("noncurrent_investments"),
                deferred_tax_assets_net=parsed_json.get("deferred_tax_assets_net"),
                foreign_curr_monetary_item_trans_diff_asset_account=parsed_json.get("foreign_curr_monetary_item_trans_diff_asset_account"),
                long_term_loans_and_advances=parsed_json.get("long_term_loans_and_advances"),
                other_noncurrent_assets=parsed_json.get("other_noncurrent_assets"),
                current_investments=parsed_json.get("current_investments"),
                inventories=parsed_json.get("inventories"),
                trade_receivables=parsed_json.get("trade_receivables"),
                cash_and_bank_balances=parsed_json.get("cash_and_bank_balances"),
                short_term_loans_and_advances=parsed_json.get("short_term_loans_and_advances"),
                other_current_assets=parsed_json.get("other_current_assets"),
                given_assets_total=parsed_json.get("given_assets_total"),
                share_capital=parsed_json.get("share_capital"),
                reserves_and_surplus=parsed_json.get("reserves_and_surplus"),
                money_received_against_share_warrants=parsed_json.get("money_received_against_share_warrants"),
                share_application_money_pending_allotment=parsed_json.get("share_application_money_pending_allotment"),
                deferred_government_grants=parsed_json.get("deferred_government_grants"),
                minority_interest=parsed_json.get("minority_interest"),
                long_term_borrowings=parsed_json.get("long_term_borrowings"),
                deferred_tax_liabilities_net=parsed_json.get("deferred_tax_liabilities_net"),
                foreign_curr_monetary_item_trans_diff_liability_account=parsed_json.get("foreign_curr_monetary_item_trans_diff_liability_account"),
                other_long_term_liabilities=parsed_json.get("other_long_term_liabilities"),
                long_term_provisions=parsed_json.get("long_term_provisions"),
                short_term_borrowings=parsed_json.get("short_term_borrowings"),
                trade_payables=parsed_json.get("trade_payables"),
                other_current_liabilities=parsed_json.get("other_current_liabilities"),
                short_term_provisions=parsed_json.get("short_term_provisions"),
                given_liabilities_total=parsed_json.get("given_liabilities_total"),
                total_equity=parsed_json.get("total_equity"),
                total_current_liabilities=parsed_json.get("total_current_liabilities"),
                total_non_current_liabilities=parsed_json.get("total_non_current_liabilities"),
                net_fixed_assets=parsed_json.get("net_fixed_assets"),
                total_current_assets=parsed_json.get("total_current_assets"),
                capital_wip=parsed_json.get("capital_wip"),
                total_debt=parsed_json.get("total_debt"),
                gross_fixed_assets=parsed_json.get("gross_fixed_assets"),
                trade_receivable_exceeding_six_months=parsed_json.get("trade_receivable_exceeding_six_months"),
                net_revenue=parsed_json.get("net_revenue"),
                total_cost_of_materials_consumed=parsed_json.get("total_cost_of_materials_consumed"),
                total_purchases_of_stock_in_trade=parsed_json.get("total_purchases_of_stock_in_trade"),
                total_changes_in_inventories_or_finished_goods=parsed_json.get("total_changes_in_inventories_or_finished_goods"),
                total_employee_benefit_expense=parsed_json.get("total_employee_benefit_expense"),
                total_other_expenses=parsed_json.get("total_other_expenses"),
                operating_profit=parsed_json.get("operating_profit"),
                other_income=parsed_json.get("other_income"),
                depreciation=parsed_json.get("depreciation"),
                profit_before_interest_and_tax=parsed_json.get("profit_before_interest_and_tax"),
                interest=parsed_json.get("interest"),
                profit_before_tax_and_exceptional_items_before_tax=parsed_json.get("profit_before_tax_and_exceptional_items_before_tax"),
                exceptional_items_before_tax=parsed_json.get("exceptional_items_before_tax"),
                profit_before_tax=parsed_json.get("profit_before_tax"),
                income_tax=parsed_json.get("income_tax"),
                profit_for_period_from_continuing_operations=parsed_json.get("profit_for_period_from_continuing_operations"),
                profit_from_discontinuing_operation_after_tax=parsed_json.get("profit_from_discontinuing_operation_after_tax"),
                minority_interest_and_profit_from_associates_and_joint_ventures=parsed_json.get("minority_interest_and_profit_from_associates_and_joint_ventures"),
                profit_after_tax=parsed_json.get("profit_after_tax"),
                total_operating_cost=parsed_json.get("total_operating_cost"),
                revenue_from_operations=parsed_json.get("revenue_from_operations"),
                revenue_from_interest=parsed_json.get("revenue_from_interest"),
                revenue_from_other_financial_services=parsed_json.get("revenue_from_other_financial_services"),
                revenue_from_sale_of_products=parsed_json.get("revenue_from_sale_of_products"),
                revenue_from_sale_of_services=parsed_json.get("revenue_from_sale_of_services"),
                other_operating_revenues=parsed_json.get("other_operating_revenues"),
                excise_duty=parsed_json.get("excise_duty"),
                service_tax_collected=parsed_json.get("service_tax_collected"),
                other_duties_taxes_collected=parsed_json.get("other_duties_taxes_collected"),
                sale_of_goods_manufactured_domestic=parsed_json.get("sale_of_goods_manufactured_domestic"),
                sale_of_goods_traded_domestic=parsed_json.get("sale_of_goods_traded_domestic"),
                sale_or_supply_of_services_domestic=parsed_json.get("sale_or_supply_of_services_domestic"),
                sale_or_supply_of_services_export=parsed_json.get("sale_or_supply_of_services_export"),
                sale_of_goods_manufactured_export=parsed_json.get("sale_of_goods_manufactured_export"),
                sale_of_goods_traded_export=parsed_json.get("sale_of_goods_traded_export"),
                depreciation_amortisation=parsed_json.get("depreciation_amortisation"),
                depletion=parsed_json.get("depletion"),
                depreciation_and_amortization=parsed_json.get("depreciation_and_amortization"),
                profit_before_tax_cf=parsed_json.get("profit_before_tax_cf"),
                adjustment_for_finance_cost_and_depreciation=parsed_json.get("adjustment_for_finance_cost_and_depreciation"),
                adjustment_for_current_and_non_current_assets=parsed_json.get("adjustment_for_current_and_non_current_assets"),
                adjustment_for_current_and_non_current_liabilities=parsed_json.get("adjustment_for_current_and_non_current_liabilities"),
                other_adjustments_in_operating_activities=parsed_json.get("other_adjustments_in_operating_activities"),
                cash_flows_from_used_in_operating_activities=parsed_json.get("cash_flows_from_used_in_operating_activities"),
                cash_outflow_from_purchase_of_assets=parsed_json.get("cash_outflow_from_purchase_of_assets"),
                cash_inflow_from_sale_of_assets=parsed_json.get("cash_inflow_from_sale_of_assets"),
                income_from_assets=parsed_json.get("income_from_assets"),
                other_adjustments_in_investing_activities=parsed_json.get("other_adjustments_in_investing_activities"),
                cash_flows_from_used_in_investing_activities=parsed_json.get("cash_flows_from_used_in_investing_activities"),
                cash_outflow_from_repayment_of_capital_and_borrowings=parsed_json.get("cash_outflow_from_repayment_of_capital_and_borrowings"),
                cash_inflow_from_raisng_capital_and_borrowings=parsed_json.get("cash_inflow_from_raisng_capital_and_borrowings"),
                interest_and_dividends_paid=parsed_json.get("interest_and_dividends_paid"),
                other_adjustments_in_financing_activities=parsed_json.get("other_adjustments_in_financing_activities"),
                cash_flows_from_used_in_financing_activities=parsed_json.get("cash_flows_from_used_in_financing_activities"),
                incr_decr_in_cash_cash_equv_before_effect_of_excg_rate_changes=parsed_json.get("incr_decr_in_cash_cash_equv_before_effect_of_excg_rate_changes"),
                adjustments_to_cash_and_cash_equivalents=parsed_json.get("adjustments_to_cash_and_cash_equivalents"),
                incr_decr_in_cash_cash_equv=parsed_json.get("incr_decr_in_cash_cash_equv"),
                cash_flow_statement_at_end_of_period=parsed_json.get("cash_flow_statement_at_end_of_period"),
                managerial_remuneration=parsed_json.get("managerial_remuneration"),
                payment_to_auditors=parsed_json.get("payment_to_auditors"),
                insurance_expenses=parsed_json.get("insurance_expenses"),
                power_and_fuel=parsed_json.get("power_and_fuel"),
                auditor_name=parsed_json.get("auditor_name"),
                auditor_firm_name=parsed_json.get("auditor_firm_name"),
                pan=parsed_json.get("pan"),
                membership_number=parsed_json.get("membership_number"),
                firm_registration_number=parsed_json.get("firm_registration_number"),
                auditor_address=parsed_json.get("auditor_address"),
                report_has_adverse_remarks=parsed_json.get("report_has_adverse_remarks"),
                auditor_comments=parsed_json.get("auditor_comments"),
                auditor_additional=parsed_json.get("auditor_additional"),
                additional_fields=parsed_json.get("additional_fields"),
                data=year_data,
                labels_map=parsed_json.get("labels_map")
            )
            db.add(financials)
        await db.commit()
        # Commit the session to save all financial data
        print(f"Financial data for document ID {doc_id} populated successfully.")
        return
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in populating Chartered accountant data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in populating Chartered accountant data: {e}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()  # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in populating chartered accountant data: {e}")

# async def populate_compliance_table(doc_id: str):
#     async_session_gen = get_db_async()
#     db = None
#     try:
#         db = await async_session_gen.__anext__()  # Get the actual session from the generator

#          # find company based on the doc_id
#         document = await db.execute(
#             select(CompanyDocumentUploaded).where(CompanyDocumentUploaded.id == doc_id)
#         )
#         document = document.scalars().first()
#         if not document:
#             raise ValueError(f"No document found with ID {doc_id}")
#         company_id = document.company_id
#         if not company_id:
#             raise ValueError(f"Document with ID {doc_id} does not have a valid company_id")
        
#         # get the offering using doc_id
#         offering = await db.execute(
#             select(IPOOfferings).where(IPOOfferings.id == UUID(doc_id))
#         )

#         offering = offering.scalars().first()
#         if not offering:
#             raise ValueError(f"No offering found with ID {doc_id}")
        
#         eligibility_type = offering.eligibility_type
#         if not eligibility_type:
#             raise ValueError(f"Offering with ID {doc_id} does not have a valid eligibility_type")
        
#         # get all the elig requirements for the eligibility type
#         requirements_elig = await db.execute(
#             select(ComplianceClauses).where(ComplianceClauses.statue=='new', ComplianceClauses.obligation_type=='eligibility', ComplianceClauses.applicability==eligibility_type)
#         )
#         requirements_elig = requirements_elig.scalars().all()
#         if not requirements_elig:
#             raise ValueError(f"No requirements found for eligibility type {eligibility_type}")
        
#         print(f"Found {len(requirements_elig)} requirements for eligibility type {eligibility_type}")

#         # get all the general conditions
#         requirements_general = await db.execute(
#             select(ComplianceClauses).where(ComplianceClauses.statue=='new', ComplianceClauses.obligation_type=='general')
#         )
#         requirements_general = requirements_general.scalars().all()
#         if not requirements_general:
#             raise ValueError("No general conditions found")
#         print(f"Found {len(requirements_general)} general conditions")

#         # get all the disclosure requirements
#         requirements_disclosure = await db.execute(
#             select(ComplianceClauses).where(ComplianceClauses.statue=='new', ComplianceClauses.obligation_type=='disclosure')
#         )
#         requirements_disclosure = requirements_disclosure.scalars().all()
#         if not requirements_disclosure:
#             raise ValueError("No disclosure requirements found")
#         print(f"Found {len(requirements_disclosure)} disclosure requirements")

#         # now we need to preprocess the requirements_disclosure a little bit
#         disclosure_req = []
#         for req in requirements_disclosure:
#             if eligibility_type in req.applicability or req.applicability == 'All':
#                 disclosure_req.append({
#                     "requirement": req.requirement,
#                     "icdr_clause": req.clause_reference,
#                     "obligation_type": req.obligation_type,
#                     "category": req.category,
#                     "applicability": req.applicability,
#                     "effective_date": req.effective_date,
#                     "status": req.status,
#                     "clause_id": str(req.id)
#                 })

#         # Now we have to check for all these requirements using gptwrapper to see if the document satisfies these requirements
#         gpt = GPTWrapper(doc_id, iterations=5)

#         # first check eligibility requirements
#         print("Checking eligibility requirements...")
#         for req in requirements_elig:
#             query = PROMPT_COMPLIANCE_CHECK + f"""\n\n
#             Requirement: {req.requirement}
#             ICDR Clause: {req.icdr_clause}
#             """
#             response = gpt.chat(query)
#             if not response:
#                 print(f"Failed to get response for requirement: {req.get('requirement')}")
#                 continue
#             response_data = parse_llm_response(response)
#             if not response_data or not isinstance(response_data, dict):
#                 print(f"Parsed response data for requirement {req.get('requirement')} is empty or invalid")
#                 continue
#             compliance_data = IssuerComplianceStatus(
#                 id=uuid4(),
#                 company_id=UUID(company_id),  # or fetch the related ListingCompanies UUID if you have it, else keep None
#                 listing_id=UUID(doc_id),  # Assuming doc_id is the listing ID
#                 clause_id=UUID(req.id),  # Assuming req.id is the clause ID
#                 issuer_disclosure=response_data.get("disclosure") or "N/A",
#                 independent_check=None,
#                 is_compliant=
#             )