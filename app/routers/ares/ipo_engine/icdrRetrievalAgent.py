import json
from typing import List, Dict, Union

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import StateGraph, END
# No longer need SqliteSaver
from langchain.chat_models import init_chat_model
from pydantic import BaseModel, Field

from .retriever import get_bm25_retriever, get_vector_retriever
from .utils import parse_llm_response
from .prompts import PROMPT_RESEARCHER_ICDR, PROMPT_SUPERVISOR_ICDR

class State(BaseModel):
    """
    Represents the in-memory state of the agent's execution graph for a single run.
    """
    doc_id: str
    user_query: str
    base_query: str = ""
    no_iterations: int = 0
    queries_generated: List[str] = Field(default_factory=list)
    all_queries: List[str] = Field(default_factory=list)
    # CUMULATIVE context from all iterations
    retrieved_context: List[Dict] = Field(default_factory=list)
    # NEW context from the LATEST iteration only
    newly_retrieved_context: List[Dict] = Field(default_factory=list)
    feedback: str = ""
    finished: bool = False
    final_response: List[Dict] = Field(default_factory=list)

class DataRetrievalAgent:
    """
    An agent that uses an efficient, incremental supervision process to retrieve data.
    The state for each run is managed entirely in memory.
    """
    def __init__(self, supervisor_llm_str: str, researcher_llm_str: str, doc_id: str, iterations: int = 5):
        self.iterations = iterations
        self.supervisor_llm = init_chat_model(supervisor_llm_str)
        self.researcher_llm = init_chat_model(researcher_llm_str)
        self.doc_id = doc_id

        graph = StateGraph(State)
        graph.add_node("llm_researcher", self.llm_researcher)
        graph.add_node("retrievers", self.retrievers)
        graph.add_node("llm_supervisor", self.llm_supervisor)
        graph.add_node("process_final_output", self.process_final_output)

        graph.set_entry_point("llm_researcher")
        graph.add_edge("llm_researcher", "retrievers")
        graph.add_edge("retrievers", "llm_supervisor")
        graph.add_conditional_edges(
            "llm_supervisor",
            self.router,
            {"continue": "llm_researcher", "end": "process_final_output"}
        )
        graph.add_edge("process_final_output", END)

        self.bm25_retriever = get_bm25_retriever(doc_id)
        self.vector_retriever = get_vector_retriever("modus2", "rag_retriever")
        print("Compiled DataRetrievalAgent with BM25 and Vector Retrievers")
        self.agent = graph.compile()

    def llm_researcher(self, state: State) -> Dict:
        """
        Generates new queries based on the supervisor's latest feedback.
        """
        system_prompt = PROMPT_RESEARCHER_ICDR
        if state.no_iterations == 0:
            user_message = f"User Request: {state.user_query}"
        else:
            user_message = f"""
            User Request: {state.user_query}
            Your Previous Base Query: {state.base_query}
            Supervisor Feedback: {state.feedback}
            """
        messages = [SystemMessage(content=system_prompt), HumanMessage(content=user_message)]
        response = self.researcher_llm.invoke(messages)
        response_data = parse_llm_response(response)
        print("----"*20)
        print("Iteration:", state.no_iterations + 1)
        print(f"LLM Researcher Response: {response_data}")

        return {
            "base_query": state.base_query or response_data.get("new_base_query", ""),
            "queries_generated": response_data.get("queries", []),
            "all_queries": state.all_queries + response_data.get("queries", []),
            "no_iterations": state.no_iterations + 1,
        }

    def retrievers(self, state: State) -> Dict:
        """
        Executes the latest queries and separates new results from cumulative results.
        """
        newly_fetched_results = []
        for query in state.queries_generated:
            newly_fetched_results.extend(self.bm25_retriever.query(query, top_k=3))
            vector_docs = self.vector_retriever.invoke(query, filter={"doc_id": state.doc_id})
            newly_fetched_results.extend(
                [{**doc.metadata, "text": doc.page_content} for doc in vector_docs]
            )

        # Deduplicate the newly fetched results for this iteration
        unique_new_results = list({f"{res.get('page', '')}_{res.get('chunk_id', '')}": res for res in newly_fetched_results}.values())

        print(f"Newly Retrieved Context: {len(unique_new_results)} unique results")

        return {
            "newly_retrieved_context": unique_new_results,
        }

    def llm_supervisor(self, state: State) -> Dict:
        """
        Incrementally evaluates if the NEWLY retrieved context satisfies the last feedback.
        """
        system_prompt = PROMPT_SUPERVISOR_ICDR
        if state.no_iterations >= self.iterations:
            return {
                "finished": True,
                "feedback": "Maximum iterations reached. Terminating process."
            }

        # On the first iteration, there's no previous feedback, so we evaluate against the base query
        previous_feedback = state.feedback or "This is the first check, please evaluate the context against the base query."

        message_content = f"""
        User's Ultimate Goal (Base Query): {state.user_query}
        Newly formed Base Query: {state.base_query}
        Your Previous Feedback (What you asked for this turn): {previous_feedback}
        Newly Retrieved Context (The evidence to inspect): {json.dumps(state.newly_retrieved_context, indent=2)}
        """
        messages = [SystemMessage(content=system_prompt), HumanMessage(content=message_content)]
        response = self.supervisor_llm.invoke(messages)
        response_data = parse_llm_response(response)

        # Drop the unnecessary excerpts based on the supervisor's feedback
        necessary_excerpts = response_data.get("necessary_excerpts", [])
        filtered_new_context = []
        if necessary_excerpts:
            for exp in necessary_excerpts:
                page = exp.get("page", 0)
                chunk_id = exp.get("chunk_id", 0)
                # only keep excerpts that match the necessary excerpts
                for excerpt in state.newly_retrieved_context:
                    if excerpt.get("page") == page and excerpt.get("chunk_id") == chunk_id:
                        filtered_new_context.append(excerpt)

        combined_context = state.retrieved_context + filtered_new_context

        print("- - - "*20)
        print("Feedback from Supervisor:", response_data.get("feedback", "No feedback provided"))
        print("- - - "*20)
        return {
            "finished": response_data.get("finished", False),
            "feedback": response_data.get("feedback", ""),
            "retrieved_context": combined_context,
        }

    def process_final_output(self, state: State) -> Dict:
        """
        Generates the final response using the CUMULATIVE retrieved context.
        """
        return {
            "final_response": state.retrieved_context
        }


    def router(self, state: State) -> str:
        """Routes execution based on the supervisor's decision."""
        return "end" if state.finished else "continue"

    def invoke(self, user_query: str):
        """Executes the agent for a given user query and document."""
        initial_state = {"doc_id": self.doc_id, "user_query": user_query}
        final_state = self.agent.invoke(initial_state)
        return final_state.get("final_response")