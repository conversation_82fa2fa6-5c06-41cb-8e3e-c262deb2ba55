import boto3
import os
import json
from uuid import uuid4
from botocore.exceptions import ClientError
from botocore.config import Config

# Configuration constants
AWS_REGION = os.environ.get("AWS_REGION", "ap-south-1")
DEFAULT_BUCKET_NAME = "documents-modus"
DEFAULT_EXPIRATION = 3600
MAX_FILE_SIZE = 20971520  # 20MB in bytes

def create_presigned_post(bucket_name, object_name, expiration=DEFAULT_EXPIRATION):
    """
    Generates a presigned URL and the necessary form fields for an HTTP POST request.
    This is often preferred for browser-based uploads.

    :param bucket_name: String name of the S3 bucket.
    :param object_name: String name of the S3 object (the file's path in the bucket).
    :param expiration: Time in seconds for the presigned URL to remain valid.
    :return: A dictionary containing the URL and form fields, or None if error.
    """
    s3_client = boto3.client('s3', region_name=AWS_REGION, config=Config(signature_version='s3v4'))
    try:
        response = s3_client.generate_presigned_post(
            Bucket=bucket_name,
            Key=object_name,
            Fields={"Content-Type": "application/pdf"},
            Conditions=[
                {"Content-Type": "application/pdf"},
                ["content-length-range", 1, MAX_FILE_SIZE]
            ],
            ExpiresIn=expiration
        )
    except ClientError as e:
        print(f"Error generating presigned URL: {e}")
        return None, None

    return response

def create_presigned_url_for_download(bucket_name, object_name, expiration=DEFAULT_EXPIRATION):
    """
    Generates a presigned URL to download an S3 object.

    :param bucket_name: String name of the S3 bucket.
    :param object_name: String name of the S3 object (the file's path in the bucket).
    :param expiration: Time in seconds for the presigned URL to remain valid.
    :return: The presigned URL as a string, or None if error.
    """
    s3_client = boto3.client('s3', region_name=AWS_REGION)
    try:
        response = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': object_name},
            ExpiresIn=expiration
        )
    except ClientError as e:
        print(f"Error generating presigned URL for download: {e}")
        return None
    return response

def upload_json_to_s3(data: dict, bucket_name: str, key: str) -> bool:
    """
    Uploads a JSON object to an S3 bucket.

    :param data: Dictionary to be saved as JSON.
    :param bucket_name: Name of the S3 bucket.
    :param key: S3 object key (e.g., 'foldername/filename.json').
    :return: True if upload succeeds, False otherwise.
    """
    try:
        s3_client = boto3.client("s3", region_name=AWS_REGION)
        json_bytes = json.dumps(data).encode("utf-8")
        s3_client.put_object(
            Bucket=bucket_name,
            Key=key,
            Body=json_bytes,
            ContentType='application/json'
        )
        print(f"Successfully uploaded to s3://{bucket_name}/{key}")
        return True
    except ClientError as e:
        print(f"Error uploading JSON to S3: {e}")
        return False
    
def load_json_from_s3(bucket_name: str, key: str) -> dict:
    """
    Loads a JSON object from an S3 bucket.

    :param bucket_name: Name of the S3 bucket.
    :param key: S3 object key (e.g., 'foldername/filename.json').
    :return: Parsed JSON object as a Python dictionary, or None if error.
    """
    try:
        s3_client = boto3.client("s3", region_name=AWS_REGION)
        response = s3_client.get_object(Bucket=bucket_name, Key=key)
        json_data = response['Body'].read().decode('utf-8')
        return json.loads(json_data)
    except ClientError as e:
        print(f"Error loading JSON from S3: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"Invalid JSON format: {e}")
        return None
    
def list_s3_files(bucket_name, prefix=''):
    """
    List all files in a specific S3 bucket directory (prefix).

    :param bucket_name: Name of the S3 bucket
    :param prefix: Directory path inside the bucket (e.g., 'folder/subfolder/')
    """
    s3 = boto3.client('s3')

    paginator = s3.get_paginator('list_objects_v2')
    pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix)

    file_list = []

    for page in pages:
        if 'Contents' in page:
            for obj in page['Contents']:
                file_list.append(obj['Key'])

    return file_list

# 2. Upload to S3
def upload_to_s3(local_path, bucket_name, s3_key):
    s3 = boto3.client('s3')
    with open(local_path, 'rb') as f:
        s3.upload_fileobj(f, bucket_name, s3_key)
    print(f"✅ Uploaded to s3://{bucket_name}/{s3_key}")

def download_from_s3(bucket_name, s3_key, download_path):
    s3 = boto3.client('s3')
    with open(download_path, 'wb') as f:
        s3.download_fileobj(bucket_name, s3_key, f)
    print(f"📥 Downloaded to {download_path}")