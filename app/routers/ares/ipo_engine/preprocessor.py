from datetime import datetime
from zoneinfo import ZoneInfo
from uuid import uuid4
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import select
from sqlalchemy.orm.attributes import flag_modified
import asyncio
import copy
from .utils import *
from .file_upload import *
from .ingesting_vectors import *
from ....models import models
from ....database import get_db_async
from ....utils.json_utils import serialize_to_json

# Configuration constants
DOCUMENTS_BUCKET = "documents-modus"

async def update_job_object(job_id: str, status: str=None, error: str=None, step: dict=None):
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        if not db:
            print(f"Error: Failed to acquire DB session in update_job_object for job_id {job_id}")
            return

        job = await db.execute(select(models.Job).where(models.Job.job_id == job_id))
        job = job.scalars().first()

        if not job:
            print(f"Error: Job not found in update_job_object for job_id {job_id}")
            return
        
        if status:
            job.job_status = status

        if error:
            job.job_error = error

        if step:
            to_push = {
                "timestamp": datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
                "status": step.get("status"),
                "message": step.get("message"),
                "event": step.get("event")
            }
            job.job_history.append(serialize_to_json(to_push))
            flag_modified(job, "job_history")

        job.job_updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
        await db.commit()

    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in update_job_object for job_id {job_id}: {e}")
    except Exception as e:
        print(f"Unexpected error in update_job_object for job_id {job_id}: {e}")
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose() # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in update_job_object for job_id {job_id}: {e}")


async def download_and_parse_pdf(job_id: str, upload_id: str):
    """This function will download and parse the pdf."""

    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Correctly await the async generator

        if not db:
            print(f"Error: Failed to acquire DB session in preprocess_pdf for upload_id {upload_id}")
            return None
        
        await update_job_object(job_id, status="running", error=None, step={
            "status": "running",
            "message": "Downloading pdf",
            "event": "downloading_started"
        })

        # Step 2: Create a presigned url to download the pdf from the s3 storage
        query = select(models.CompanyDocumentUploaded).where(models.CompanyDocumentUploaded.id == upload_id)
        result = await db.execute(query)
        upload_data = result.scalars().first()

        if not upload_data:
            print(f"Error: Upload data not found for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="No upload data found for upload id")
            return None
        
        download_url = create_presigned_url_for_download(
            upload_data.aws_data["bucket"],
            upload_data.aws_data["key"]
        )

        if not download_url:
            print(f"Error: Failed to generate download url for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to generate download url")
            return None
        
        # Step 3: Download the pdf
        file_path = download_pdf_from_presigned_url(download_url)
        if not file_path:
            print(f"Error: Failed to download pdf for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to download pdf")
            return None
        
        await update_job_object(job_id, step={
            "status": "running",
            "message": "Downloaded pdf successfully",
            "event": "downloading_completed"
        })

        await update_job_object(job_id, status="running", error=None, step={
            "status": "running",
            "message": "Parsing pdf",
            "event": "parsing_started"
        })

        # Step 4: Parse the pdf
        parsed_pdf = await parse_pdf(file_path)
        if not parsed_pdf:
            print(f"Error: Failed to parse pdf for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to parse pdf")
            return None
        
        await update_job_object(job_id, step={
            "status": "running",
            "message": "Parsed pdf successfully",
            "event": "parsing_completed"
        })

        # upload the parsed pdf to s3
        key = f"parsed_raw_data/{upload_id}.json"
        if not upload_json_to_s3(parsed_pdf, DOCUMENTS_BUCKET, key):
            print(f"Error: Failed to upload parsed pdf to s3 for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to upload parsed pdf to s3")
            return None

        # Step 5: Save the parsed pdf to the database
        raw_data = models.CompanyDocumentRawData(
            id=str(uuid4()),
            doc_id=upload_id,
            job_id=job_id,
            raw_data={
                "bucket": DOCUMENTS_BUCKET,
                "key": key
            },
            created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
            updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
        )
        stdb = await save_json_to_db(raw_data, db)  
        if stdb == 0:
            print(f"Error: Failed to save raw data for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to save raw data")
            return None

        await update_job_object(job_id, status="completed", error=None, step={
            "status": "completed",
            "message": "Raw data saved successfully",
            "event": "raw_data_saved"
        })
        return raw_data.id
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in preprocess_pdf for upload_id {upload_id}: {e}")
        await update_job_object(job_id, status="failed", error=f"Database error: {str(e)}")
        return None
    except Exception as e:
        print(f"Unexpected error in preprocess_pdf for upload_id {upload_id}: {e}")
        await update_job_object(job_id, status="failed", error=f"Unexpected error: {str(e)}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose() # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in download_and_parse_pdf for upload_id {upload_id}: {e}")


async def preprocess_pdf(job_id: str, upload_id: str, raw_data_id: str = None):
    """This function will preprocess uploaded pdf to be ready for retriever."""
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        if not db:
            print(f"Error: Failed to acquire DB session in preprocess_pdf for upload_id {upload_id}")
            return None
        
        await update_job_object(job_id, status="running", error=None, step={
            "status": "running",
            "message": "Loading raw data",
            "event": "loading_raw_data"
        })

        # Step 1: Get the raw data from the database
        if raw_data_id:
            query = select(models.CompanyDocumentRawData).where(models.CompanyDocumentRawData.id == raw_data_id)
            result = await db.execute(query)
            raw_data_obj = result.scalars().first()

            if not raw_data_obj:
                print(f"Error: Raw data not found for raw_data_id {raw_data_id}")
                await update_job_object(job_id, status="failed", error="No raw data found for raw data id")
                return None
        else:
            query = select(models.CompanyDocumentRawData).where(models.CompanyDocumentRawData.doc_id == upload_id).order_by(models.CompanyDocumentRawData.created_at.desc())
            result = await db.execute(query)
            raw_data_obj = result.scalars().first()

            if not raw_data_obj:
                print(f"Error: Raw data not found for upload_id {upload_id}")
                await update_job_object(job_id, status="failed", error="No raw data found for upload id")
                return None
            
        # load the raw data from s3
        raw_data_json = load_json_from_s3(raw_data_obj.raw_data["bucket"], raw_data_obj.raw_data["key"])
        if not raw_data_json:
            print(f"Error: Failed to load raw data from s3 for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to load raw data from s3")
            return None
            
        await update_job_object(job_id, step={
            "status": "running",
            "message": "Raw data loaded successfully",
            "event": "raw_data_loaded"
        })

        # Step 2: Find the index page
        try:
            index_page_number, index_content = get_index_page(raw_data_json)
            if not index_page_number:
                print(f"Error: Index page not found for upload_id {upload_id}")
                await update_job_object(job_id, status="failed", error="Index page not found")
                return None
        except Exception as e:
            print(f"Error finding index page for upload_id {upload_id}: {str(e)}")
            await update_job_object(job_id, status="failed", error=f"Error finding index page: {str(e)}")
            return None
        
        await update_job_object(job_id, step={
            "status": "running",
            "message": "Index page found",
            "event": "index_page_found"
        })

        # Step 3: Parse the index page
        index_data = await parse_index_page(index_content)
        if not index_data:
            print(f"Error: Failed to parse index page for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to parse index page")
            return None
        
        await update_job_object(job_id, step={
            "status": "running",
            "message": "Index page parsed successfully",
            "event": "index_page_parsed"
        })

        await update_job_object(job_id, status="running", error=None, step={
            "status": "running",
            "message": "Preprocessing raw data",
            "event": "preprocessing_started"
        })
    
        # Step 4: Add starting pages as a section
        index_data = add_starting_pages_as_section(index_data.copy(), index_page_number)
        if not index_data:
            print(f"Error: Failed to add index page as section for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to add index page as section")
            return None
        
        # save chunks to s3
        chunked_data = create_chunk_data(copy.deepcopy(raw_data_json), index_data)
        print(f"Number of chunks to be saved: {len(chunked_data)}")
        key = f"chunked_data/{upload_id}.json"
        if not upload_json_to_s3({"chunks": chunked_data}, DOCUMENTS_BUCKET, key):
            print(f"Error: Failed to upload chunked data to s3 for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to upload chunked data to s3")
            return None
        
        await update_job_object(job_id, step={
            "status": "running",
            "message": "Index page added as section successfully",
            "event": "index_page_added_as_section"
        })

        await update_job_object(job_id, step={
            "status": "running",
            "message": "Reading through the document",
            "event": "reading_through_document_started"
        })

        # Step 5: Create subsections
        all_pages = raw_data_json.get('pages', [])
        print(f"Number of pages: {len(all_pages)}")

        sections = copy.deepcopy(index_data['sections'])

        # Main processing loop using async/await patterns
        # We are commenting out the processing of data for now, save raw data as it is to the database
        print("\n Skipping processing of data for now, saving raw data as it is to the database")
        # for section in sections:
        #     section_name = section['name']
        #     section_start_page = int(section['page_range'][0])
        #     section_end_page = int(section['page_range'][1])
        #     print(f"Section: {section_name}, Start Page: {section_start_page}, End Page: {section_end_page}")

        #     _, page_numbers_in_this_section = get_pages_in_section(all_pages, section_start_page, section_end_page)

        #     subsections_data = section.get('subsections', [])
        #     page_numbers_in_this_section = set(page_numbers_in_this_section)

        #     # Prepare tasks for initial subsections
        #     subsection_tasks = []
        #     subsection_metadata = []
        #     for subsection in subsections_data:
        #         subsection_name = subsection['name']
        #         subsection_start_page = int(subsection['page_range'][0])
        #         subsection_end_page = int(subsection['page_range'][1])
        #         print(f"\tSubsection: {subsection_name}, Start Page: {subsection_start_page}, End Page: {subsection_end_page}")
        #         all_pages_in_this_subsection_plus_one, page_numbers_in_this_subsection = get_pages_in_section_plus_one(all_pages, subsection_start_page, subsection_end_page)
        #         page_numbers_in_this_section -= set(page_numbers_in_this_subsection)

        #         task = process_subsection_task(all_pages_in_this_subsection_plus_one, subsection_name)
        #         subsection_tasks.append(task)
        #         subsection_metadata.append(subsection)

        #     # Process initial subsections concurrently
        #     if subsection_tasks:
        #         subsection_results = await asyncio.gather(*subsection_tasks)

        #         # Process results for initial subsections
        #         for result, subsection in zip(subsection_results, subsection_metadata):
        #             subsubsections = result
        #             subsection['subsubsections'] = subsubsections

        #     # Process the remaining pages in the section to create new subsections
        #     chunks_consecutive_pages = get_chunks_consecutive_pages(page_numbers_in_this_section)

        #     # Prepare tasks for new subsections from remaining chunks
        #     chunk_tasks = []
        #     for chunk in chunks_consecutive_pages:
        #         task = process_chunk_task(all_pages, chunk, section_name)
        #         chunk_tasks.append(task)

        #     # Process new subsections concurrently
        #     if chunk_tasks:
        #         chunk_results = await asyncio.gather(*chunk_tasks)

        #         # Process results for new subsections
        #         for new_subsections in chunk_results:
        #             section['subsections'].extend(new_subsections)

        # await update_job_object(job_id, step={
        #     "status": "running",
        #     "message": "Reading through the document completed",
        #     "event": "reading_through_document_completed"
        # })

        # await update_job_object(job_id, step={
        #     "status": "running",
        #     "message": "Summarizing the document",
        #     "event": "summarizing_started"
        # })

        # # Step 6: Summarize the sections using parallel processing
        # # Phase 1: Summarize subsubsections
        # print("\n[1] Summarizing subsubsections...")
        # subsubsection_tasks = []
        # for section in sections:
        #     print(section['name'])
        #     for subsection in section.get('subsections', []):
        #         print("\t", subsection['name'])
        #         for subsub in subsection.get('subsubsections', []):
        #             if subsub.get('raw_text'):  # Only process if raw_text exists
        #                 subsubsection_tasks.append(summarize_subsubsection(subsub))

        # # Execute all subsubsection summarization tasks concurrently
        # if subsubsection_tasks:
        #     await asyncio.gather(*subsubsection_tasks)

        # await update_job_object(job_id, step={
        #     "status": "running",
        #     "message": "Subsubsections summarized",
        #     "event": "subsubsections_summarized"
        # })

        # # Phase 2: Summarize subsections
        # print("\n[2] Summarizing subsections...")
        # subsection_tasks = []
        # for section in sections:
        #     print(section['name'])
        #     for subsection in section.get('subsections', []):
        #         print("\t", subsection['name'])
        #         subsection_tasks.append(summarize_subsection(subsection))

        # # Execute all subsection summarization tasks concurrently
        # if subsection_tasks:
        #     await asyncio.gather(*subsection_tasks)

        # await update_job_object(job_id, step={
        #     "status": "running",
        #     "message": "Subsections summarized",
        #     "event": "subsections_summarized"
        # })

        # # Phase 3: Summarize sections
        # print("\n[3] Summarizing sections...")
        # section_tasks = []
        # for section in sections:
        #     section_tasks.append(summarize_section(section))

        # # Execute all section summarization tasks concurrently
        # if section_tasks:
        #     await asyncio.gather(*section_tasks)

        # await update_job_object(job_id, step={
        #     "status": "running",
        #     "message": "Sections summarized",
        #     "event": "sections_summarized"
        # })

        await update_job_object(job_id, step={
            "status": "running",
            "message": "Summarizing the document completed",
            "event": "summarizing_completed"
        })
        
        # upload the processed data to s3
        key = f"processed_data/{upload_id}.json"
        if not upload_json_to_s3({"sections": sections}, DOCUMENTS_BUCKET, key):
            print(f"Error: Failed to upload processed data to s3 for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to upload processed data to s3")
            return None

        # Step 7: Save the processed data to the database
        processed_data = models.CompanyDocumentProcessedData(
            id=str(uuid4()),
            doc_id=upload_id,
            job_id=job_id,
            table_of_contents=index_data,
            processed_data={
                "bucket": DOCUMENTS_BUCKET,
                "key": key
            },
            created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
            updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
        )

        stdb = await save_json_to_db(processed_data, db)
        if stdb == 0:
            print(f"Error: Failed to save processed data for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to save processed data")
            return None

        await update_job_object(job_id, status="completed", error=None, step={
            "status": "completed",
            "message": "Processed data saved successfully",
            "event": "processed_data_saved"
        })
        return processed_data.id
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in preprocess_pdf for upload_id {upload_id}: {e}")
        await update_job_object(job_id, status="failed", error=f"Database error: {str(e)}")
        return None
    except Exception as e:
        print(f"Unexpected error in preprocess_pdf for upload_id {upload_id}: {e}")
        await update_job_object(job_id, status="failed", error=f"Unexpected error: {str(e)}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose() # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in preprocess_pdf for upload_id {upload_id}: {e}")

async def full_preprocess_pdf(job_id: str, upload_id: str):
    # if download and parse pdf is already done, get the raw_data_id from the database
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        if not db:
            print(f"Error: Failed to acquire DB session in full_preprocess_pdf for upload_id {upload_id}")
            return None
        
        # Step 1: Create an object in the database to keep track of things
        job = models.Job(
            job_id=job_id,
            doc_id=upload_id,
            job_type="parsing",
            job_status="running",
            job_history=[
                serialize_to_json({
                    "timestamp": datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
                    "status": "running",
                    "message": "Job started",
                    "event": "job_started"
                }),
            ],
            job_data={},
            job_error=None,
            job_created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
            job_updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
        )
        
        stdb = await save_json_to_db(job, db)
        if stdb == 0:
            print(f"Error: Failed to save job object for upload_id {upload_id}")
            return None
        
        print(f"Job created successfully for upload_id: {upload_id} with job_id: {job_id}")
        
        query = select(models.CompanyDocumentRawData).where(models.CompanyDocumentRawData.doc_id == upload_id).order_by(models.CompanyDocumentRawData.created_at.desc())
        result = await db.execute(query)
        raw_data_obj = result.scalars().first()

        if not raw_data_obj:
            print(f"Raw data not present in the database for {upload_id}. Downloading and parsing the pdf.")
            raw_data_id = await download_and_parse_pdf(job_id, upload_id)
            if not raw_data_id:
                print(f"Error: Failed to download and parse pdf for upload_id {upload_id}")
                await update_job_object(job_id, status="failed", error="Failed to download and parse pdf")
                return None
        else:
            raw_data_id = raw_data_obj.id
            print(f"Raw data already present in the database for {upload_id}. Skipping download and parse step.")
            await update_job_object(job_id, status="running", error=None, step={
                "status": "running",
                "message": "Raw data already present in the database, skipping download and parse step",
                "event": "raw_data_already_present"
            })  

        query = select(models.CompanyDocumentProcessedData).where(models.CompanyDocumentProcessedData.doc_id == upload_id).order_by(models.CompanyDocumentProcessedData.created_at.desc())
        result = await db.execute(query)
        processed_data_obj = result.scalars().first()

        if not processed_data_obj:
            print(f"Processed data not present in the database for {upload_id}. Preprocessing the pdf.")
            processed_data_id = await preprocess_pdf(job_id, upload_id, raw_data_id)
            if not processed_data_id:
                print(f"Error: Failed to preprocess pdf for upload_id {upload_id}")
                await update_job_object(job_id, status="failed", error="Failed to preprocess pdf")
                return None
        else:
            print(f"Processed data already present in the database for {upload_id}. Skipping preprocessing step.")
            await update_job_object(job_id, status="running", error=None, step={
                "status": "running",
                "message": "Processed data already present in the database, skipping preprocessing step",
                "event": "processed_data_already_present"
            })
            processed_data_id = processed_data_obj.id
        
        # Step 8: Ingest the data into pinecone
        # load the processed data from the database and check if pushed to pinecone is true or not
        query = select(models.CompanyDocumentProcessedData).where(models.CompanyDocumentProcessedData.id == processed_data_id)
        result = await db.execute(query)
        processed_data_obj = result.scalars().first()

        # load the raw data from the database
        query = select(models.CompanyDocumentRawData).where(models.CompanyDocumentRawData.id == raw_data_id)
        result = await db.execute(query)
        raw_data_obj = result.scalars().first()

        if not processed_data_obj:
            print(f"Error: Failed to load processed data for upload_id {upload_id}")
            await update_job_object(job_id, status="failed", error="Failed to load processed data")
            return None

        if processed_data_obj.pushed_to_pinecone:
            print(f"Data already pushed to pinecone for {upload_id}. Skipping pinecone push step.")
            await update_job_object(job_id, status="running", error=None, step={
                "status": "running",
                "message": "Data already pushed to pinecone, skipping pinecone push step",
                "event": "data_already_pushed_to_pinecone"
            })
            return True
        
        print("Ingesting data into pinecone...")
        await update_job_object(job_id, status="running", error=None, step={
            "status": "running",
            "message": "Ingesting data into pinecone",
            "event": "ingesting_data_into_pinecone_started"
        })

        # TODO: Ingest the data into pinecone
        try:
            await ingest_data_into_pinecone(processed_data_obj.table_of_contents, processed_data_obj.processed_data, raw_data_obj.raw_data, upload_id)
            # update the pushed_to_pinecone flag to true
            processed_data_obj.pushed_to_pinecone = True
            processed_data_obj.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
            await db.commit()
            print("Data ingested into pinecone successfully")
        except Exception as e:
            print(f"Error: Failed to ingest data into pinecone for upload_id {upload_id}: {e}")
            await update_job_object(job_id, status="failed", error="Failed to ingest data into pinecone")
            return None
        
        await update_job_object(job_id, status="completed", error=None, step={
            "status": "completed",
            "message": "Data ingested into pinecone successfully",
            "event": "ingesting_data_into_pinecone_completed"
        })
        return True
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in full_preprocess_pdf for upload_id {upload_id}: {e}")
        await update_job_object(job_id, status="failed", error=f"Database error: {str(e)}")
        return None
    except Exception as e:
        print(f"Unexpected error in full_preprocess_pdf for upload_id {upload_id}: {e}")
        await update_job_object(job_id, status="failed", error=f"Unexpected error: {str(e)}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose() # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in full_preprocess_pdf for upload_id {upload_id}: {e}")

async def preprocess_icdr_doc(job_id: str, upload_id: str):
    """This function will preprocess IDCR document."""
    async_session_gen = get_db_async()
    db = None
    try:
        db = await async_session_gen.__anext__()  # Get the actual session from the generator

        # Step 1: Create an object in the database to keep track of things
        job = models.Job(
            job_id=job_id,
            doc_id=upload_id,
            job_type="parsing",
            job_status="running",
            job_history=[
                serialize_to_json({
                    "timestamp": datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
                    "status": "running",
                    "message": "Job started",
                    "event": "job_started"
                }),
            ],
            job_data={},
            job_error=None,
            job_created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
            job_updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
        )
        db.add(job)
        await db.commit()
        print(f"Job created successfully for upload_id: {upload_id} with job_id: {job_id}")

        await update_job_object(job_id, status="running", error=None, step={
            "status": "running",
            "message": "Preprocessing IDCR document",
            "event": "preprocessing_started"
        })

        # Step 2: Download the ICDR and parse the pdf
        # raw_data_id = await download_and_parse_pdf(job_id, upload_id)
        # if not raw_data_id:
        #     print(f"Error: Failed to download and parse ICDR document for upload_id {upload_id}")
        #     await update_job_object(job_id, status="failed", error="Failed to download and parse ICDR document")
        #     return None
        # print(f"Raw data downloaded and parsed successfully for upload_id: {upload_id}")
        # await update_job_object(job_id, step={
        #     "status": "running",
        #     "message": "Raw data downloaded and parsed successfully",
        #     "event": "raw_data_downloaded_and_parsed"
        # })

        # # Step 3: Chunk the raw data
        # query = select(models.CompanyDocumentRawData).where(models.CompanyDocumentRawData.id == raw_data_id)
        # result = await db.execute(query)
        # raw_data_obj = result.scalars().first() 

        # if not raw_data_obj:
        #     print(f"Error: Raw data not found for raw_data_id {raw_data_id}")
        #     await update_job_object(job_id, status="failed", error="No raw data found for raw data id")
        #     return None
        
        # # load the raw data from s3
        # raw_data_json = load_json_from_s3(raw_data_obj.raw_data["bucket"], raw_data_obj.raw_data["key"])
        # if not raw_data_json:
        #     print(f"Error: Failed to load raw data from s3 for upload_id {upload_id}")
        #     await update_job_object(job_id, status="failed", error="Failed to load raw data from s3")
        #     return None
        # print(f"Raw data loaded successfully for upload_id: {upload_id}")
        # await update_job_object(job_id, step={
        #     "status": "running",
        #     "message": "Raw data loaded successfully",
        #     "event": "raw_data_loaded"
        # })

        # # create chunked data
        # chunked_data = create_chunk_data(copy.deepcopy(raw_data_json), {})
        # print(f"Number of chunks created: {len(chunked_data)}")

        # # save chunks to s3
        # key = f"chunked_data/{upload_id}.json"
        # if not upload_json_to_s3({"chunks": chunked_data}, DOCUMENTS_BUCKET, key):
        #     print(f"Error: Failed to upload chunked data to s3 for upload_id {upload_id}")
        #     await update_job_object(job_id, status="failed", error="Failed to upload chunked data to s3")
        #     return None
        # print(f"Chunked data uploaded successfully for upload_id: {upload_id}")
        # await update_job_object(job_id, step={
        #     "status": "running",
        #     "message": "Chunked data created and uploaded successfully",
        #     "event": "chunked_data_created_and_uploaded"
        # })

        # # Step 4: Push the chunked data to pinecone
        # await update_job_object(job_id, status="running", error=None, step={
        #     "status": "running",
        #     "message": "Pushing chunked data to pinecone",
        #     "event": "pushing_chunked_data_to_pinecone_started"
        # })
        # print("raw_data: ", raw_data_obj.raw_data)
        print("pushing chunked data to pinecone...")
        raw_data = {"key": "parsed_raw_data/01c08f3f-b4f9-4ebe-a715-c28259a81ab4.json", "bucket": "documents-modus"}
        try:
            await ingest_data_into_pinecone({}, raw_data, raw_data, upload_id)
            print("Chunked data pushed to pinecone successfully")
        except Exception as e:
            print(f"Error: Failed to push chunked data to pinecone for upload_id {upload_id}: {e}")
            await update_job_object(job_id, status="failed", error="Failed to push chunked data to pinecone")
            return None
        
        await update_job_object(job_id, status="completed", error=None, step={
            "status": "completed",
            "message": "Chunked data pushed to pinecone successfully",
            "event": "pushing_chunked_data_to_pinecone_completed"
        })
        print(f"ICDR document preprocessed successfully for upload_id: {upload_id}")
        return True
    except SQLAlchemyError as e:
        print(f"SQLAlchemyError in preprocess_icdr_doc for upload_id {upload_id}: {e}")
        await update_job_object(job_id, status="failed", error=f"Database error: {str(e)}")
        return None
    except Exception as e:
        print(f"Unexpected error in preprocess_icdr_doc for upload_id {upload_id}: {e}")
        await update_job_object(job_id, status="failed", error=f"Unexpected error: {str(e)}")
        return None
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose() # Ensure the generator's finally block is called
            except Exception as e:
                print(f"Error closing session generator in preprocess_icdr_doc for upload_id {upload_id}: {e}")