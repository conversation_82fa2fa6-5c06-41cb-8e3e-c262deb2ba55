PROMPT_COMPANY_INFO ="""
    You are provided with the text content from an IPO document. Your task is to extract key company information and return it in a structured JSON format, as described below.

    **FIELDS TO EXTRACT:**
    - cin: Corporate Identity Number
    - legal_name: Official registered name of the company
    - efiling_status: Status of electronic filings with regulatory authorities
    - incorporation_date: Date of incorporation (YYYY-MM-DD)
    - paid_up_capital: Actual capital paid by shareholders (numeric, in INR)
    - sum_of_charges: Total value of charges against company assets (numeric, in INR)
    - authorized_capital: Maximum capital authorized to issue (numeric, in INR)
    - active_compliance: Is the company compliant with regulatory requirements? (true/false)
    - registered_address: JSON with {"address_line_1": "", "address_line_2": "", "city": "", "state": "", "pincode": "", "country": ""}
    - business_address: JSON with same structure as above
    - pan: Permanent Account Number
    - website: Company website URL
    - classification: Industry classification or business category
    - status: Current operational status (active, dormant, etc.)
    - last_agm_date: Date of last Annual General Meeting (YYYY-MM-DD)
    - last_filing_date: Date of most recent regulatory filing (YYYY-MM-DD)
    - email: Official email address for communication
    - description: Brief description of company's business
    - contact_email: Email address for investor/public contact
    - contact_phone: Phone number for investor/public contact
    - lei_number: Legal Entity Identifier number
    - lei_status: Status of the LEI registration
    - lei_registration_date: Date of initial LEI registration (YYYY-MM-DD)
    - lei_last_updated_date: Date when LEI info was last updated (YYYY-MM-DD)
    - lei_next_renewal_date: Date when LEI requires renewal (YYYY-MM-DD)
    - name_history: Array of historical company names, e.g. [{"name": "...", "from": "YYYY-MM-DD", "to": "YYYY-MM-DD"}]
    - last_updated_date: Date when company info was last updated (YYYY-MM-DDTHH:MM:SS)
    - about_company: Detailed company description, operations, and strategy
    - about_industry: Information about the company's industry sector

    **GUIDELINES:**
    - If a value is missing or not found, use null.
    - For addresses, fill as many subfields as are available; if any are missing, set them to null.
    - For name_history, if not available, use null; otherwise, provide as an array of objects.
    - All numbers must be plain (no currency symbols or commas).
    - All boolean fields should be true or false.
    - Dates must be in YYYY-MM-DD (or ISO8601 for datetime fields).
    - Keep to the structure shown below.

    **OUTPUT EXAMPLE:**
    ```json
    {{
    "cin": "U12345MH2001PLC012345",
    "legal_name": "ABC Industries Limited",
    "efiling_status": "Active",
    "incorporation_date": "2001-04-23",
    "paid_up_capital": ********.00,
    "sum_of_charges": 15000000.00,
    "authorized_capital": *********.00,
    "active_compliance": true,
    "registered_address": {{
        "address_line_1": "123 Example Street",
        "address_line_2": "Industrial Area",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400001",
        "country": "India"
    }},
    "business_address": {{
        "address_line_1": "456 Corporate Avenue",
        "address_line_2": null,
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400021",
        "country": "India"
    }},
    "pan": "**********",
    "website": "https://www.abcindustries.com",
    "classification": "Manufacturing",
    "status": "Active",
    "last_agm_date": "2023-08-10",
    "last_filing_date": "2024-02-15",
    "email": "<EMAIL>",
    "description": "ABC Industries is a leading manufacturer of specialty chemicals.",
    "contact_email": "<EMAIL>",
    "contact_phone": "+91-22-12345678",
    "lei_number": "5493001KJTIIGC8Y1R12",
    "lei_status": "Issued",
    "lei_registration_date": "2019-03-05",
    "lei_last_updated_date": "2024-02-10",
    "lei_next_renewal_date": "2025-03-05",
    "name_history": [
        {{"name": "ABC Pvt Ltd", "from": "2001-04-23", "to": "2010-07-15"}},
        {{"name": "ABC Industries Ltd", "from": "2010-07-16", "to": null}}
    ],
    "last_updated_date": "2024-05-10T14:25:00",
    "about_company": "Founded in 2001, ABC Industries operates in the specialty chemicals sector...",
    "about_industry": "The specialty chemicals industry in India is growing rapidly due to demand from various sectors."
    }}
    ```
    """

PROMPT_IPO_OFFERING = """
    You are provided with the text content from an IPO document. Your task is to extract all available IPO offering information and present it in a structured JSON format as described below.

    **FIELDS TO EXTRACT:**
    - listing_date: Date the IPO listing was Dated on (convert to: YYYY-MM-DD)
    - listing_type: Type of issue (can be "Fresh Issue", "Offer for Sale", or "Fresh Issue & Offer for Sale")
    - offering_id: Make a short form ID for the IPO offering, e.g. "IPO2024-00123"
    - eligibility_type: The eligibility criteria or category for this IPO (same as in listing type, one out of [Main Board - 6(1), Main Board - 6(2), SME - 228(1), SME - 228(2)])
    - document_type: Type of document (can be one of [Draft Red Herring Prospectus (DRHP), Red Herring Prospectus (HRP), Prospectus (P)])
    - listing_details: Provide as a JSON object with two main keys, "freshIssue" and "offerForSale". Each should contain key information as available (if not present, set as null):
        - fresh_issue: {
            "nubmer_of_shares": (numeric, number of shares issued in Fresh Issue),
            "face_value": (numeric, face value per share for Fresh Issue),
            "aggregating_amount": (numeric, total amount for Fresh Issue in INR, no currency symbols)
        }
        - offer_for_sale: {
            "number_of_shares": (numeric, number of shares in Offer for Sale),
            "face_value": (numeric, face value per share for Offer for Sale),
            "aggregating_amount": (numeric, total amount for Offer for Sale in INR, no currency symbols)
        }
    - If data for freshIssue or offerForSale is not available, use null for that key.
    - offer_size: Total OFFER SIZE in INR (numeric, no currency symbols)

    - data: This is a nested JSON object with two keys:
        - object_of_issue: If listing_type contains "Fresh Issue", extract and present the following:
            - total_amount: Total amount (numeric, in INR crores, no currency symbol)
            - number_of_purposes: Number of purposes mentioned
            - issue_type: Type of issue (Fresh Issue)
            - details: An array of objects, each with:
                - reason: The reason/purpose of the issue
                - amount: Amount in INR crores (numeric)
        - sharing_share_holder: If listing_type contains "Offer for Sale", extract and present the following:
            - total_shareholders: Number of selling shareholders
            - total_shares_selling: Total number of shares being sold (numeric)
            - total_current_shares: Total number of shares before issue (numeric)
            - details: An array of objects, each with:
                - shareholder_name: Name of shareholder
                - current_shares: Number of shares held before selling (numeric)
                - current_shareholding_percent: Percentage shareholding before selling (numeric, as percent)
                - shares_selling: Number of shares being sold (numeric)
                - resultant_shareholding_percent: Percentage shareholding after selling (numeric, as percent)
                - percent_of_shares_offered: Percentage of shares offered by this shareholder (numeric, as percent)
                - percent_of_offer: Percentage of total offer this shareholder represents (numeric, as percent)
                - waca: Weighted Average Cost of Acquisition (numeric, e.g. 10.0)

    **GUIDELINES:**
    - If a value is missing or not found, use null.
    - For listing_details, extract as much structured data as possible; if not found, use {}.
    - All numbers must be plain (no currency symbols or commas).
    - All dates must be in YYYY-MM-DD format. If the date is in any other format, convert it to YYYY-MM-DD.
    - For data.object_of_issue and data.sharing_share_holder, if the section is not present or not applicable, use null.
    - Only extract the fields above; do not infer additional data.
    - Also convert all the money values to INR numeric values (no currency symbols).

    **OUTPUT EXAMPLE:**
    ```json
    {{
    "listing_date": "2024-06-05",
    "listing_type": "Fresh Issue & Offer for Sale",
    "offering_id": "IPO2024-00123",
    "eligibility_type": "Main Board - 6(1)",
    "document_type": "Red Herring Prospectus",
    "listing_details": {{
        "fresh_issue": {{
            "number_of_shares": 2500000,
            "face_value": 10.0,
            "aggregating_amount": 2********.0
        }},
        "offer_for_sale": {{
            "number_of_shares": 5000000,
            "face_value": 10.0,
            "aggregating_amount": ********0.0
        }}
    }},
    "offer_size": 15********.00,
    "data": {{
        "object_of_issue": {{
        "total_amount": 250,
        "number_of_purposes": 5,
        "issue_type": "Fresh Issue",
        "details": [
            {{
            "reason": "Funding for expansion of cloud infrastructure",
            "amount": 150
            }},
            {{
            "reason": "Investment in R&D for AI solutions",
            "amount": 50
            }},
            {{
            "reason": "Working capital requirements",
            "amount": 30
            }},
            {{
            "reason": "Repayment of certain borrowings",
            "amount": 20
            }},
            {{
            "reason": "General corporate purposes",
            "amount": 0
            }}
        ]
        }},
        "sharing_share_holder": {{
            "total_shareholders": 2,
            "total_shares_selling": 25000000,
            "total_current_shares": ********,
            "details": [
                {{
                "shareholder_name": "TechServe Ventures Pvt Ltd",
                "current_shares": 30000000,
                "current_shareholding_percent": 60.00,
                "shares_selling": 15000000,
                "resultant_shareholding_percent": 30.00,
                "percent_of_shares_offered": 20.00,
                "percent_of_offer": 60.00,
                "waca": 10.0
                }},
                {{
                "shareholder_name": "Innovation Capital Fund",
                "current_shares": 20000000,
                "current_shareholding_percent": 40.00,
                "shares_selling": 10000000,
                "resultant_shareholding_percent": 20.00,
                "percent_of_shares_offered": 10.00,
                "percent_of_offer": 40.00,
                "waca": 12.0
                }}
            ]
            }}
    }}
    }}
    ```
"""


PROMPT_PEER_GROUPS_INFO = """
    You are given the text from an IPO document, which includes a section on the peer group (comparable listed companies) used for valuation or benchmarking. Your task is to extract information for each peer company and present the data in a structured JSON format, as specified below.

    **FIELDS TO EXTRACT (for each peer company):**
    - is_self: true if this entry refers to the issuer itself, otherwise false
    - name: Name of the peer company
    - revenue_from_operations_in_million_inr: Revenue from operations in INR millions (numeric, no currency symbols)
    - face_value_per_equity_share: Face value per equity share in INR (numeric)
    - closing_price_inr: Closing share price in INR (numeric)
    - closing_price_date: Date of closing price (YYYY-MM-DD)
    - pe_ratio: Price/Earnings ratio (numeric)
    - eps_basic_inr: Basic Earnings Per Share in INR (numeric)
    - eps_diluted_inr: Diluted Earnings Per Share in INR (numeric)
    - ronw_percent: Return on Net Worth (%) (numeric)
    - nav_per_equity_share_inr: Net Asset Value per equity share in INR (numeric)

    **GUIDELINES:**
    - Provide data for all peer companies found, including the issuer if shown in the peer group table.
    - If the data for any field is missing, use null.
    - All numbers must be plain (no currency symbols or commas).
    - All dates must be in YYYY-MM-DD format.

    **OUTPUT EXAMPLE:**  
    If there are three peer companies (including the issuer itself as the first):

    ```json
    [
    {{
        "is_self": true,
        "name": "ABC Industries Limited",
        "revenue_from_operations_in_million_inr": 4302.15,
        "face_value_per_equity_share": 10.00,
        "closing_price_inr": 520.45,
        "closing_price_date": "2024-05-31",
        "pe_ratio": 23.12,
        "eps_basic_inr": 22.51,
        "eps_diluted_inr": 22.31,
        "ronw_percent": 16.07,
        "nav_per_equity_share_inr": 141.36
    }},
    {{
        "is_self": false,
        "name": "DEF Chemicals Ltd",
        "revenue_from_operations_in_million_inr": 5500.00,
        "face_value_per_equity_share": 10.00,
        "closing_price_inr": 720.85,
        "closing_price_date": "2024-05-31",
        "pe_ratio": 26.94,
        "eps_basic_inr": 26.76,
        "eps_diluted_inr": 26.50,
        "ronw_percent": 18.14,
        "nav_per_equity_share_inr": 164.53
    }},
    {{
        "is_self": false,
        "name": "GHI Polymers Pvt Ltd",
        "revenue_from_operations_in_million_inr": 3287.21,
        "face_value_per_equity_share": 5.00,
        "closing_price_inr": 248.70,
        "closing_price_date": "2024-05-31",
        "pe_ratio": 19.04,
        "eps_basic_inr": 13.06,
        "eps_diluted_inr": 12.94,
        "ronw_percent": 11.59,
        "nav_per_equity_share_inr": 111.23
    }}
    ]
    ```
"""

PROMPT_BOOKRUNNING_MANAGERS = """
    You are given the text from an IPO document. Your task is to extract information about each Book Running Lead Manager (BRLM) involved in the IPO and present the data in a structured JSON format, as specified below.

    **FIELDS TO EXTRACT (for each BRLM):**
    - name: Name of the Book Running Lead Manager
    - unique_id: Unique identifier for the manager (if available)
    - address: Provide as a JSON object: {"address_line_1": "", "address_line_2": "", "city": "", "state": "", "pincode": "", "country": ""}
    - mobile: Mobile phone number
    - email: General email address
    - grievance_email: Email address for grievances, if specified
    - website: Official website URL
    - contact_person: JSON object, e.g. {"name": "", "designation": "", "email": "", "mobile": ""}
    - sebi_registration_number: SEBI registration number for the BRLM

    **GUIDELINES:**
    - Provide data for all Book Running Lead Managers found in the document.
    - If the data for any field is missing, use null.
    - For address and contact_person, fill as many subfields as possible; set any missing ones to null.

    **OUTPUT EXAMPLE:**  
    If there are two BRLMs:

    ```json
    [
    {{
        "name": "Axis Capital Limited",
        "unique_id": "AXIS-001",
        "address": {{
        "address_line_1": "1st Floor, Axis House",
        "address_line_2": "Bombay Dyeing Mills Compound, Pandurang Budhkar Marg",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400025",
        "country": "India"
        }},
        "mobile": "+91-22-43252525",
        "email": "<EMAIL>",
        "grievance_email": "<EMAIL>",
        "website": "https://www.axiscapital.co.in",
        "contact_person": {{
        "name": "Rahul Jain",
        "designation": "Vice President",
        "email": "<EMAIL>",
        "mobile": "+91-22-43252526"
        }},
        "sebi_registration_number": "INM000012029",
        "logo": "https://www.axiscapital.co.in/logo.png"
    }},
    {{
        "name": "ICICI Securities Limited",
        "unique_id": "ICICI-002",
        "address": {{
        "address_line_1": "ICICI Centre, H.T. Parekh Marg",
        "address_line_2": "Churchgate",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400020",
        "country": "India"
        }},
        "mobile": "+91-22-********",
        "email": "<EMAIL>",
        "grievance_email": "<EMAIL>",
        "website": "https://www.icicisecurities.com",
        "contact_person": {{
        "name": "Anjali Mehta",
        "designation": "AVP, Investment Banking",
        "email": "<EMAIL>",
        "mobile": "+91-22-********"
        }},
        "sebi_registration_number": "INM000011179",
        "logo": "https://www.icicisecurities.com/logo.png"
    }}
    ]
    ```
    """

PROMPT_REGISTRAR =  """
    You are given the text from an IPO document. Your task is to extract information about the Registrar for the IPO and present the data in a structured JSON format as specified below.

    **FIELDS TO EXTRACT (for the Registrar):**
    - name: Name of the Registrar
    - unique_id: Unique identifier for the registrar (if available)
    - address: Provide as a JSON object: {"address_line_1": "", "address_line_2": "", "city": "", "state": "", "pincode": "", "country": ""}
    - mobile: Mobile phone number
    - email: General email address
    - grievance_email: Email address for grievances, if specified
    - website: Official website URL
    - contact_person: JSON object, e.g. {"name": "", "designation": "", "email": "", "mobile": ""}
    - sebi_registration_number: SEBI registration number for the Registrar

    **GUIDELINES:**
    - If there are multiple registrars, extract data for all.
    - If the data for any field is missing, use null.
    - For address and contact_person, fill as many subfields as possible; set any missing ones to null.
    **OUTPUT EXAMPLE:**

    ```json
    [
    {{
        "name": "KFin Technologies Limited",
        "unique_id": "KFIN-001",
        "address": {{
        "address_line_1": "Selenium Tower B, Plot No. 31 & 32",
        "address_line_2": "Financial District, Nanakramguda",
        "city": "Hyderabad",
        "state": "Telangana",
        "pincode": "500032",
        "country": "India"
        }},
        "mobile": "+91-40-67162222",
        "email": "<EMAIL>",
        "grievance_email": "<EMAIL>",
        "website": "https://www.kfintech.com",
        "contact_person": {{
        "name": "Priya Sharma",
        "designation": "Senior Manager",
        "email": "<EMAIL>",
        "mobile": "+91-40-67162200"
        }},
        "sebi_registration_number": "INR000000221"
    }}
    ]
    ````
    """

PROMPT_LEGAL_COUNSEL = """
    You are given the text from an IPO document. Your task is to extract information about each Legal Counsel involved in the IPO and present the data in a structured JSON format as specified below.

    **FIELDS TO EXTRACT (for each Legal Counsel):**
    - type: Type of counsel (e.g., "Domestic Legal Counsel", "International Legal Counsel", "Issuer's Counsel", etc.)
    - name: Name of the Legal Counsel firm
    - unique_id: Unique identifier for the legal counsel (if available)
    - address: Provide as a JSON object: {"address_line_1": "", "address_line_2": "", "city": "", "state": "", "pincode": "", "country": ""}
    - mobile: Mobile phone number
    - email: General email address
    - grievance_email: Email address for grievances, if specified
    - website: Official website URL
    - contact_person: JSON object, e.g. {"name": "", "designation": "", "email": "", "mobile": ""}
    - sebi_registration_number: SEBI registration number for the Legal Counsel (if available)
    - logo: Logo URL (if available)

    **GUIDELINES:**
    - Provide data for all Legal Counsels mentioned in the document.
    - If the data for any field is missing, use null.
    - For address and contact_person, fill as many subfields as possible; set any missing ones to null.

    **OUTPUT EXAMPLE:**  
    If there are two legal counsels:

    ```json
    [
    {{
        "type": "Domestic Legal Counsel",
        "name": "Shardul Amarchand Mangaldas & Co.",
        "unique_id": "SAMCO-001",
        "address": {{
        "address_line_1": "AMarchand Towers, 1st Floor",
        "address_line_2": "Barakhamba Road, Connaught Place",
        "city": "New Delhi",
        "state": "Delhi",
        "pincode": "110001",
        "country": "India"
        }},
        "mobile": "+91-11-12345678",
        "email": "<EMAIL>",
        "grievance_email": "<EMAIL>",
        "website": "https://www.amsshardul.com",
        "contact_person": {{
        "name": "Sonal Shah",
        "designation": "Partner",
        "email": "<EMAIL>",
        "mobile": "+91-11-87654321"
        }},
        "sebi_registration_number": "INL000000001",
        "logo": "https://www.amsshardul.com/logo.png"
    }},
    {{
        "type": "International Legal Counsel",
        "name": "Linklaters LLP",
        "unique_id": "LINK-001",
        "address": {{
        "address_line_1": "One Silk Street",
        "address_line_2": null,
        "city": "London",
        "state": null,
        "pincode": "EC2Y 8HQ",
        "country": "United Kingdom"
        }},
        "mobile": "+44-20-7456-2000",
        "email": "<EMAIL>",
        "grievance_email": null,
        "website": "https://www.linklaters.com",
        "contact_person": {{
        "name": "David Green",
        "designation": "Partner",
        "email": "<EMAIL>",
        "mobile": "+44-20-7456-2001"
        }},
        "sebi_registration_number": null,
        "logo": "https://www.linklaters.com/logo.png"
    }}
    ]
    ```
    """

PROMPT_STATUTORY_AUDITORS = """
    You are given the text from an IPO document. Your task is to extract information about each Statutory Auditor involved in the IPO and present the data in a structured JSON format as specified below.

    **FIELDS TO EXTRACT (for each Statutory Auditor):**
    - name: Name of the Statutory Auditor or Audit Firm
    - unique_id: Unique identifier for the auditor (if available)
    - address: Provide as a JSON object: {"address_line_1": "", "address_line_2": "", "city": "", "state": "", "pincode": "", "country": ""}
    - mobile: Mobile phone number
    - email: General email address
    - grievance_email: Email address for grievances, if specified
    - website: Official website URL
    - contact_person: JSON object, e.g. {"name": "", "designation": "", "email": "", "mobile": ""}
    - sebi_registration_number: SEBI registration number for the Auditor (if available)
    - logo: Logo URL (if available)

    **GUIDELINES:**
    - Provide data for all Statutory Auditors mentioned in the document.
    - If the data for any field is missing, use null.
    - For address and contact_person, fill as many subfields as possible; set any missing ones to null.

    **OUTPUT EXAMPLE:**  
    If there are two statutory auditors:

    ```json
    [
    {{
        "name": "Walker Chandiok & Co LLP",
        "unique_id": "WCCLLP-001",
        "address": {{
        "address_line_1": "L-41, Connaught Circus",
        "address_line_2": null,
        "city": "New Delhi",
        "state": "Delhi",
        "pincode": "110001",
        "country": "India"
        }},
        "mobile": "+91-11-42787070",
        "email": "<EMAIL>",
        "grievance_email": "<EMAIL>",
        "website": "https://www.walkerchandiok.com",
        "contact_person": {{
        "name": "S. Kumar",
        "designation": "Partner",
        "email": "<EMAIL>",
        "mobile": "+91-11-42787071"
        }},
        "sebi_registration_number": "INM000012345",
        "logo": "https://www.walkerchandiok.com/logo.png"
    }},
    {{
        "name": "Deloitte Haskins & Sells LLP",
        "unique_id": "DELOITTE-001",
        "address": {{
        "address_line_1": "Indiabulls Finance Centre, Tower 3",
        "address_line_2": "Senapati Bapat Marg, Elphinstone (W)",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400013",
        "country": "India"
        }},
        "mobile": "+91-22-********",
        "email": "<EMAIL>",
        "grievance_email": null,
        "website": "https://www.deloitte.com",
        "contact_person": {{
        "name": "Arun Mishra",
        "designation": "Partner",
        "email": "<EMAIL>",
        "mobile": "+91-22-********"
        }},
        "sebi_registration_number": "INM000012678",
        "logo": "https://www.deloitte.com/logo.png"
    }}
    ]
    ```
    """

PROMPT_CHARTERED_ACCOUNTANTS = """
You are given the text from an IPO document. Your task is to extract information about each Independent Chartered Accountant (ICA) involved in the IPO and present the data in a structured JSON format as specified below.

**FIELDS TO EXTRACT (for each Independent Chartered Accountant):**
- name: Name of the Independent Chartered Accountant or firm
- unique_id: Unique identifier for the accountant (if available)
- address: Provide as a JSON object: {"address_line_1": "", "address_line_2": "", "city": "", "state": "", "pincode": "", "country": ""}
- mobile: Mobile phone number
- email: General email address
- grievance_email: Email address for grievances, if specified
- website: Official website URL
- contact_person: JSON object, e.g. {"name": "", "designation": "", "email": "", "mobile": ""}
- sebi_registration_number: SEBI registration number for the ICA (if available)
- logo: Logo URL (if available)

**GUIDELINES:**
- Provide data for all Independent Chartered Accountants mentioned in the document.
- If the data for any field is missing, use null.
- For address and contact_person, fill as many subfields as possible; set any missing ones to null.

**OUTPUT EXAMPLE:**  
If there are two ICAs:

```json
[
  {{
    "name": "KPMG India Private Limited",
    "unique_id": "KPMG-001",
    "address": {{
      "address_line_1": "Lodha Excelus, 1st Floor",
      "address_line_2": "Apollo Mills Compound, N.M. Joshi Marg",
      "city": "Mumbai",
      "state": "Maharashtra",
      "pincode": "400011",
      "country": "India"
    }},
    "mobile": "+91-22-********",
    "email": "<EMAIL>",
    "grievance_email": null,
    "website": "https://home.kpmg/in/en/home.html",
    "contact_person": {{
      "name": "Rakesh Patel",
      "designation": "Partner",
      "email": "<EMAIL>",
      "mobile": "+91-22-********"
    }},
    "sebi_registration_number": "INM000012345",
    "logo": "https://home.kpmg/content/dam/kpmg/in/images/logo.png"
  }},
  {{
    "name": "Grant Thornton Bharat LLP",
    "unique_id": "GTB-002",
    "address": {{
      "address_line_1": "6th Floor, Worldmark 2",
      "address_line_2": "Aerocity",
      "city": "New Delhi",
      "state": "Delhi",
      "pincode": "110037",
      "country": "India"
    }},
    "mobile": "+91-11-42787000",
    "email": "<EMAIL>",
    "grievance_email": "<EMAIL>",
    "website": "https://www.grantthornton.in",
    "contact_person": {{
      "name": "Anu Sharma",
      "designation": "Director",
      "email": "<EMAIL>",
      "mobile": "+91-11-42787001"
    }},
    "sebi_registration_number": "INM000012678",
    "logo": "https://www.grantthornton.in/logo.png"
  }}
]
```
"""

PROMPT_TABLE_TO_JSON = """
    **Task:**
    You are given financial data in Markdown format, including tables, captions, and headings for several years. Your job is to extract **all** data into a hierarchical JSON, grouped by year and major categories, using a flat section/subsection style (as shown below). **No information should be omitted or summarized. Every number in every table for every year must be present.**

    ---

    ### **Instructions**

    1. **Identify all years in the tables**. The top-level keys must be the years (e.g., `"2025"`, `"2024"`, `"2023"`).
    2. For each year, use these **main keys** (customize slightly if the table names are different):

    * `"Assets and Liabilities"`
    * `"Profit and Loss"`
    * `"Cash Flow"`
    3. **Within each category**, structure the data as nested dictionaries to represent the main groupings and sub-groupings (as shown in the sample below).

    * Use nested dictionaries for groupings (e.g., `"Non-Current Assets"`, `"Current Assets"`, `"Financial Assets"`, etc.)
    * For lists of items (e.g., “Trade payables”), use objects with each label as a key.
    * **Every numeric value from the original tables must be present at its correct nesting and key.**
    4. **For missing or blank values in the table, use `null`.**
    5. **No information should be omitted, summarized, or left as “to fill.”**
    6. **Order keys and values as per their appearance in the input.**
    7. **Strictly output only the JSON** in ```json ``` block only.
    8. **No abbreviations, no text, just valid json**

    ---

    ### **Output JSON Structure (Strict Template)**

    ```json
    {{
    "2025": {{
        "Assets and Liabilities": {{
        "Assets": {{
            "Non-Current Assets": {{
            "Property, Plant & Equipment": 42644.1,
            "Capital Work in Progress": 16167.8,
            // ...continue as in the data, with nesting for "Financial Assets" as a dictionary
            }},
            "Current Assets": {{
            // ...
            }}
        }},
        "Equity and Liabilities": {{
            "Equity": {{
            // ...
            }},
            "Liabilities": {{
            "Non-Current Liabilities": {{
                // ...
            }},
            "Current Liabilities": {{
                // ...
            }}
            }}
        }}
        }},
        "Profit and Loss": {{
        "Revenue": {{
            // ...
        }},
        "Expenses": {{
            // ...
        }},
        // Other items...
        }},
        "Cash Flow": {{
        "Cash Flows from Operating Activities": {{
            // ...
        }},
        "Cash Flows from Investing Activities": {{
            // ...
        }},
        "Cash Flows from Financing Activities": {{
            // ...
        }},
        // Other cash/cash-equivalents groupings
        }}
    }},
    "2024": {{
        // Repeat structure for all years, filling all values for every year
    }},
    "2023": {{
        // ...
    }}
    }}
    ```

    **Strictly follow this structure, maintaining all groupings as nested dictionaries. Do not omit or abbreviate any data from the input tables. Fill in every value, for every year, under the correct key. If a grouping in the data does not exist for a year, omit that key for that year.**

    ** The field names and the structure can change based on the input data, but the overall structure must remain consistent with the provided template.**
    """

PROMPT_FILING_FINANCIALS_YEAR = """
    You are provided with financial data for a company in JSON format, representing key information typically included in IPO filings. Your task is to accurately extract and map all available data into a structured financial record, following the schema below. This data may include balance sheet items, profit & loss, cash flow, revenue breakdowns, tax details, and auditor comments.

    **FIELDS TO EXTRACT:**
    Fill in as many fields as possible for each record. Use null if a value is missing.

    - nature: Nature of financial statement (e.g., "STANDALONE", "CONSOLIDATED")
    - financial_year: Financial year for which the statement is prepared (format: YYYY-MM-DD, use last day of the year if only year is given)
    - stated_on: Date for which the financial statement is prepared (YYYY-MM-DD)
    - filing_type: Type of filing (e.g., "Annual", "Quarterly", "Half-yearly")
    - filing_standard: Accounting standard used (e.g., "Ind AS", "GAAP", "IFRS")

    **BALANCE SHEET (ASSETS)**
    - tangible_assets: Property, plant and equipment (net)
    - producing_properties
    - intangible_assets: Intangible assets (net)
    - preproducing_properties
    - tangible_assets_capital_work_in_progress
    - intangible_assets_under_development
    - noncurrent_investments
    - deferred_tax_assets_net
    - foreign_curr_monetary_item_trans_diff_asset_account
    - long_term_loans_and_advances
    - other_noncurrent_assets
    - current_investments
    - inventories
    - trade_receivables
    - cash_and_bank_balances
    - short_term_loans_and_advances
    - other_current_assets
    - given_assets_total: Total assets as given in financial statements

    **BALANCE SHEET (LIABILITIES & EQUITY)**
    - share_capital
    - reserves_and_surplus
    - money_received_against_share_warrants
    - share_application_money_pending_allotment
    - deferred_government_grants
    - minority_interest
    - long_term_borrowings
    - deferred_tax_liabilities_net
    - foreign_curr_monetary_item_trans_diff_liability_account
    - other_long_term_liabilities
    - long_term_provisions
    - short_term_borrowings
    - trade_payables
    - other_current_liabilities
    - short_term_provisions
    - given_liabilities_total: Total liabilities as given in financial statements

    **CALCULATED BALANCE SHEET ITEMS**
    - total_equity
    - total_current_liabilities
    - total_non_current_liabilities
    - net_fixed_assets
    - total_current_assets
    - capital_wip
    - total_debt
    - gross_fixed_assets
    - trade_receivable_exceeding_six_months

    **PROFIT & LOSS STATEMENT**
    - net_revenue
    - total_cost_of_materials_consumed
    - total_purchases_of_stock_in_trade
    - total_changes_in_inventories_or_finished_goods
    - total_employee_benefit_expense
    - total_other_expenses
    - operating_profit
    - other_income
    - depreciation
    - profit_before_interest_and_tax
    - interest
    - profit_before_tax_and_exceptional_items_before_tax
    - exceptional_items_before_tax
    - profit_before_tax
    - income_tax
    - profit_for_period_from_continuing_operations
    - profit_from_discontinuing_operation_after_tax
    - minority_interest_and_profit_from_associates_and_joint_ventures
    - profit_after_tax
    - total_operating_cost

    **REVENUE BREAKDOWN**
    - revenue_from_operations
    - revenue_from_interest
    - revenue_from_other_financial_services
    - revenue_from_sale_of_products
    - revenue_from_sale_of_services
    - other_operating_revenues

    **TAX COLLECTIONS**
    - excise_duty
    - service_tax_collected
    - other_duties_taxes_collected

    **GEOGRAPHIC REVENUE SPLIT**
    - sale_of_goods_manufactured_domestic
    - sale_of_goods_traded_domestic
    - sale_or_supply_of_services_domestic
    - sale_or_supply_of_services_export
    - sale_of_goods_manufactured_export
    - sale_of_goods_traded_export

    **DEPRECIATION DETAILS**
    - depreciation_amortisation
    - depletion
    - depreciation_and_amortization

    **CASH FLOW STATEMENT**
    - profit_before_tax_cf
    - adjustment_for_finance_cost_and_depreciation
    - adjustment_for_current_and_non_current_assets
    - adjustment_for_current_and_non_current_liabilities
    - other_adjustments_in_operating_activities
    - cash_flows_from_used_in_operating_activities
    - cash_outflow_from_purchase_of_assets
    - cash_inflow_from_sale_of_assets
    - income_from_assets
    - other_adjustments_in_investing_activities
    - cash_flows_from_used_in_investing_activities
    - cash_outflow_from_repayment_of_capital_and_borrowings
    - cash_inflow_from_raisng_capital_and_borrowings
    - interest_and_dividends_paid
    - other_adjustments_in_financing_activities
    - cash_flows_from_used_in_financing_activities
    - incr_decr_in_cash_cash_equv_before_effect_of_excg_rate_changes
    - adjustments_to_cash_and_cash_equivalents
    - incr_decr_in_cash_cash_equv
    - cash_flow_statement_at_end_of_period

    **OTHER EXPENSE DETAILS**
    - managerial_remuneration
    - payment_to_auditors
    - insurance_expenses
    - power_and_fuel

    **AUDITOR INFORMATION**
    - auditor_name
    - auditor_firm_name
    - pan
    - membership_number
    - firm_registration_number
    - auditor_address: Provide as JSON, e.g. {{"address_line_1": "", ...}}
    - report_has_adverse_remarks: true/false
    - auditor_comments
    - auditor_additional

    **FLEXIBLE FIELDS**
    - additional_fields: For any extra financial fields not covered above, provide as JSON key-value pairs.
    - labels_map: Provide a mapping from labels found in the original data to the standardized field names above.

    **GUIDELINES:**
    - Map every possible field from the source data to the corresponding standardized field above. If a field is not available or not applicable, set its value as null.
    - Numeric fields must have no currency symbols or commas, and use two decimal places if possible.
    - Dates must be in YYYY-MM-DD format.
    - For boolean fields, use true or false.
    - For JSON fields, use the specified structure.
    - For additional data that doesn't fit the above schema, include it in `additional_fields`.
    - In `labels_map`, provide the original label or heading from the source data and the field name it was mapped to.

    **OUTPUT EXAMPLE:**  
    ```json
    {{
    "nature": "Standalone",
    "financial_year": "2024-03-31",
    "stated_on": "2024-03-31",
    "filing_type": "Annual",
    "filing_standard": "Ind AS",
    "tangible_assets": *********.00,
    "intangible_assets": 1500000.00,
    "trade_receivables": ********.00,
    "cash_and_bank_balances": ********.00,
    "share_capital": ********.00,
    "reserves_and_surplus": *********.00,
    "long_term_borrowings": ********.00,
    "trade_payables": ********.00,
    "net_revenue": *********.00,
    "operating_profit": ********.00,
    "profit_after_tax": ********.00,
    "auditor_name": "Walker Chandiok & Co LLP",
    "auditor_firm_name": "Walker Chandiok & Co LLP",
    "pan": "**********",
    "auditor_address": {{
        "address_line_1": "L-41, Connaught Circus",
        "address_line_2": null,
        "city": "New Delhi",
        "state": "Delhi",
        "pincode": "110001",
        "country": "India"
    }},
    "report_has_adverse_remarks": false,
    "auditor_comments": "No material weaknesses identified.",
    "auditor_additional": null,
    "additional_fields": {{
        "custom_ratio_1": 2.35
    }},
    "labels_map": {{
        "Total Assets": "given_assets_total",
        "Issued Capital": "share_capital",
        "PAT": "profit_after_tax"
    }}
    }}
    ```
    - **Very IMPORTANT:** Every number/amount must be a plain number in INR (no currency symbols, no commas).
    - **Do not abbreviate, summarize, or refer the user to fill in missing data.** If the source table has blanks, show `null` as the value.
    - **Do not output anything but the JSON in the structure above.**
    """

PROMPT_COMPLIANCE_CHECK = """
    You are an excellent SEBI compliance officer. You are given the requirements and the reference to ICDR clause and regulation. You need to find if the given IPO document meets the requirements or not (Compliant or Non-Compliant). If it is compliant, you need to provide the reference to the clause and regulation. If it is non-compliant, you need to provide the reason for non-compliance. Return the result in a structured JSON format as specified below.
    ```json
    {{
        "compliant": true/false,
        "reason": "If non-compliant, provide the reason for non-compliance and also mention what is missing." or null,
        "disclosure": "Company's disclosure or statement regarding compliance" or null,
        "reference": {{
            "page_number": "Page number where the requirement is met in the IPO document",
            "icdr_clause": "ICDR clause number",
            "icdr_regulation": "ICDR regulation number"
        }} or {{}},
    }}
    ```
        **GUIDELINES:**
        - If the requirement is met, set `compliant` to true and provide the page number, ICDR clause, and regulation in the `reference` field.
        - If the requirement is not met, set `compliant` to false and provide a clear reason for non-compliance in the `reason` field.
        - Ensure that all references are accurate and correspond to the actual content in the IPO document.
    """