import re
import async<PERSON>
from langchain_pinecone import PineconeVectorStore
from langchain_openai import OpenAIEmbeddings
from langchain.schema import Document
from langchain.embeddings.base import Embeddings
from typing import List

from .file_upload import *
from .utils import *

def preprocess_chunk(text: str):
    """This function preprocesses a chunk of text"""
    text = re.sub(r'"|"', '"', text)  # normalize quotes
    text = text.strip()
    text = re.sub(r'\s+', ' ', text)  # collapse multiple spaces
    text = re.sub(r'\n+', '\n', text)  # collapse multiple newlines
    return text  # Return the processed text

async def upload_documents_in_batches_async(
    documents: List[Document],
    index_name: str,
    namespace: str,
    embedding: Embeddings, # Pass embedding as an argument
    vector_store: PineconeVectorStore,
    batch_size: int = 50,
) -> None:
    """
    Asynchronously upload documents to Pinecone in smaller batches to avoid size limits.
    """
    total_docs = len(documents)
    batch_tasks = []

    for i in range(0, total_docs, batch_size):
        end_idx = min(i + batch_size, total_docs)
        print(f"Preparing batch {i//batch_size + 1}/{(total_docs+batch_size-1)//batch_size}: documents {i} to {end_idx-1}")

        batch_docs = documents[i:end_idx]

        # Create an asynchronous task for each batch upload
        task = vector_store.afrom_documents(
            batch_docs,
            index_name=index_name,
            embedding=embedding,
            namespace=namespace
        )
        batch_tasks.append(task)

    print(f"Starting upload of {len(batch_tasks)} batches concurrently...")
    # Run all batch upload tasks concurrently
    await asyncio.gather(*batch_tasks)
    print("All batches successfully uploaded.")

async def ingest_data_into_pinecone(index_data: dict, s3_data: dict, s3_raw_data: dict, doc_id: str):
    """This function ingests the data into pinecone"""

    # load the raw data from s3
    all_pages = load_json_from_s3(s3_raw_data["bucket"], s3_raw_data["key"]).get("pages", [])
    if not all_pages:
        print(f"Error: Failed to load raw data from s3 for upload_id {doc_id}")
        return None

    # load the processed data from s3
    processed_data = load_json_from_s3(s3_data["bucket"], s3_data["key"])
    if not processed_data:
        print(f"Error: Failed to load processed data from s3 for upload_id {doc_id}")
        return None
    
    # load the chunked data from s3
    key = f"chunked_data/{doc_id}.json"
    chunked_data = load_json_from_s3(s3_data["bucket"], key).get("chunks", [])
    if not chunked_data:
        print(f"Error: Failed to load chunked data from s3 for upload_id {doc_id}")
        return None
    print(f"Number of chunks loaded: {len(chunked_data)}")
    
    sections = processed_data.get("sections",[])
    
    embeddings = OpenAIEmbeddings()
    index_name = "modus2"
    vector_store = PineconeVectorStore(
        index_name=index_name,
        embedding=embeddings
    )
    namespace_vector_retriever = "rag_retriever"

    documents = []
    for chunk in chunked_data:
        documents.append(
            Document(
                page_content=chunk["md"],
                metadata={
                    "page": chunk["page"],
                    "section": chunk["section"],
                    "chunk_id": chunk["chunk_id"],
                    "md": chunk["md"],
                    "subsection": chunk["subsection"],
                    "doc_id": doc_id
                }
            )
        )

    print("Number of documents: ", len(documents))
    # Call the function with your documents
    await upload_documents_in_batches_async(documents, index_name, namespace_vector_retriever, embeddings, vector_store, batch_size=50)
    print("All documents successfully uploaded.")

    # For now we are skipping the hierarchical retriever
    print("Skipping hierarchical retriever upload for now.")
    # Now we need to upload the summaries
    # name_space_secton = "hier_section_retriever1"
    # name_space_subsection = "hier_subsection_retriever1"
    # name_space_subsubsection = "hier_subsubsection_retriever1"

    # summarized_data = sections

    # # 1. Subsubsections
    # subsubsection_data = []
    # for section in summarized_data:
    #     section_name = section["name"]
    #     for subsection in section.get("subsections", []):
    #         subsection_name = subsection["name"]
    #         for subsubsection in subsection.get("subsubsections", []):
    #             subsubsection_data.append(
    #                 {
    #                     "section": section_name,
    #                     "subsection": subsection_name,
    #                     "subsubsection": subsubsection["name"],
    #                     "summary": subsubsection.get("summary", ""),
    #                     "md": subsubsection["raw_text"],
    #                     "start_page": subsubsection["start_page"],
    #                     "end_page": subsubsection["end_page"]
    #                 }
    #             )

    # print("Number of subsubsections: ", len(subsubsection_data))

    # # 2. Subsections
    # subsection_data = []
    # for section in summarized_data:
    #     section_name = section["name"]
    #     for subsection in section.get("subsections", []):
    #         subsection_data.append(
    #             {
    #                 "section": section_name,
    #                 "subsection": subsection["name"],
    #                 "summary": subsection.get("summary", ""),
    #                 "start_page": subsection["page_range"][0],
    #                 "end_page": subsection["page_range"][1],
    #                 "md": subsection.get("raw_text", "")
    #             }
    #         )

    # print("Number of subsections: ", len(subsection_data))

    # # 3. Sections
    # section_data = []
    # for i, section in enumerate(summarized_data):
    #     print(i, section["name"])
    #     section_data.append(
    #         {
    #             "section": section["name"],
    #             "summary": section.get("summary", ""),
    #             "start_page": section["page_range"][0],
    #             "end_page": section["page_range"][1]
    #         }
    #     )

    # print("Number of sections: ", len(section_data))

    # #pushing subsubsectiond data first
    # subsubsectiondocs = []
    # for subsubsection in subsubsection_data:
    #     subsubsectiondocs.append(
    #         Document(
    #             page_content=subsubsection["summary"],
    #             metadata={
    #                 "section": subsubsection["section"],
    #                 "subsection": subsubsection["subsection"],
    #                 "subsubsection": subsubsection["subsubsection"],
    #                 "md": subsubsection["md"],
    #                 "start_page": subsubsection["start_page"],
    #                 "end_page": subsubsection["end_page"],
    #                 "doc_id": doc_id
    #             }
    #         )
    #     )
    
    # await upload_documents_in_batches_async(subsubsectiondocs, index_name, name_space_subsubsection, embeddings, vector_store, batch_size=100)
    # print("All subsubsections successfully uploaded.")

    # # pushing subsection data
    # subsectiondocs = []
    # for subsection in subsection_data:
    #     subsectiondocs.append(
    #         Document(
    #             page_content=subsection["summary"],
    #             metadata={
    #                 "section": subsection["section"],
    #                 "subsection": subsection["subsection"],
    #                 "start_page": subsection["start_page"],
    #                 "end_page": subsection["end_page"],
    #                 "md": subsection["md"],
    #                 "doc_id": doc_id
    #             }
    #         )
    #     )
    # await upload_documents_in_batches_async(subsectiondocs, index_name, name_space_subsection, embeddings, vector_store, batch_size=100)
    # print("All subsections successfully uploaded.")

    # # pushing section data
    # sectiondocs = []
    # for section in section_data:
    #     sectiondocs.append(
    #         Document(
    #             page_content=section["summary"],
    #             metadata={
    #                 "section": section["section"],
    #                 "start_page": section["start_page"],
    #                 "end_page": section["end_page"],
    #                 "doc_id": doc_id
    #             }
    #         )
    #     )
    # await upload_documents_in_batches_async(sectiondocs, index_name, name_space_secton, embeddings, vector_store, batch_size=100)
    # print("All sections successfully uploaded.")