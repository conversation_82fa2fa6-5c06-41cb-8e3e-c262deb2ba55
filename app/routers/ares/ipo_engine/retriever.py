import os
import pathlib
from langchain_pinecone import PineconeVectorStore
from langchain_openai import OpenAIEmbeddings
from langchain.schema import Document
from bm25_fusion import BM25

BUCKET_NAME = "documents-modus"

from .file_upload import load_json_from_s3, upload_to_s3, list_s3_files, download_from_s3
from .utils import stop_words, clean_text

def get_vector_retriever(index_name: str, namespace: str) -> PineconeVectorStore:
    embeddings = OpenAIEmbeddings()
    vector_store = PineconeVectorStore(
        index_name=index_name,
        embedding=embeddings,
        namespace=namespace
    )
    return vector_store.as_retriever(search_kwargs={"k": 3})

def create_bm25_retriever(doc_id: str):
    # list all files in the chunked_data folder
    list_files = list_s3_files(BUCKET_NAME, "chunked_data/")
    list_files = [file.split("/")[-1].split(".")[0] for file in list_files]
    if doc_id not in list_files:
        print(f"Error: No chunked data found for doc_id {doc_id}")
        return None

    # load the chunked data from s3
    key = f"chunked_data/{doc_id}.json"
    chunked_data = load_json_from_s3("documents-modus", key).get("chunks", [])
    if not chunked_data:
        print(f"Error: Failed to load chunked data from s3 for upload_id {doc_id}")
        return None
    print(f"Number of chunks loaded: {len(chunked_data)}")

    text_chunks = [clean_text(chunk["md"]) for chunk in chunked_data]
    metadata = [
        {
            "page": chunk["page"],
            "section": chunk["section"],
            "subsection": chunk["subsection"],
            "chunk_id": chunk["chunk_id"]
        }
        for chunk in chunked_data
    ]
    bm25 = BM25(
        texts = text_chunks,
        metadata = metadata,
        variant="bm25+",
        delta=0.5,
        stopwords=stop_words
    )

    # save locally in tmp folder in current working directory with the name bm25_retriever.h5
    current_working_directory = pathlib.Path().resolve()
    tmp_dir = os.path.join(current_working_directory, "tmp")
    os.makedirs(tmp_dir, exist_ok=True)
    bm25.save(os.path.join(tmp_dir, "bm25_retriever.h5"))

    upload_to_s3(os.path.join(tmp_dir, "bm25_retriever.h5"), bucket_name=BUCKET_NAME, s3_key=f"bm25_models/{doc_id}.h5")
    print(f"BM25 model saved for doc_id {doc_id}")
    return bm25

def get_bm25_retriever(doc_id: str):
    # load the bm25 model from s3
    list_files = list_s3_files(BUCKET_NAME, "bm25_models/")
    print(f"Files in bm25_models: {list_files}")
    if f"bm25_models/{doc_id}.h5" not in list_files:
        print(f"BM25 model not found for doc_id {doc_id}")
        print("Creating BM25 model...")
        if not create_bm25_retriever(doc_id):
            print(f"Error: Failed to create BM25 model for doc_id {doc_id}")
            return None
        print("BM25 model created and saved")

    # download the model
    current_working_directory = pathlib.Path().resolve()
    tmp_dir = os.path.join(current_working_directory, "tmp")
    os.makedirs(tmp_dir, exist_ok=True)
    download_from_s3(BUCKET_NAME, f"bm25_models/{doc_id}.h5", os.path.join(tmp_dir, "bm25_retriever.h5"))
    print(f"BM25 model downloaded for doc_id {doc_id}")

    # load the model
    bm25 = BM25.load(os.path.join(tmp_dir, "bm25_retriever.h5"))
    return bm25