from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain.chat_models import init_chat_model

from .icdrRetrievalAgent import DataRetrievalAgent as ICDRDataRetrievalAgent
from .listingRetrievalAgent import DataRetrievalAgent as ListingDataRetrievalAgent
from .utils import parse_llm_response
from .prompts import PROMPT_GPT_WRAPPER1, PROMPT_GPT_WRAPPER2

class GPTWrapper:
    def __init__(self, doc_id: str, iterations: int = 5):
        self.doc_id = doc_id
        self.iterations = iterations
        self.llm = init_chat_model("openai:gpt-4.1-mini")
        self.icdr_agent = ICDRDataRetrievalAgent(
            supervisor_llm_str="openai:gpt-4.1",
            researcher_llm_str="openai:o4-mini",
            doc_id='01c08f3f-b4f9-4ebe-a715-c28259a81ab4',
            iterations=iterations
        )
        self.listing_agent = ListingDataRetrievalAgent(
            supervisor_llm_str="openai:gpt-4.1",
            researcher_llm_str="openai:o4-mini",
            doc_id=doc_id,
            iterations=iterations
        )

    def icdr_retrieval(self, query: str):
        """
        Perform ICDR data retrieval using the agent.
        Args:
            query (str): The query to process.
        Returns:
            str: The final response from the agent.
        """
        print(f"ICDR Retrieval Query: {query}")
        results = self.icdr_agent.invoke(query)
        return results
    
    def listing_retrieval(self, query: str):
        """
        Perform Listing data retrieval using the agent.
        Args:
            query (str): The query to process.
        Returns:
            str: The final response from the agent.
        """
        print(f"Listing Retrieval Query: {query}")
        results = self.listing_agent.invoke(query)
        return results
    
    def chat(self, query: str):
        """
        Perform a chat interaction with the LLM.
        Args:
            query (str): The user query.
        Returns:
            str: The response from the LLM.
        """
        print(f"Chat Query: {query}")
        system_prompt = PROMPT_GPT_WRAPPER1
        messages = [SystemMessage(content=system_prompt), HumanMessage(content=query)]
        response = self.llm.invoke(messages)
        response_data = parse_llm_response(response)
        print("Chat Response:", response_data)

        queries = {
            "icdr_query": response_data.get("icdr_query", ""),
            "listing_query": response_data.get("listing_query", "")
        }

        icdr_response = self.icdr_retrieval(queries["icdr_query"]) if queries["icdr_query"] else ""
        listing_response = self.listing_retrieval(queries["listing_query"]) if queries["listing_query"] else ""
        final_response = {
            "icdr_response": icdr_response,
            "listing_response": listing_response
        }

        sytem_prompt = f"""
        {PROMPT_GPT_WRAPPER2}
        
        Retrieved ICDR Data: {icdr_response}
        Retrieved Listing Data: {listing_response}
        """

        messages = [SystemMessage(content=sytem_prompt), HumanMessage(content=query)]
        final_response = self.llm.invoke(messages)
        print("Final Response:", final_response)
        return final_response