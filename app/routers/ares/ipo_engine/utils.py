import os
import requests
import uuid
import json
import asyncio
import re  # Added missing import
from typing import Dict, List, Any, Tuple  # Added missing imports
import nltk
from nltk.corpus import stopwords
from langchain.text_splitter import RecursiveCharacterTextSplitter
from sqlalchemy.ext.asyncio import AsyncSession
from llama_cloud_services import LlamaParse
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain.chat_models import init_chat_model


from .prompts import (
    PROMPT_PARSE_INDEX_PAGE,
    PROMPT_CREATE_SUBSECTIONS,
    PROMPT_SUMMARY,
    PROMPT_SUMMARY_TEXT_LIST
)

# One-time download (only needed once)
nltk.download("stopwords")
nltk.download("punkt")

stop_words = set(stopwords.words("english"))

def download_pdf_from_presigned_url(presigned_url: str) -> str:
    # Ensure the tmp directory exists
    tmp_dir = os.path.join(os.getcwd(), "tmp")
    os.makedirs(tmp_dir, exist_ok=True)

    # Generate a temporary filename
    temp_filename = f"{uuid.uuid4().hex}.pdf"
    temp_filepath = os.path.join(tmp_dir, temp_filename)

    # Download the file
    response = requests.get(presigned_url)
    if response.status_code == 200:
        with open(temp_filepath, 'wb') as f:
            f.write(response.content)
        print(f"File downloaded successfully to {temp_filepath}")
        return temp_filepath
    else:
        print(f"Failed to download file. Status code: {response.status_code}")
        return None
    
async def parse_pdf(file_path: str):
    try:
        parser = LlamaParse(
            num_workers=4,       # if multiple files passed, split in `num_workers` API calls
            verbose=True,
            language="en",       # optionally define a language, default=en
            preset="balanced"
        )

        result = await parser.aparse(file_path)
        result = result.model_dump_json()
        result = json.loads(result)
        data = {
            "pages": result["pages"],
            "file_name": result["file_name"]
        }
        return data
    except Exception as e:
        print(f"Error parsing pdf: {e}")
        return None

async def save_json_to_db(data_object, db: AsyncSession):
    MAX_RETRIES = 3
    RETRY_DELAY = 2
    tries = 0
    while tries < MAX_RETRIES:
        try:
            db.add(data_object)
            await db.commit()
            return 1
        except Exception as e:
            print(f"Error committing to database: {str(e)}")
            tries += 1
            if tries < MAX_RETRIES:
                print(f"Retrying in {RETRY_DELAY} seconds...")
                await asyncio.sleep(RETRY_DELAY)
            else:
                print("Max retries exceeded. Skipping record.")
    return 0    

def get_index_page(content: dict):
    """This function finds the page number of the index page and its content."""

    for page in content.get('pages',[]):
        page_text = page.get('md', '')
        page_text_lower = page_text.lower()
        
        # Check text content for specific table of contents patterns
        if any(keyword in page_text_lower for keyword in [
            'table of contents', 'table of content'
        ]):
            return page.get('page', 0), page_text
        
        # Check for TOC indicators in text (since images won't be available)
        has_contents_header = 'contents' in page_text_lower
        has_page_numbers = any(indicator in page_text_lower for indicator in [
            'page no', 'page number'
        ])
        has_sections = any(indicator in page_text_lower for indicator in [
            'section i', 'section ii', 'section iii', 'section iv',
            'chapter', 'part i', 'part ii'
        ])
        
        # If text has strong TOC indicators, consider it a TOC page
        if (has_contents_header and has_page_numbers) or (has_contents_header and has_sections):
            return page.get('page', 0), page_text
        
    return None, None

def parse_llm_response(response: AIMessage) -> dict:
    """Safely parses the LLM's JSON response."""
    content = response.content
    try:
        if "```json" in content:
            json_str = content.split("```json")[1].split("```")[0].strip()
        else:
            json_str = content.strip()
        return json.loads(json_str)
    except (json.JSONDecodeError, IndexError) as e:
        print(f"Error parsing LLM response: {e}")
        print(f"Raw response: {content}")
        return None
    
async def call_llm_with_retry(llm, messages):
    MAX_RETRIES = 3
    RETRY_DELAY = 2
    tries = 0
    while tries < MAX_RETRIES:
        try:
            response = await llm.ainvoke(messages)
            return response
        except Exception as e:
            print(f"Error calling LLM: {e}")
            tries += 1
            if tries < MAX_RETRIES:
                print(f"Retrying in {RETRY_DELAY} seconds...")
                await asyncio.sleep(RETRY_DELAY)
            else:
                print("Max retries exceeded. Skipping record.")
    return None

async def parse_index_page(index_content: str):

    llm = init_chat_model("openai:gpt-4.1-mini")

    system_prompt = PROMPT_PARSE_INDEX_PAGE
    user_message = f"""
    The index page of the document is as follows:
    {index_content}

    Please extract the information about the structure of the document and create a json object as per the instructions.
    """

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=user_message)
    ]

    response = await call_llm_with_retry(llm, messages)
    return parse_llm_response(response)

def add_starting_pages_as_section(old_index: dict, index_page_number: int):
    """This function adds the starting pages as a section before the index page."""
    old_index['sections'] = [
        {
            "name": "SECTION 0: INTRODUCTION",
            "page_range": [1, index_page_number - 1],
            "subsections": []
        }
    ] + old_index.get('sections', [])
    return old_index

def get_pages_in_section(all_pages: list, start_page: int, end_page: int):
    all_pages_in_section = []
    page_numbers_in_section = set()
    for page in all_pages:
        page_number = int(page['page'])
        if page_number >= start_page and page_number <= end_page:
            all_pages_in_section.append({
                "md": page["md"],
                "page": page_number
            })
            page_numbers_in_section.add(page_number)
    assert len(page_numbers_in_section) == len(all_pages_in_section), f"Number of pages in section {len(all_pages_in_section)} does not match with the number of pages in the section {len(page_numbers_in_section)}"
    return all_pages_in_section, sorted(list(page_numbers_in_section))

def get_pages_in_section_plus_one(all_pages: list, start_page: int, end_page: int):
    all_pages_in_section = []
    page_numbers_in_section = set()
    for page in all_pages:
        page_number = int(page['page'])
        if page_number >= start_page and page_number <= end_page + 1:
            all_pages_in_section.append({
                "md": page["md"],
                "page": page_number
            })
        if page_number >= start_page and page_number <= end_page:
            page_numbers_in_section.add(page_number)
    return all_pages_in_section, sorted(list(page_numbers_in_section))

async def create_subsections_llm(page_content: str, parent_section_name: str):
    llm = init_chat_model("openai:gpt-4.1-mini")

    system_prompt = PROMPT_CREATE_SUBSECTIONS
    user_message = f"""
    Parent section: {parent_section_name}

    Page content:
    {page_content}

    Please extract the subsections and return a valid JSON object as per the instructions.
    """

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=user_message)
    ]

    response = await call_llm_with_retry(llm, messages)
    return parse_llm_response(response)

async def create_subsections(all_pages_in_subsection_plus_one: list, parent_section_name: str):
    """
    Identifies and extracts subsections from a series of pages, correctly handling
    subsections that span across page breaks.

    Args:
        all_pages_in_subsection_plus_one: A list of page dictionaries. The final page
            in the list is a "lookahead" page, used only to complete a section
            that might have been cut off on the penultimate page.
        parent_section_name: The name of the parent section, for context.

    Returns:
        A list of complete subsection dictionaries.
    """
    combined_subsections = []

    # --- State Variables ---
    # Stores the dict of a section that was incomplete on the previous page.
    incomplete_section_carryover = None
    # Stores the raw text of the incomplete section to prepend to the next page.
    text_to_prepend = ""

    # --- Main Processing Loop ---
    # Iterate through all pages. The last page is only for completing a carryover.
    for i, page in enumerate(all_pages_in_subsection_plus_one):
        page_number = page['page']
        is_last_page = (i == len(all_pages_in_subsection_plus_one) - 1)

        # STEP 1: Prepare the content for the LLM call.
        # Prepend any text from an incomplete section on the previous page.
        page_content = text_to_prepend + page['md']
        text_to_prepend = ""

        if not page_content.strip():
            continue

        # STEP 2: Call the LLM to identify subsections on the current page.
        result = await create_subsections_llm(page_content, parent_section_name)

        if result is None or 'subsections' not in result:
            print(f"[ERROR] LLM parsing failed on page {page_number}. Aborting carryover if any.")
            # If parsing fails, we can't continue a carried-over section.
            # We discard it to prevent creating a malformed entry.
            incomplete_section_carryover = None
            continue

        page_sections = result.get('subsections', [])

        # STEP 3: Handle the completion of a section carried over from the previous page.
        if incomplete_section_carryover and page_sections:
            completion_part = page_sections.pop(0)

            # Merge the original data with the new completion data.
            # This preserves the original title and start_tokens.
            completed_section = {
                "name": completion_part['name'],  # Use the ORIGINAL title.
                "start_tokens": incomplete_section_carryover['start_tokens'],
                "end_tokens": completion_part['end_tokens'],
                "start_page": incomplete_section_carryover['start_page'],
                "end_page": page_number
            }
            combined_subsections.append(completed_section)
            print(f"[INFO] Completed carried-over section '{completed_section['name']}' on page {page_number}")
            
            # The section is now complete, so reset the carryover state.
            incomplete_section_carryover = None

        # STEP 4: If this is the last "lookahead" page, its job is done.
        # We do not process any other new sections it may contain.
        if is_last_page:
            break

        # STEP 5: Add any other fully-contained sections from the current page.
        for sub in page_sections:
            sub['start_page'] = page_number
            sub['end_page'] = page_number
            combined_subsections.append(sub)

        # STEP 6: Check if this page's LAST section is incomplete and set up the next carryover.
        is_last_section_incomplete = not result.get('last_section_completed', True)
        if is_last_section_incomplete and combined_subsections:
            # The last section we just added is incomplete, so pop it from the final list.
            incomplete_section_carryover = combined_subsections.pop()
            
            # Find the start of the incomplete section in the text. We will carry forward
            # all text from that point to give the LLM full context on the next page.
            try:
                start_index = page_content.rfind(incomplete_section_carryover['start_tokens'])
                if start_index != -1:
                    text_to_prepend = page_content[start_index:]
                else: # Fallback if start_tokens can't be found (should be rare).
                    print(f"[WARNING] Could not find start_tokens on page {page_number}. Carryover may be imprecise.")
                    # As a fallback, just take the text after the supposed end.
                    text_to_prepend = page_content.rsplit(incomplete_section_carryover['end_tokens'], 1)[0]
            except Exception as e:
                 print(f"[ERROR] Failed to slice text for carryover on page {page_number}: {e}")
                 text_to_prepend = "" # Don't carry over anything if slicing fails.
                 incomplete_section_carryover = None # Cancel the carryover.

            if incomplete_section_carryover:
                print(f"[INFO] Carrying over section '{incomplete_section_carryover['name']}' from page {page_number}")

    print(f"[DONE] Total subsections identified: {len(combined_subsections)}")
    return combined_subsections

def add_raw_text(subsections, all_pages_in_subsection_plus_one):
    """This function adds the raw text to the subsections using the start and end tokens."""
    new_subsections = []
    for subsection in subsections:
        raw_text = ""
        start_page = subsection['start_page']
        end_page = subsection['end_page']

        start_token = subsection['start_tokens']
        end_token = subsection['end_tokens']

        for page in all_pages_in_subsection_plus_one:
            if page['page'] >= start_page and page['page'] <= end_page:
                raw_text += page['md'] + "\n\n"
        
        # Extract the relevant text using the start and end tokens
        try:
            start_index = raw_text.find(start_token)
            end_index = raw_text.rfind(end_token) + len(end_token)
            raw_text = raw_text[start_index:end_index]
        except Exception as e:
            print(f"[ERROR] Failed to extract text for subsection '{subsection['name']}': {e}")
            
        subsection['raw_text'] = raw_text
        new_subsections.append(subsection)
    return new_subsections

def get_chunks_consecutive_pages(page_numbers):
    """This function takes a list of page numbers and returns a list of chunks of consecutive pages."""
    chunks = []
    current_chunk = []
    for page_number in sorted(page_numbers):
        if not current_chunk or page_number == current_chunk[-1] + 1:
            current_chunk.append(page_number)
        else:
            chunks.append(current_chunk)
            current_chunk = [page_number]
    if current_chunk:
        chunks.append(current_chunk)
    return chunks

async def process_subsection_task(all_pages_in_this_subsection_plus_one, subsection_name):
    """Task to be executed in parallel for subsections."""
    subsubsections = await create_subsections(all_pages_in_this_subsection_plus_one, subsection_name)
    subsubsections = add_raw_text(subsubsections, all_pages_in_this_subsection_plus_one)
    return subsubsections

async def process_chunk_task(all_pages, chunk, section_name):
    """Task to be executed in parallel for remaining pages in a section."""
    all_pages_in_this_section_plus_one, _ = get_pages_in_section_plus_one(all_pages, chunk[0], chunk[-1])
    subsections = await create_subsections(all_pages_in_this_section_plus_one, section_name)
    subsections = add_raw_text(subsections, all_pages_in_this_section_plus_one)
    
    new_subsections = []
    for subsection in subsections:
        new_sub = {
            "name": subsection['name'],
            "page_range": [subsection['start_page'], subsection['end_page']],
            "raw_text": subsection['raw_text'],
        }
        new_subsections.append(new_sub)
    return new_subsections

async def summary_llm(content: str, parent_section_name: str):
    llm = init_chat_model("openai:gpt-4.1-mini")

    system_prompt = PROMPT_SUMMARY
    user_message = f"""
    The name of the section is {parent_section_name}. The content of the section is:
    {content}
    """

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=user_message)
    ]

    response = await call_llm_with_retry(llm, messages)

    return parse_llm_response(response)

async def summarize_text_list(summaries: list[str], context_name: str):
    """
    Takes a list of summary strings and synthesizes them into a single, high-level summary.
    """

    llm = init_chat_model("openai:gpt-4.1-mini")

    if not summaries:
        return f"No detailed summaries were available to generate a roll-up summary for {context_name}."

    system_prompt = PROMPT_SUMMARY_TEXT_LIST
    
    # Combine the list of summaries into a single block of text for the prompt
    combined_summaries = "\n\n---\n\n".join(summaries)

    user_message = f"""
    Please synthesize the following detailed summaries for the section titled '{context_name}' into one high-level summary. The summary should be like pointers, indicating the overall information present, and not exceed 5-6 lines.

    Summaries to merge:
    ---
    {combined_summaries}
    ---
    """
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=user_message)
    ]
    response = await call_llm_with_retry(llm, messages)
    
    return parse_llm_response(response)

# --- Wrappers for parallel tasks ---
async def summarize_subsubsection(subsubsection):
    try:
        name = subsubsection['name']
        raw_text = subsubsection['raw_text']
        result = await summary_llm(raw_text, name)
        subsubsection['summary'] = result.get('summary', '') if result else ''
    except Exception as e:
        print(f"Error summarizing subsubsection '{subsubsection.get('name')}': {e}")
        subsubsection['summary'] = ''
    return subsubsection

async def summarize_subsection(subsection):
    print("\tSummarizing subsection:", subsection['name'])
    try:
        subsub_summaries = [s['summary'] for s in subsection.get('subsubsections', []) if s.get('summary')]
        if subsub_summaries:
            result = await summarize_text_list(subsub_summaries, subsection['name'])
            subsection['summary'] = result.get('summary', '') if result else ''
        else:
            result = await summary_llm(subsection.get('raw_text', ''), subsection['name'])
            subsection['summary'] = result.get('summary', '') if result else ''
    except Exception as e:
        print(f"Error summarizing subsection '{subsection.get('name')}': {e}")
        subsection['summary'] = ''
    return subsection

async def summarize_section(section):
    print("Summarizing section:", section['name'])
    try:
        subsection_summaries = [s['summary'] for s in section.get('subsections', []) if s.get('summary')]
        if subsection_summaries:
            result = await summarize_text_list(subsection_summaries, section['name'])
            section['summary'] = result.get('summary', '') if result else ''
        else:
            section['summary'] = ''
    except Exception as e:
        print(f"Error summarizing section '{section.get('name')}': {e}")
        section['summary'] = ''
    return section

def find_section(table_of_content: dict, page_number: int):
    """This function finds the section a page belongs to"""
    for section in table_of_content.get('sections',[]):
        if page_number >= section['page_range'][0] and page_number <= section['page_range'][1]:
            return section['name']
    return None

def find_subsection(table_of_content: dict, page_number: int):
    """This function finds the subsection a page belongs to"""
    for section in table_of_content.get('sections',[]):
        if page_number >= section['page_range'][0] and page_number <= section['page_range'][1]:
            for subsection in section['subsections']:
                if page_number >= subsection['page_range'][0] and page_number <= subsection['page_range'][1]:
                    return subsection['name']
    return None

def create_chunk_data(raw_data_json, index_data):
    """This function creates chunk data from the raw data"""
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=2000,
        chunk_overlap=200,
        separators=["\n\n", "\n", ".", " "]
    )
    chunk_data = []
    for page in raw_data_json["pages"]:
        full_page_text = page["md"]
        page_num = page["page"]
        section = find_section(index_data, page_num)
        subsection = find_subsection(index_data, page_num)
        chunks = text_splitter.split_text(full_page_text)
        for i, chunk in enumerate(chunks):
            chunk_data.append({
                "page": page_num,
                "md": chunk,
                "chunk_id": i,
                "section": section if section else "N/A",
                "subsection": subsection if subsection else "N/A"
            })
    return chunk_data

def clean_text(text):
    # 1. Normalize markdown tables, headers, bullets, etc.
    text = re.sub(r'`{1,3}[^`]*`{1,3}', '', text)             # remove inline/code blocks
    text = re.sub(r'!\[.*?\]\(.*?\)', '', text)               # remove images
    text = re.sub(r'\[.*?\]\(.*?\)', '', text)                # remove links
    text = re.sub(r'^#+\s*', '', text, flags=re.MULTILINE)    # remove headers (###, ## etc.)
    text = re.sub(r'^\s*[-*+]\s+', '', text, flags=re.MULTILINE)  # remove bullets
    text = re.sub(r'\|.*?\|', '', text)                       # remove markdown table rows
    text = re.sub(r'-{3,}', '', text)                         # remove table header dividers

    # 2. Replace multiple spaces/newlines
    text = re.sub(r'\s+', ' ', text)

    # 3. Remove non-ASCII/strange symbols except punctuation
    text = re.sub(r'[^A-Za-z0-9.,;:\-()\[\]\'\" ]+', '', text)

    # 4. Remove space before punctuation
    text = re.sub(r'\s([.,;:])', r'\1', text)

    # 5. Lowercase and tokenize
    tokens = nltk.word_tokenize(text.lower())

    # 6. Remove stopwords
    filtered_tokens = [word for word in tokens if word not in stop_words]

    # 7. Join back to cleaned text
    return " ".join(filtered_tokens).strip()