import os
from dotenv import load_dotenv
from jinja2 import Template
from weasyprint import HTML
from io import BytesIO
from pathlib import Path
import boto3
from botocore.config import Config

# Load AWS credentials and region from .env
# AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
# AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
# AWS_DEFAULT_REGION = os.getenv("AWS_REGION", "ap-south-1")
# BUCKET_NAME = os.getenv("S3_BUCKET_NAME")

AWS_DEFAULT_REGION = os.getenv("AWS_REGION", "ap-south-1")
BUCKET_NAME = os.getenv("S3_BUCKET_NAME", "documents-modus")

# Initialize boto3 S3 client with Signature V4
config = Config(signature_version="s3v4")
s3 = boto3.client(
    "s3",
    region_name=AWS_DEFAULT_REGION,
    config=config,
)

def generate_pdf_bytes(data: dict) -> BytesIO:
    """Generates an HTML report with visualizations and saves a PDF version in the same directory."""

    company_details = data.get("company_details", {}) or {}
    industry = data.get("industry", {}) or {}
    financial = data.get("financial", []) or []
    transaction = data.get("transaction", []) or []
    risk_metrics = data.get("risk_metrics", {}) or {}
    red_flags = data.get("red_flags", {}) or {}
    notes = data.get("notes", []) or []
    visualizations = data.get("visualizations", []) or []

    template_str = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Modus Fraud Detection Report</title>
    <style>
        @page { size: A4; margin: 2cm; }
        body { font-family: 'Arial', sans-serif; font-size: 12px; margin: 0; color: #000; }
        h1 { color: #2739C1; font-size: 20px; margin-bottom: 0; }
        h2 { font-size: 16px; margin-top: 30px; border-bottom: 1px solid #2739C1; padding-bottom: 4px; }
        h3 { font-size: 14px; margin-top: 20px; margin-bottom: 5px; color: #444; }
        p { margin: 5px 0 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ccc; padding: 5px; text-align: left; font-size: 11px; }
        th { background-color: #f5f5f5; font-weight: bold; }
        ul { margin-top: 0; padding-left: 20px; }
        .note { margin-top: 20px; }
        .visualization { margin-top: 20px; text-align: center; }
        .visualization img { width: 500px; height: 280px; object-fit: contain; display: block; margin: 0 auto; border: none; }
    </style>
    </head>
    <body>
        <h1>Modus Fraud Detection Report</h1>

        {% if company_details.company_name %}
        <p><strong>Company:</strong> {{ company_details.company_name }}</p>
        {% endif %}

        {% if company_details.about_the_company %}
        <h2>Company Overview</h2>
        <p>{{ company_details.about_the_company }}</p>
        {% endif %}

        {% if industry %}
        <h2>Industry Overview</h2>
        {% if industry.is_industry_risky %}
        <p><strong>Industry Risk:</strong> {{ industry.is_industry_risky }}</p>
        {% endif %}
        {% if industry.about_the_industry %}
        <p>{{ industry.about_the_industry }}</p>
        {% endif %}
        {% if industry.justification %}
        <p><em>{{ industry.justification }}</em></p>
        {% endif %}
        {% endif %}

        {% if financial %}
        <h2>Financial Metrics</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            {% for item in financial %}
            <tr><td>{{ item.label }}</td><td>{{ item.value }}</td></tr>
            {% endfor %}
        </table>
        {% endif %}

        {% if transaction %}
        <h2>Transaction Metrics</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            {% for item in transaction %}
            <tr><td>{{ item.label }}</td><td>{{ item.value }}</td></tr>
            {% endfor %}
        </table>
        {% endif %}

        {% if risk_metrics %}
        <h2>Risk Metrics</h2>
        <table>
            {% for key, value in risk_metrics.items() %}
            <tr><td>{{ key.replace("_", " ").title() }}</td><td>{{ value }}</td></tr>
            {% endfor %}
        </table>
        {% endif %}

        {% if red_flags %}
        <h2>Red Flags</h2>
        {% for section, flags in red_flags.items() if flags %}
            <h3>{{ section.replace("_", " ").title() }}</h3>
            <table>
                <tr><th>Description</th><th>Severity</th><th>Created At</th></tr>
                {% for flag in flags %}
                <tr>
                    <td>{{ flag.description }}</td>
                    <td>{{ flag.severity }}</td>
                    <td>{{ flag.created_at }}</td>
                </tr>
                {% endfor %}
            </table>
        {% endfor %}
        {% endif %}

        {% if visualizations %}
            <h2>Visualizations</h2>
            {% for vis_id, vis_path in visualizations %}
                <div class="visualization">
                    <img src="{{ vis_path }}" alt="Visualization">
                </div>
            {% endfor %}
        {% endif %}

        <div class="note">
            <h2>Notes</h2>
            {% if notes %}
            <ul>
                {% for note in notes %}
                <li>{{ note }}</li>
                {% endfor %}
            </ul>
            {% else %}
            <p>No additional notes.</p>
            {% endif %}
        </div>  

    </body>
    </html>
    """

    # Render HTML from template
    template = Template(template_str)
    html_content = template.render(
        company_details=company_details,
        industry=industry,
        financial=financial,
        transaction=transaction,
        risk_metrics=risk_metrics,
        red_flags=red_flags,
        notes=notes,
        visualizations=visualizations
    )

    # Save HTML
    # Convert to PDF with base_url to support image rendering
    base_path = Path(visualizations[0][1]).parent if visualizations else Path(".")
    pdf_io = BytesIO()
    HTML(string=html_content, base_url=str(base_path)).write_pdf(pdf_io)
    pdf_io.seek(0)
    return pdf_io

def upload_to_s3(file_obj: BytesIO, bucket: str, key: str):
    """Upload file-like object to S3."""
    s3.upload_fileobj(
        file_obj,
        bucket,
        key,
        ExtraArgs={"ContentType": "application/pdf"}
    )

def generate_presigned_url(bucket: str, key: str, expires_in=3600) -> str:
    """Generate a presigned URL for the uploaded PDF."""
    return s3.generate_presigned_url(
        "get_object",
        Params={"Bucket": bucket, "Key": key},
        ExpiresIn=expires_in
    )

def main(data: dict) -> str:
    """Main pipeline: HTML → PDF → S3 → Signed URL"""
    # 1. Generate PDF in memory
    pdf_file = generate_pdf_bytes(data)

    # 2. Choose S3 key (filename + folder)
    filename = "fraud_report.pdf"
    s3_key = f"reports/2025/{filename}"

    # 3. Upload to S3
    upload_to_s3(pdf_file, BUCKET_NAME, s3_key)

    # 4. Return signed URL
    return generate_presigned_url(BUCKET_NAME, s3_key)
