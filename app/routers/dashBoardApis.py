from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError # Added import

from uuid import UUID, uuid4
from zoneinfo import ZoneInfo
import asyncio
from decimal import Decimal
from datetime import date, datetime

from ..models import models
from ..database import get_db, get_db_async
from .apiProtection import get_current_user
from .ares.modus_agent.modus_agent import workflow
from ..utils.async_sql_executor import run_sql_query_async
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

import json
from pydantic import BaseModel
import tempfile
import subprocess
import asyncpg

router = APIRouter()

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

class VisualizationDashboardRequest(BaseModel):
    visualization_id: str
    dashboard_id: str

class VisualizationUpdateRequest(BaseModel):
    visualization_id: str | None
    title: str | None
    description: str | None
    dashboard_id: str | None
    config: dict
    code_type: str | None  # Type of code used for the graph (e.g., 'python', 'sql')
    code: str | None  # Python code for the graph, if applicable
    query: str | None  # SQL query for the graph, if applicable


@router.get("/get_all_dashboards")
async def get_all_dashboards(
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    try:
        query = select(models.Dashboard)
        result = await db.execute(query)
        dashboards = result.scalars().all()
        results = []
        for dashboard in dashboards:
            dashboard_id = str(dashboard.dashboard_id)
            # Get the count of visualizations associated with this dashboard
            vis_query = select(models.GraphSpecs).where(models.GraphSpecs.dashboard_id == dashboard_id)
            vis_result = await db.execute(vis_query)
            visualization_count = len(vis_result.scalars().all())
            # Add the count to the dashboard data
            results.append({
                "id": str(dashboard.dashboard_id),
                "name": dashboard.name,
                "description": dashboard.description,
                "type": dashboard.dash_type,
                "created_by": dashboard.created_by,
                "count": visualization_count
            })
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Dashboards retrieved successfully",
                "data": results
            }
        )
    except SQLAlchemyError as e:
        # Log the error or handle it as needed
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/get_dashboard/{dashboard_id}")
async def get_dashboard(
    dashboard_id: str,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # we need to get both the dashboard and its associated visualizations
        query = select(models.Dashboard).where(models.Dashboard.dashboard_id == dashboard_id)
        result = await db.execute(query)
        dashboard = result.scalar_one_or_none()
        if not dashboard:
            raise HTTPException(status_code=404, detail="Dashboard not found")

        dashboard_id = str(dashboard.dashboard_id)
        dashboard = dashboard.__dict__

        # Get the visualizations associated with this dashboard
        query = select(models.GraphSpecs).where(models.GraphSpecs.dashboard_id == dashboard_id)
        result = await db.execute(query)
        visualizations = result.scalars().all()
        dashboard = {
            "id": str(dashboard['dashboard_id']),
            "name": dashboard['name'],
            "description": dashboard['description'],
            "type": dashboard['dash_type'],
        }

        print("running queries")
        dashboard['visualizations'] = []
        for vis in visualizations:
            vid_id = str(vis.visualization_id)
            # get the data by running the query
            query = vis.query if vis.code_type == 'query' else vis.code
            if not query:
                continue
            # Run the SQL query asynchronously using the session
            data = await run_sql_query_async(db, query)
            print("data", data)
            if data['status'] == 'error':
                raise HTTPException(status_code=500, detail=data['message'])
            data = data['results']
            data = json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)  # Convert to JSON string for response
            dashboard['visualizations'].append({
                "id": vid_id,
                "title": vis.title,
                "description": vis.description,
                "config": vis.config,
                "code_type": vis.code_type,
                "code": vis.code,
                "query": vis.query,
                "data": data,  # Add the data to the visualization
                "created_by": vis.created_by,
            })
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Dashboard retrieved successfully",
                "data": dashboard
            }
        )
    except SQLAlchemyError as e:
        # Log the error or handle it as needed
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/get_vis_data/{visualization_id}")
async def get_vis_data(
    visualization_id: str,
    db: AsyncSession = Depends(get_db_async),
    current_user: models.User = Depends(get_current_user)
):
    try:
        query = select(models.GraphSpecs).where(models.GraphSpecs.visualization_id == visualization_id)
        result = await db.execute(query)
        visualization = result.scalar_one_or_none()
        if not visualization:
            raise HTTPException(status_code=404, detail="Visualization not found")

        query = visualization.query if visualization.code_type == 'query' else visualization.code
        if not query:
            raise HTTPException(status_code=404, detail="No query found for this visualization")

        # Run the SQL query asynchronously using the session
        data = await run_sql_query_async(db, query)
        if data['status'] == 'error':
            raise HTTPException(status_code=500, detail=data['message'])

        results = data['results']
        results = json.dumps(results, ensure_ascii=False, cls=CustomJSONEncoder)  # Convert to JSON string for response
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"data": json.loads(results), "message": "Data retrieved successfully", "success": True}
        )
    except SQLAlchemyError as e:
        # Log the error or handle it as needed
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/save-update-visualization")
async def save_update_visualization(
    request: VisualizationUpdateRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Save updates to a visualization or create a new one"""
    try:
        visualization_id = request.visualization_id

        if visualization_id:
            # Update existing visualization
            existing_visualization = db.query(models.GraphSpecs).filter(models.GraphSpecs.visualization_id == visualization_id).first()
            if not existing_visualization:
                return JSONResponse(
                    status_code=status.HTTP_404_NOT_FOUND,
                    content={"success": False, "message": "Visualization not found"}
                )

            update_data = request.model_dump(exclude_unset=True) # Get only provided fields

            for key, value in update_data.items():
                if value is not None and hasattr(existing_visualization, key):
                    setattr(existing_visualization, key, value)

            existing_visualization.updated_by = current_user.id
            existing_visualization.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
            print("existing_visualization: ", existing_visualization)
            db.add(existing_visualization)
            db.commit()
            db.refresh(existing_visualization)
            message = "Visualization updated successfully"
        else:
            # Create new visualization
            visualization_id = str(uuid4())
            new_visualization = models.GraphSpecs(
                visualization_id=visualization_id,
                title=request.title,
                description=request.description,
                dashboard_id=request.dashboard_id,
                chat_id=request.chat_id,
                config=request.config,
                code_type=request.code_type,
                code=request.code,
                query=request.query,
                created_by=str(current_user.id),
                created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
                updated_by=str(current_user.id),
                updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
            )
            db.add(new_visualization)
            db.commit()
            db.refresh(new_visualization)
            message = "Visualization created successfully"
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": message,
                "data": {
                    "visualization_id": visualization_id
                }
            }
        )
    except SQLAlchemyError as e:
        db.rollback()
        print(f"SQLAlchemyError in save_update_visualization: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        db.rollback()
        print(f"Unexpected error in save_update_visualization: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/add-visualization-dashboard")
def add_visualization_dashboard(
    request: VisualizationDashboardRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """This function will add a visualization to a dashboard."""
    try:
        # get the visualization from the database
        visualization = db.query(models.GraphSpecs).filter(models.GraphSpecs.visualization_id == request.visualization_id).first()
        if not visualization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Visualization not found"
            )

        # Check if the dashboard already exists
        dashboard = db.query(models.Dashboard).filter(models.Dashboard.dashboard_id == request.dashboard_id).first()
        if not dashboard:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Dashboard not found"
            )

        response_visualization_id = visualization.visualization_id # Default to original

        # Check if the visualization is already added to some dashboard
        current_dashboard_id = visualization.dashboard_id
        if current_dashboard_id and current_dashboard_id != request.dashboard_id:
            # create a copy of the visualization
            new_visualization_id = str(uuid4())
            new_visualization = models.GraphSpecs(
                visualization_id=new_visualization_id,
                title=visualization.title,
                description=visualization.description,
                dashboard_id=request.dashboard_id,  # Set the new dashboard ID
                chat_id=visualization.chat_id,
                config=visualization.config,
                code_type=visualization.code_type,
                code=visualization.code,
                query=visualization.query,
                created_by=str(current_user.id),
                created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
                updated_by=str(current_user.id),
                updated_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None) # Corrected ZoneInfo
            )
            db.add(new_visualization)
            db.commit()
            response_visualization_id = new_visualization_id # Update to the new ID
        else:
            # Update the existing visualization to point to the new dashboard
            # This also handles the case where visualization is not on any dashboard (current_dashboard_id is None)
            # or if it's already on the target dashboard (current_dashboard_id == request.dashboard_id)
            visualization.dashboard_id = request.dashboard_id
            visualization.updated_by = str(current_user.id)
            visualization.updated_at = datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)
            db.add(visualization)
            db.commit()
            # response_visualization_id remains visualization.visualization_id

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Visualization added to dashboard successfully",
                "data": {
                    "visualization_id": response_visualization_id, # Use the correct ID
                    "dashboard_id": request.dashboard_id
                }
            }
        )
    except SQLAlchemyError as e:
        db.rollback()
        print(f"SQLAlchemyError in add_visualization_dashboard: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

class CreateDashboardRequest(BaseModel):
    name: str
    description: str | None

@router.post("/create-dashboard")
def create_dashboard(
    request: CreateDashboardRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Create a new dashboard."""
    try:
        new_dashboard = models.Dashboard(
            dashboard_id=str(uuid4()),
            name=request.name,
            description=request.description,
            created_by=str(current_user.id),
            created_at=datetime.now(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None),
            dash_type='user'  # Default type
        )
        db.add(new_dashboard)
        db.commit()
        db.refresh(new_dashboard)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Dashboard created successfully",
                "data": {
                    "dashboard_id": new_dashboard.dashboard_id
                }
            }
        )
    except SQLAlchemyError as e:
        db.rollback()
        print(f"SQLAlchemyError in create_dashboard: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/delete-dashboard/{dashboard_id}")
def delete_dashboard(
    dashboard_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Delete a dashboard."""
    try:
        dashboard = db.query(models.Dashboard).filter(models.Dashboard.dashboard_id == dashboard_id).first()
        if not dashboard:
            raise HTTPException(status_code=404, detail="Dashboard not found")

        # Delete all visualizations associated with this dashboard
        db.query(models.GraphSpecs).filter(models.GraphSpecs.dashboard_id == dashboard_id).delete(synchronize_session=False)

        db.delete(dashboard)
        db.commit()

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Dashboard deleted successfully"
            }
        )
    except SQLAlchemyError as e:
        db.rollback()
        print(f"SQLAlchemyError in delete_dashboard: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")