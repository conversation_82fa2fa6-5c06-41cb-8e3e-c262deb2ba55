from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Path, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID, uuid4
from datetime import datetime, date
from ..database import get_db
from ..models import models, dfbmodels
from ..schemas import request_models
from ..schemas.response_models import TransactionResponse, TransactionListResponse, PaymentChannelListResponse, PayoutListResponse, CommunicationListResponse, TimelineEventResponse, TimelineEventListResponse, InvestigationResponse, InvestigationListResponse, InvestigationNoteListResponse, KeyMetricsResponse
from collections import defaultdict
from .ares.model.llm_transaction_analysis import run_this_please
from .ares.feature_generation.incremental_feature_gen import main
import pandas as pd
import numpy as np
import os
import json
from .ares.investigation_gpt.gpt_with_context import use_it
from .ares.model.summarizzer import summer_is_mine
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from neo4j import GraphDatabase, exceptions
import time
from transformers import AutoTokenizer, AutoModel
import torch
from sklearn.metrics.pairwise import cosine_similarity
from groq import Groq
import asyncio
import requests
import json
import time
from datetime import datetime
import os
import whois
import socket
from ipwhois import IPWhois
import ipaddress
from urllib.parse import urlparse
import requests
from bs4 import BeautifulSoup
import re
import spacy
from spacy.matcher import Matcher
from urllib.parse import urlparse
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords
from collections import Counter
from typing import Dict, List, Tuple
import logging
from openai import OpenAI
from .apiProtection import get_current_user
from .redFlagGenerationRouter import generate_red_flag
from ..utils.scrapers import full_scrapper_temp
from ..utils.digital_footprint_builder import make_digital_footprint
import uuid

# Initialize logger
logger = logging.getLogger(__name__)

router = APIRouter()

scheduler = BackgroundScheduler()

jobManager: Dict[str, dfbmodels.DFPJobs] = {}

def response_time_series_update():
    db = next(get_db())
    merchants = db.query(models.Merchant).all()
    for merchant in merchants:
        print("********************************************")
        domain = merchant.domain
        
        try:
            start_time = time.time()
            response = requests.get(f"{domain}", timeout=10)
            end_time = time.time()
            response_time_ms = str(int((end_time - start_time) * 1000))
            
            # Create new response time series entry
            new_entry = models.response_time_series(
                merchant_id=merchant.id,
                timestamp=datetime.now().isoformat(),
                status_code=str(response.status_code),
                response_time_ms=response_time_ms,
                is_active=response.status_code == 200,
                error=None
            )
            db.add(new_entry)
            db.commit()

            # if response.status_code == 200:
            #     print(f"Website is up and running: {domain}")
            # else:
            #     print(f"Website is down: {domain}")

        except Exception as e:
            # Log error in response time series
            error_entry = models.response_time_series(
                merchant_id=merchant.id,
                timestamp=datetime.now().isoformat(),
                status_code="ERROR",
                response_time_ms="0",
                is_active=False,
                error=str(e)
            )
            db.add(error_entry)
            db.commit()
            print(f"Error checking website status: {e}")

class WebsiteAnalyzer:
    def __init__(self):
        # Download required NLTK data
        try:
            # Create nltk_data directory if it doesn't exist
            nltk_data_dir = os.path.expanduser('~/nltk_data')
            if not os.path.exists(nltk_data_dir):
                os.makedirs(nltk_data_dir)
            
            # Download all required NLTK data with explicit paths
            resources = {
                'tokenizers/punkt': 'punkt',
                'corpora/stopwords': 'stopwords',
                'taggers/averaged_perceptron_tagger': 'averaged_perceptron_tagger',
                'chunkers/maxent_ne_chunker': 'maxent_ne_chunker',
                'corpora/words': 'words'
            }
            
            for resource_path, resource_name in resources.items():
                try:
                    nltk.data.find(resource_path)
                except LookupError:
                    print(f"Downloading {resource_name}...")
                    nltk.download(resource_name, quiet=True)
            
            # Initialize stopwords after ensuring downloads
            self.stop_words = set(stopwords.words('english'))
            
        except Exception as e:
            print(f"Warning: Error initializing NLTK resources: {e}")
            # Provide empty stopwords as fallback
            self.stop_words = set()
        
    def analyze_website(self, url: str) -> Dict:
        """
        Analyzes a merchant website to extract product/service information,
        categorization, and key words.
        
        Args:
            url (str): The website URL to analyze
            
        Returns:
            Dict: Analysis results containing products/services, categorization, and keywords
        """
        try:
            # Fetch the website content
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Initialize results dictionary
            results = {
                'products_services': self._extract_products_services(soup),
                'categorization': self._determine_categorization(soup),
                'keywords': self._extract_keywords(soup),
                'status': 'success'
            }
            
            return results
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    def _extract_products_services(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract products and services information from the website"""
        products_services = []
        
        # Look for product/service containers
        product_containers = soup.find_all(['div', 'section'], class_=re.compile(
            r'product|service|item|offering', re.IGNORECASE))
        
        for container in product_containers:
            # Extract name
            name = container.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            name = name.text.strip() if name else ''
            
            # Extract description
            description = container.find(['p', 'div'], class_=re.compile(
                r'description|details|info', re.IGNORECASE))
            description = description.text.strip() if description else ''
            
            # Extract price if available
            price = container.find(string=re.compile(r'[\$€£]\d+|\d+[\$€£]'))
            price = price.strip() if price else ''
            
            if name or description:
                products_services.append({
                    'name': name,
                    'description': description,
                    'price': price
                })
        
        return products_services
    
    def _determine_categorization(self, soup: BeautifulSoup) -> Dict:
        """Determine if the website is B2B, B2C, or both"""
        text = soup.get_text().lower()
        
        # Define indicators for B2B and B2C
        b2b_indicators = [
            'business solutions', 'enterprise', 'wholesale', 'bulk orders',
            'corporate', 'business customers', 'volume pricing'
        ]
        
        b2c_indicators = [
            'shop now', 'add to cart', 'personal use', 'retail',
            'individual', 'consumer', 'shopping cart'
        ]
        
        # Count occurrences
        b2b_count = sum(1 for indicator in b2b_indicators if indicator in text)
        b2c_count = sum(1 for indicator in b2c_indicators if indicator in text)
        
        # Determine categorization
        categorization = {
            'is_b2b': b2b_count > 0,
            'is_b2c': b2c_count > 0,
            'primary_category': 'B2B' if b2b_count > b2c_count else 'B2C' if b2c_count > b2b_count else 'Hybrid'
        }
        
        return categorization
    
    def _extract_keywords(self, soup: BeautifulSoup) -> List[Tuple[str, int]]:
        """Extract and analyze key words from the website"""
        try:
            # Get text content
            text = soup.get_text()
            
            # Use a more robust tokenization approach
            try:
                tokens = word_tokenize(text.lower())
            except LookupError:
                # Fallback to basic tokenization if NLTK tokenizer fails
                tokens = text.lower().split()
            
            # Remove stop words and non-alphabetic tokens
            keywords = [word for word in tokens 
                       if word.isalpha() 
                       and word not in self.stop_words 
                       and len(word) > 2]
            
            # Count frequency
            keyword_freq = Counter(keywords)
            
            # Return top 20 keywords with their frequencies
            return keyword_freq.most_common(20)
        except Exception as e:
            print(f"Warning: Error extracting keywords: {e}")
            return []
        
def get_domain_info(domain):
    try:
        # Fetch domain WHOIS information
        domain_data = whois.whois(domain)
        return {
            'domain': domain,
            'creation_date': domain_data.creation_date,
            'registrar': domain_data.registrar,
            'emails': domain_data.emails,
            'admin_contact': domain_data.name,
            'privacy_protection': bool(domain_data.privacy)
        }
    except Exception as e:
        return {
            'domain': domain,
            'error': str(e)
        }
    
def get_ip_info(url):
    try:
        # Extract domain from URL
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        if not domain:
            domain = parsed_url.path
        
        # Remove www. if present
        domain = domain.replace('www.', '')
        
        # Get IP address of the domain
        ip_address = socket.gethostbyname(domain)
        
        # Get additional information about the IP
        ip_info = IPWhois(ip_address)
        result = ip_info.lookup_whois()
        
        country = result.get("asn_country_code", "Unknown")
        owner = result.get("asn_description", "Unknown")
        
        return {
            "domain": domain,
            "ip_address": ip_address,
            "country": country,
            "owner": owner
        }
    except Exception as e:
        return {
            "error": str(e)
        }


def find_digital_for_all_merchants():
    db = next(get_db())
    merchants = db.query(models.Merchant).all()
    client = Groq(
        api_key=os.environ.get("GROQ_API_KEY3"),
    )
    # Retrieve all merchant IDs that have investigations
    investigation_merchants = db.query(models.investigations.merchant_id).distinct().all()
    investigation_merchant_ids = [merchant.merchant_id for merchant in investigation_merchants]
    for merchant in merchants:
        if merchant.id not in investigation_merchant_ids:
            continue
        try:
            print("********************************************")
            domain = merchant.domain
            if not domain:
                print(f"Skipping merchant {merchant.id} - No domain found")

            ip_info = get_ip_info(domain)
            print(ip_info)
            if 'error' in ip_info:
                print(f"Error getting IP info: {ip_info['error']}")

            data_info = get_domain_info(domain)
            print(data_info)
            if 'error' in data_info:
                print(f"Error getting domain info: {data_info['error']}")

            analyzer = WebsiteAnalyzer()
            analysis_results = analyzer.analyze_website(domain)
            if analysis_results['status'] != 'success':
                print(f"Error analyzing website: {analysis_results['error_message']}")

            print("\nProducts and Services:")
            for item in analysis_results['products_services']:
                print(f"- {item['name']}")
                print(f"  Description: {item['description']}")
                if item['price']:
                    print(f"  Price: {item['price']}")
            
            print("\nCategorization:")
            print(f"Primary Category: {analysis_results['categorization']['primary_category']}")
            print(f"B2B: {'Yes' if analysis_results['categorization']['is_b2b'] else 'No'}")
            print(f"B2C: {'Yes' if analysis_results['categorization']['is_b2c'] else 'No'}")
            
            print("\nTop Keywords:")
            for keyword, frequency in analysis_results['keywords']:
                print(f"- {keyword}: {frequency}")

            # Delete existing records for this merchant before inserting new ones
            db.query(models.digital_information).filter(
                models.digital_information.merchant_id == merchant.id
            ).delete()
            
            db.query(models.web_keywords).filter(
                models.web_keywords.merchant_id == merchant.id
            ).delete()

            # Insert new digital_information record
            digital_info = models.digital_information(
                merchant_id=merchant.id,
                domain=domain,
                server_ip_address=ip_info.get('ip_address'),
                server_ip_country=ip_info.get('country'),
                server_ip_owner=ip_info.get('owner'),
                domain_age=data_info.get('creation_date'),
                registrar=data_info.get('registrar'),
                owner_email=data_info.get('emails'),
                admin_contact=data_info.get('admin_contact'),
                privacy_protection=data_info.get('privacy_protection'),
                business_model=analysis_results['categorization']['primary_category']
            )
            db.add(digital_info)

            # Insert new web_keywords records
            for keyword, frequency in analysis_results['keywords']:
                web_keywords = models.web_keywords(
                    merchant_id=merchant.id,
                    keyword=keyword,
                    frequency=frequency
                )
                db.add(web_keywords)

            db.commit()

        except Exception as e:
            print(f"Error processing merchant {merchant.id}: {str(e)}")

        # Get website content using BeautifulSoup
        try:
            response = requests.get(f"{domain}", timeout=10)
            html_content = response.content.decode('utf-8')

            # Compile regular expressions
            phone_pattern = r'\+91\s*[6789]\d{9}|\b[6789]\d{9}\b'
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            link_pattern = r'href=[\'"]([^\'" >]+?)[\'"]\s*|https?://(?:wa\.me|[^\s<>"\']+)'

            # Find all matches
            phone_matches = list(set(re.findall(phone_pattern, html_content)))
            email_matches = list(set(re.findall(email_pattern, html_content)))
            link_matches = list(set(re.findall(link_pattern, html_content)))

            # Filter and clean links
            cleaned_links = []
            for link in link_matches:
                if link.startswith('http') or link.startswith('www'):
                    cleaned_links.append(link)

            # Delete existing records for this merchant
            db.query(models.web_contacts).filter(
                models.web_contacts.merchant_id == merchant.id
            ).delete()

            # Store phone numbers
            for phone in phone_matches:
                contact = models.web_contacts(
                    merchant_id=merchant.id,
                    contact_type='phone',
                    contact_value=phone
                )
                db.add(contact)

            # Store emails
            for email in email_matches:
                contact = models.web_contacts(
                    merchant_id=merchant.id,
                    contact_type='email', 
                    contact_value=email
                )
                db.add(contact)

            # Store links
            for link in cleaned_links:
                contact = models.web_contacts(
                    merchant_id=merchant.id,
                    contact_type='link',
                    contact_value=link
                )
                db.add(contact)

            db.commit()
            website_content = html_content

        except Exception as e:
            logger.error(f"Error scraping website {domain}: {str(e)}")
            website_content = ""

        prompt = (
            f"FRAUD INVESTIGATION MISSION: Conduct an exhaustive search across the internet to find ANY negative content, fraud reports, scams, or illegal activities about:\n\n"
            f"Merchant Name: {merchant.legal_name}\n"
            f"Merchant Domain: {domain}\n\n"
            "CRITICAL: Search ALL platforms and sources. Your mission:\n\n"
            "Find everything negative, fraudulent, or suspicious written about this merchant across the internet."
            "You must provide at least 5-6 different source links."
            "DISCARD ALL POSITIVE FINDINGS - We are only interested in risks and negative content."
            "Be extremely thorough and dig deep into all possible sources."
            "Return findings in this EXACT JSON format:\n"
            '{\n'
            '  "reviews_overview": {\n'
            '    "sentiment_status": "Summary of negative sentiment and risk level",\n'
            '    "reviews_summary": "Overview of all negative findings, fraud reports, and risks" \n'
            '  },\n'
            '  "individual_reviews": [\n'
            '    {\n'
            '      "source_website": "Name of website/platform where content was found",\n'
            '      "review_title": "Title or headline of the content (max 8-9 words)",\n'
            '      "review_summary": "Summary of complaint/fraud report/risk (max 50 words)",\n'
            '      "review_content": "Key details from source content (max 50-60 words)",\n'
            '      "url": "Direct URL to source content"\n'
            '    }\n'
            '    ... additional findings\n'
            '  ]\n'
            '}\n\n'
            "CRITICAL REQUIREMENTS:\n"
            "- Search ALL platforms and sources extensively\n" 
            "- Include ONLY negative content, fraud reports, scams, and risks\n"
            "- COMPLETELY IGNORE any positive reviews or content\n"
            "- Provide DIRECT URLs to source content\n"
            "- Find minimum 5-6 different sources\n"
            "- Be extremely detailed and thorough in your investigation\n"
            "- Uncover as much negative information as possible\n"
           )

        messages = [
            {
                "role": "system",
                "content": (
                    "You are an aggressive and uncompromising fraud investigator with a mission to expose ALL negative information. "
                    "Your goal is to uncover and thoroughly document every single complaint, criticism, and red flag about this merchant. "
                    "Search exhaustively across ALL platforms including but not limited to: "
                    "Reddit, Twitter, Facebook, LinkedIn, YouTube, news websites, Glassdoor, Indeed, BBB, Trustpilot, Yelp, "
                    "RipoffReport, Scam.com, consumer complaint forums, business review sites, and ANY other potential sources. "
                    "Focus EXCLUSIVELY on negative feedback, complaints, and red flags - completely discard positive content. "
                    "Be relentless in finding concerning information. Leave no stone unturned. "
                    "Provide extensive details and direct links to ALL concerning content. "
                    "Your investigation should be thorough, aggressive, and focused solely on exposing potential risks. "
                    "Do not summarize or downplay negative findings - provide full details of every concerning piece of information. "
                    "Your response should be comprehensive and detailed, highlighting ALL possible risks and red flags."
                ),
            },
            {   
                "role": "user",
                "content": (
                    prompt
                ),
            },
        ]

        client = OpenAI(api_key=os.environ.get("PERPLEXITY_API_KEY"), base_url="https://api.perplexity.ai")
        response = client.chat.completions.create(
            model="r1-1776",
            messages=messages,
        )

        # Extract the content from the response
        content = response.choices[0].message.content

        # Parse the JSON content from the response
        try:
            # Find the JSON part in the content and parse it
            json_pattern = r'\{[\s\S]*\}'
            json_match = re.search(json_pattern, content)
            if json_match:
                json_str = json_match.group()
                reviews_data = json.loads(json_str)
            else:
                reviews_data = {}

            # Delete existing records for this merchant
            db.query(models.reviews_merchant).filter(
                models.reviews_merchant.merchant_id == merchant.id
            ).delete()
            
            db.query(models.individual_reviews).filter(
                models.individual_reviews.merchant_id == merchant.id
            ).delete()

            # Store reviews overview
            reviews_overview = models.reviews_merchant(
                merchant_id=merchant.id,
                sentiment_status=reviews_data.get('reviews_overview', {}).get('sentiment_status', ''),
                reviews_summary=reviews_data.get('reviews_overview', {}).get('reviews_summary', '')
            )
            db.add(reviews_overview)

            # Store individual reviews
            for review in reviews_data.get('individual_reviews', []):
                individual_review = models.individual_reviews(
                    merchant_id=merchant.id,
                    source_website=review.get('source_website', ''),
                    review_title=review.get('review_title', ''),
                    review_summary=review.get('review_summary', ''),
                    review_content=review.get('review_content', ''),
                    url=review.get('url', '')
                )
                db.add(individual_review)

            db.commit()
            print("Successfully stored reviews data")

        except json.JSONDecodeError as e:
            print(f"Error parsing JSON content: {e}")
            print("Failed to store reviews data")

        prompt = (
            f"PRODUCT & SERVICES INVESTIGATION: Conduct an exhaustive search across the internet to find ALL products and services offered by:\n\n"
            f"Merchant Name: {merchant.legal_name}\n"
            f"Merchant Domain: {domain}\n\n"
            "CRITICAL: Search ALL platforms and sources. Your mission:\n\n"
            "Find detailed information about every product and service this merchant offers."
            "You must provide comprehensive details about their offerings."
            "Be extremely thorough and dig deep into all possible sources."
            "Return findings in this EXACT JSON format:\n"
            '{\n'
            '  "products_and_services_description_summary": "Comprehensive overview of all products and services offered",\n'
            '  "products_and_services": [\n'
            '    {\n'
            '      "product_and_service": "Name/title of specific product or service",\n'
            '      "summary": "Detailed description of the product/service (max 100 words)",\n'
            '      "price": "Price or price range if available",\n'
            '      "industry_and_location": "Industry classification and geographical availability"\n'
            '    }\n'
            '    ... additional products/services\n'
            '  ]\n'
            '}\n\n'
            "CRITICAL REQUIREMENTS:\n"
            "- Search ALL platforms and sources extensively\n"
            "- Include ALL products and services offered\n"
            "- Provide detailed descriptions and pricing when available\n"
            "- Be extremely detailed and thorough in your investigation\n"
            "- Focus on accuracy and completeness of information\n"
        )

        messages = [
            {
                "role": "system", 
                "content": (
                    "You are a thorough business analyst tasked with documenting ALL products and services offered by this merchant. "
                    "Your goal is to create a comprehensive catalog of their offerings with detailed descriptions. "
                    "Search exhaustively across ALL platforms including but not limited to: "
                    "Company website, LinkedIn, social media, business directories, review sites, "
                    "industry databases, news articles, and ANY other potential sources. "
                    "Focus on gathering accurate and detailed information about products, services, pricing and availability. "
                    "Be thorough in documenting specifications, features, and use cases. "
                    "Provide extensive details about industry classification and geographical reach. "
                    "Your investigation should be comprehensive and meticulous. "
                    "Your response should paint a complete picture of their business offerings."
                ),
            },
            {
                "role": "user",
                "content": prompt,
            },
        ]

        client = OpenAI(api_key=os.environ.get("PERPLEXITY_API_KEY"), base_url="https://api.perplexity.ai")
        response = client.chat.completions.create(
            model="r1-1776", 
            messages=messages,
        )

        # Extract the content from the response
        content = response.choices[0].message.content

        # Parse the JSON content from the response
        try:
            # Find the JSON part in the content and parse it
            json_pattern = r'\{[\s\S]*\}'
            json_match = re.search(json_pattern, content)
            if json_match:
                json_str = json_match.group()
                products_data = json.loads(json_str)
            else:
                products_data = {}

            # Delete existing records for this merchant
            db.query(models.products_and_services).filter(
                models.products_and_services.merchant_id == merchant.id
            ).delete()
            
            db.query(models.products_and_services_details).filter(
                models.products_and_services_details.merchant_id == merchant.id
            ).delete()

            # Store products and services overview
            products_overview = models.products_and_services(
                merchant_id=merchant.id,
                products_and_services_description_summary=products_data.get('products_and_services_description_summary', '')
            )
            db.add(products_overview)

            # Store individual products and services details
            for product in products_data.get('products_and_services', []):
                product_detail = models.products_and_services_details(
                    merchant_id=merchant.id,
                    product_and_service=product.get('product_and_service', ''),
                    summary=product.get('summary', ''),
                    price=product.get('price', ''),
                    industry_and_location=product.get('industry_and_location', '')
                )
                db.add(product_detail)

            db.commit()
            print("Successfully stored products and services data")

        except json.JSONDecodeError as e:
            print(f"Error parsing JSON content: {e}")
            print("Failed to store products and services data")

        prompt = (
            f"NEWS & RISK INVESTIGATION: Conduct an exhaustive search across the internet for any fraud, scam, illegal or risky activities related to:\n\n"
            f"Merchant Name: {merchant.legal_name}\n"
            f"Merchant Domain: {domain}\n\n"
            "CRITICAL: Search ALL news sources, articles and platforms. Your mission:\n\n"
            "Find detailed information about any incidents, news or reports involving fraud, scams, illegal activities or risks."
            "You must provide comprehensive details about each incident found."
            "Be extremely thorough and dig deep into all possible sources."
            "Return findings in this EXACT JSON format:\n"
            '{\n'
            '  "news_fraud_scam_illegal_risk_summary": "Comprehensive summary of any fraud/scam/illegal/risky activities found",\n'
            '  "news_sentiment": "One line sentiment analysis of overall findings",\n'
            '  "news_incidents": [\n'
            '    {\n'
            '      "incident_title": "Title of the specific incident/news",\n'
            '      "summary": "Brief summary of the incident/news",\n'
            '      "content": "Full content/details of the news article",\n'
            '      "time_of_upload": "Date/time when incident was reported",\n'
            '      "link": "Source URL where incident was found"\n'
            '    }\n'
            '    ... additional incidents\n'
            '  ]\n'
            '}\n\n'
            "CRITICAL REQUIREMENTS:\n"
            "- Search ALL news sources and platforms extensively\n" 
            "- Include ALL incidents and reports found\n"
            "- Provide detailed summaries and full content\n"
            "- Include dates and source links\n"
            "- Be extremely detailed and thorough in your investigation\n"
            "- Focus on accuracy and completeness of information\n"
        )

        messages = [
            {
                "role": "system",
                "content": (
                    "You are a thorough risk analyst tasked with finding ANY fraud, scam, illegal or risky activities "
                    "related to this merchant. Your goal is to create a comprehensive report of all incidents. "
                    "Search exhaustively across ALL platforms including but not limited to: "
                    "News websites, social media, review sites, complaint boards, legal databases, "
                    "government notices, regulatory filings, and ANY other potential sources. "
                    "Focus on gathering accurate and detailed information about each incident. "
                    "Be thorough in documenting what happened, when it occurred, and the source. "
                    "Provide extensive details about the nature and impact of each incident. "
                    "Your investigation should be comprehensive and meticulous. "
                    "Your response should paint a complete picture of any risks or issues."
                ),
            },
            {
                "role": "user",
                "content": prompt,
            },
        ]

        client = OpenAI(api_key=os.environ.get("PERPLEXITY_API_KEY"), base_url="https://api.perplexity.ai")
        response = client.chat.completions.create(
            model="r1-1776",
            messages=messages,
        )

        # Extract the content from the response
        content = response.choices[0].message.content

        # Parse the JSON content from the response
        try:
            # Find the JSON part in the content and parse it
            json_pattern = r'\{[\s\S]*\}'
            json_match = re.search(json_pattern, content)
            if json_match:
                json_str = json_match.group()
                news_data = json.loads(json_str)
            else:
                news_data = {}

            # Delete existing records for this merchant
            db.query(models.news_fraud_scam_illegal_risk).filter(
                models.news_fraud_scam_illegal_risk.merchant_id == merchant.id
            ).delete()
            
            db.query(models.news_incidents).filter(
                models.news_incidents.merchant_id == merchant.id
            ).delete()

            # Store news fraud risk summary
            news_risk = models.news_fraud_scam_illegal_risk(
                merchant_id=merchant.id,
                news_fraud_scam_illegal_risk_summary=news_data.get('news_fraud_scam_illegal_risk_summary', ''),
                news_sentiment=news_data.get('news_sentiment', '')
            )
            db.add(news_risk)

            # Store individual news incidents
            for incident in news_data.get('news_incidents', []):
                news_incident = models.news_incidents(
                    merchant_id=merchant.id,
                    incident_title=incident.get('incident_title', ''),
                    summary=incident.get('summary', ''),
                    content=incident.get('content', ''),
                    time_of_upload=incident.get('time_of_upload', ''),
                    link=incident.get('link', '')
                )
                db.add(news_incident)

            db.commit()
            print("Successfully stored news risk and incidents data")

        except json.JSONDecodeError as e:
            print(f"Error parsing JSON content: {e}")
            print("Failed to store news risk and incidents data")

@router.get("/delete_job/{job_id}")
def delete_job(job_id: str):
    """
    Delete a job from the job manager
    """
    if job_id in jobManager:
        del jobManager[job_id]
        return {"message": f"Job {job_id} deleted successfully"}
    else:
        return {"message": f"Job {job_id} not found"}

def _find_digital_sync(job_id: str):
    # get merchant id from jobManager
    job = jobManager.get(job_id)
    if not job:
        print(f"Job {job_id} not found")
        return
    merchant_id = job.merchant_id
    print("merchant id", merchant_id)
    db = next(get_db())  # sync DB session
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        print(f"Merchant {merchant_id} not found")
        job.job_status = "failed"
        job.job_error = f"Merchant {merchant_id} not found"
        job.job_updated_at = datetime.now().isoformat()
        jobManager[job_id] = job
        return
    merchant_name = merchant.legal_name
    domain = merchant.domain
    print("found merchant", merchant_name)
    if not domain:
        print(f"Skipping merchant {merchant_name} - No domain found")
        job.job_status = "failed"
        job.job_error = f"Skipping merchant {merchant_name} - No domain found"
        job.job_updated_at = datetime.now().isoformat()
        jobManager[job_id] = job
        return
    
    job.job_status = "merchant found in database"
    job.job_updated_at = datetime.now().isoformat()
    jobManager[job_id] = job

    try:
        print(f"Processing merchant {merchant_name} with domain {domain}")
        print("Scraping raw data from the internet....")
        all_digital_data = full_scrapper_temp(domain, merchant_name)
        to_push_data = models.AllDFPDataTable(
            merchant_id=merchant.id,
            timestamp=datetime.now(),
            data=all_digital_data.model_dump()
        )
        print("fethcing raw data completed, pushing data to db")
        job.job_status = "scraping completed"
        job.job_updated_at = datetime.now().isoformat()
        jobManager[job_id] = job
        db.add(to_push_data)
        db.commit()
        print(f"Successfully updated digital footprint for {merchant_name}")
        job.job_status = "completed"
        job.job_updated_at = datetime.now().isoformat()
        job.job_error = None
        jobManager[job_id] = job
    except Exception as e:
        print(f"Error processing merchant {merchant_name}: {str(e)}")
        job.job_status = "failed"
        job.job_error = str(e)
        job.job_updated_at = datetime.now().isoformat()
        jobManager[job_id] = job
        db.rollback()
        print(f"Rolled back transaction for job {job_id}")
        return
    
    # Now need to construct the digital footprint from raw data
    print("constructing digital footprint")
    job.job_status = "constructing digital footprint"
    job.job_updated_at = datetime.now().isoformat()
    jobManager[job_id] = job
    digital_footprint = make_digital_footprint(all_digital_data)
    print("digital footprint constructed")
    # Save the digital footprint to the database
    to_push_dfp_data = models.DigitalFootPrint(
        merchant_id=merchant.id,
        timestamp=datetime.now(),
        data=digital_footprint.model_dump()
    )
    db.add(to_push_dfp_data)
    db.commit()
    print(f"Successfully updated digital footprint for {merchant_name}")
    job.job_status = "completed"   
    job.job_updated_at = datetime.now().isoformat()
    job.job_error = None
    jobManager[job_id] = job
    print("job completed", job)

async def find_digital_for_merchant(job_id: str):
    await asyncio.to_thread(_find_digital_sync, job_id)

@router.get("/{merchant_id}/update_digital_footprintV2")
async def update_digital_footprintV2(merchant_id: str):
    """
    Start a process in new thread to update the digital footprint for a specific merchant
    """
    print("function call recieved")
    job_id = str(uuid.uuid4())
    job = dfbmodels.DFPJobs(
        job_id=job_id,
        merchant_id=merchant_id,
        job_status="running",
        job_created_at=datetime.now().isoformat(),
        job_updated_at=datetime.now().isoformat()
    )
    print("creating job", job)
    # start the process
    asyncio.create_task(find_digital_for_merchant(job_id))
    print("job created", job)
    jobManager[job_id] = job
    return {"message": "Digital footprint update started", "job_id": job_id}


@router.get("/update_digital_footprint") 
def update_digital_footprint(current_user: models.User = Depends(get_current_user)):
    try:
        find_digital_for_all_merchants()
        return {"message": "Digital footprint updated successfully"}
    except Exception as e:
        return {"message": "Error updating digital footprint", "error": str(e)}


def calculate_percentiles():
    db = next(get_db())  # Get the database session
    try:
        merchants = db.query(models.Merchant).all()
        flags = db.query(models.flags).all()
        risk_scores = []

        # Calculate risk scores for merchants
        max_score = 0
        max_score_business_category = defaultdict(int)
        for merchant in merchants:
            merchant_flags = [flag for flag in flags if flag.merchant_id == merchant.id]
            score = sum(
                50 if flag.severity == "Critical" else
                20 if flag.severity == "High" else
                10 if flag.severity == "Medium" else
                5 if flag.severity == "Low" else 0
                for flag in merchant_flags
            )
            max_score = max(max_score, score)
            max_score_business_category[merchant.business_category] = max(max_score_business_category[merchant.business_category], score)
            db.query(models.risk_score_for_percentile).filter_by(merchant_id=merchant.id).delete()  # Avoid duplicate entries
            db.add(models.risk_score_for_percentile(merchant_id=merchant.id, risk_score=score))
            risk_scores.append((merchant.id, merchant.business_category, score))

        db.commit()  # Commit risk score updates

        # Calculate percentiles and quartile scores
        scores_by_category = defaultdict(list)
        for _, category, score in risk_scores:
            scores_by_category[category].append(score)

        # Clear existing monitoring data
        db.query(models.monitoring).delete()
        db.query(models.monitoring_category).delete()
        db.commit()  # Commit the deletes

        # Calculate overall segments
        all_scores = [s[2] for s in risk_scores]
        if not all_scores:  # Check if there are any scores
            print("No risk scores found")
            return
            
        sorted_scores = sorted(all_scores)
        print(f"Processing {len(sorted_scores)} total scores")

        # Calculate and store overall segments
        for i in range(100):
            print(f"Processing segment {i}")
            lower = i / 100  # Convert to percentile range 0-1
            upper = (i + 1) / 100
            
            # Calculate percentile for each score and filter those in current segment
            segment_scores = [s for s in all_scores 
                            if lower <= sorted_scores.index(s) / len(sorted_scores) < upper]
            
            try:
                monitoring_entry = models.monitoring(
                    segment_number=i,
                    segment_highest_risk=max(segment_scores) if segment_scores else 0,
                    segment_number_of_merchants=len(segment_scores),
                    created_at=datetime.now()
                )
                db.add(monitoring_entry)
                print(f"Added monitoring entry for segment {i} with {len(segment_scores)} merchants")
            except Exception as e:
                print(f"Error adding monitoring entry for segment {i}: {str(e)}")
                continue

        # Calculate and store category segments
        for category in scores_by_category:
            category_scores = scores_by_category[category]
            if not category_scores:  # Check if category has any scores
                print(f"No scores found for category {category}")
                continue
                
            sorted_cat_scores = sorted(category_scores)
            print(f"Processing {len(sorted_cat_scores)} scores for category {category}")
            
            for i in range(100):
                lower = i / 100  # Convert to percentile range 0-1
                upper = (i + 1) / 100
                
                # Calculate percentile for each score and filter those in current segment
                segment_scores = [s for s in category_scores 
                                if lower <= sorted_cat_scores.index(s) / len(sorted_cat_scores) < upper]
                
                try:
                    # Create entry even if no merchants in segment
                    category_entry = models.monitoring_category(
                        category=category,
                        segment_number=i,
                        segment_highest_risk=max(segment_scores) if segment_scores else 0,
                        segment_number_of_merchants=len(segment_scores),
                        created_at=datetime.now()
                    )
                    db.add(category_entry)
                    print(f"Added category monitoring entry for {category} segment {i} with {len(segment_scores)} merchants")
                except Exception as e:
                    print(f"Error adding category monitoring entry for {category} segment {i}: {str(e)}")
                    continue

        try:
            db.commit()
            print("Successfully committed monitoring entries")
        except Exception as e:
            print(f"Error committing monitoring entries: {str(e)}")
            db.rollback()

        # Calculate overall quartile scores
        sorted_scores = sorted(s[2] for s in risk_scores)
        q1_index = len(sorted_scores) // 4
        q2_index = len(sorted_scores) // 2
        q3_index = (3 * len(sorted_scores)) // 4
        
        overall_q1_score = sorted_scores[q1_index]
        overall_q2_score = sorted_scores[q2_index]
        overall_q3_score = sorted_scores[q3_index]

        for merchant_id, category, score in risk_scores:
            # Calculate overall percentile
            if(sorted_scores.index(score) != 0):
                percentile = ((sorted_scores.index(score) + 1)/ len(sorted_scores))*100
            else:
                percentile = 0

            # Calculate category-specific percentile and quartile scores
            sorted_category_scores = sorted(scores_by_category[category])
            if(sorted_category_scores.index(score) != 0):
                percentile_category = ((sorted_category_scores.index(score)+1) / len(sorted_category_scores))*100
            else:
                percentile_category = 0

            # Calculate category quartile scores
            cat_q1_index = len(sorted_category_scores) // 4
            cat_q2_index = len(sorted_category_scores) // 2
            cat_q3_index = (3 * len(sorted_category_scores)) // 4
            
            cat_q1_score = sorted_category_scores[cat_q1_index]
            cat_q2_score = sorted_category_scores[cat_q2_index]
            cat_q3_score = sorted_category_scores[cat_q3_index]

            existing_entry = db.query(models.risk_assessments).filter(models.risk_assessments.merchant_id == merchant_id).one_or_none()
            if existing_entry:
                existing_entry.percentile = percentile
                existing_entry.percentile_business_category = percentile_category
                existing_entry.risk_score_max = max_score
                existing_entry.risk_score = score
                existing_entry.risk_score_max_business_category = max_score_business_category[category]
                existing_entry.risk_score_025_rank = overall_q1_score
                existing_entry.risk_score_050_rank = overall_q2_score
                existing_entry.risk_score_075_rank = overall_q3_score
                existing_entry.risk_score_business_category_025_rank = cat_q1_score
                existing_entry.risk_score_business_category_050_rank = cat_q2_score
                existing_entry.risk_score_business_category_075_rank = cat_q3_score
            else:
                db.add(models.risk_assessments(
                    merchant_id=merchant_id,
                    percentile=percentile,
                    percentile_business_category=percentile_category,
                    risk_score_max=max_score,
                    risk_score_max_business_category=max_score_business_category[category],
                    risk_score=score,
                    risk_score_025_rank=overall_q1_score,
                    risk_score_050_rank=overall_q2_score,
                    risk_score_075_rank=overall_q3_score,
                    risk_score_business_category_025_rank=cat_q1_score,
                    risk_score_business_category_050_rank=cat_q2_score,
                    risk_score_business_category_075_rank=cat_q3_score
                ))

        try:
            db.commit()
            print("Successfully committed risk assessment entries")
        except Exception as e:
            print(f"Error committing risk assessment entries: {str(e)}")
            db.rollback()
            
    except Exception as e:
        print(f"Error in calculate_percentiles: {str(e)}")
        db.rollback()
    finally:
        db.close()


def make_merchant_graph():
    db = next(get_db())
    driver = None

    try:
        # Load tokenizer and model only for address comparison
        logger.info("Loading tokenizer and model for address embedding generation.")
        tokenizer = AutoTokenizer.from_pretrained("sentence-transformers/paraphrase-MiniLM-L6-v2")
        model = AutoModel.from_pretrained("sentence-transformers/paraphrase-MiniLM-L6-v2")

        # Initialize Neo4j connection
        driver = connect_to_neo4j()

        # Clear the entire graph first
        clear_graph(driver)

        # Get all merchants
        merchants = db.query(models.Merchant).all()
        logger.info(f"Retrieved {len(merchants)} merchants from the database.")

        with driver.session() as session:
            # Initialize caches
            embeddings_cache = {
                "registered_address": {},
                "operating_address": {}
            }
            contact_cache = []

            # Create all merchant nodes first
            create_merchant_nodes(session, merchants, db, embeddings_cache, contact_cache, tokenizer, model)

            # Create relationships
            create_all_relationships(session, embeddings_cache, contact_cache)

        return {"message": "Merchant graph created successfully"}

    except Exception as e:
        logger.exception("Error occurred while creating the merchant graph.")
        raise HTTPException(
            status_code=500,
            detail=f"Error creating merchant graph: {str(e)}"
        )
    finally:
        if driver:
            driver.close()
        db.close()

def connect_to_neo4j(max_retries=3):
    """Establish connection to Neo4j with retry logic."""
    retry_count = 0
    while retry_count < max_retries:
        try:
            logger.info("Attempting to connect to Neo4j database.")
            driver = GraphDatabase.driver(
                os.getenv("GRAPH_DATABASE_URL"),
                auth=(os.getenv("GRAPH_DATABASE_USER"), os.getenv("GRAPH_DATABASE_PASSWORD"))
            )
            # Test connection
            with driver.session() as session:
                session.run("RETURN 1").single()
            logger.info("Successfully connected to Neo4j.")
            return driver

        except exceptions.ServiceUnavailable:
            retry_count += 1
            logger.warning(f"Connection attempt {retry_count} failed. Retrying...")
            if retry_count == max_retries:
                logger.error("Neo4j database is not available after multiple attempts.")
                raise HTTPException(
                    status_code=503,
                    detail="Neo4j database is not available. Please check if Neo4j is running."
                )
            time.sleep(2)
        except exceptions.AuthError:
            logger.error("Neo4j authentication failed. Check credentials.")
            raise HTTPException(
                status_code=401,
                detail="Neo4j authentication failed. Please check credentials."
            )

def clear_graph(driver):
    """Clear all existing nodes and relationships from the graph."""
    with driver.session() as session:
        logger.info("Clearing existing graph data.")
        session.run("MATCH (n) DETACH DELETE n")
        logger.info("Graph cleared successfully.")



def create_merchant_nodes(session, merchants, db, embeddings_cache, contact_cache, tokenizer, model):
    """Create merchant nodes and cache their data."""
    logger.info("Creating merchant nodes...")
    for merchant in merchants:
        contact = db.query(models.contacts).filter(models.contacts.merchant_id == merchant.id).first()

        registered_address = contact.registeredAddress if contact else None
        operating_address = contact.operatingAddress if contact else None
        phone = contact.phone if contact else None
        email = contact.email if contact else None
        mobile = contact.mobile if contact else None

        client = Groq(api_key=os.getenv("GROQ_API_KEY"))
        # Extract address components using Groq
        if registered_address:
            try:
                registered_completion = client.chat.completions.create(
                    messages=[{
                        "role": "user", 
                        "content": f"Extract the flat/shop/house number, area, street name, city, state and country from this address. Return only a JSON object with these fields: {registered_address}"
                    }],
                    model="llama3-8b-8192",
                )
                # logger.info(f"Groq API Response: {registered_completion.choices[0].message.content}")
                
                try:
                    registered_components = json.loads(registered_completion.choices[0].message.content)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response: {registered_completion.choices[0].message.content}")
                    # Provide a default structure if JSON parsing fails
                    registered_components = {
                        "flat/shop/house number": "",
                        "street name": registered_address.split(',')[0] if registered_address else "",
                        "area": "",
                        "city": "",
                        "state": "",
                        "country": ""
                    }
            except Exception as e:
                logger.error(f"Error calling Groq API: {str(e)}")
                registered_components = {
                    "flat/shop/house number": "",
                    "street name": registered_address.split(',')[0] if registered_address else "",  
                    "area": "",
                    "city": "",
                    "state": "",
                    "country": ""
                }
        if operating_address:
            try:
                operating_completion = client.chat.completions.create(
                    messages=[{
                        "role": "user",
                        "content": f"Extract the flat/shop/house number, area, street name, city, state and country from this address. Return only a JSON object with these fields: {operating_address}"
                    }],
                    model="llama3-8b-8192", 
                )
                # logger.info(f"Groq API Response: {operating_completion.choices[0].message.content}")
                
                try:
                    operating_components = json.loads(operating_completion.choices[0].message.content)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response: {operating_completion.choices[0].message.content}")
                    # Provide a default structure if JSON parsing fails
                    operating_components = {
                        "flat/shop/house number": "",
                        "street name": operating_address.split(',')[0] if operating_address else "",
                        "area": "",
                        "city": "",
                        "state": "",
                        "country": ""
                    }
            except Exception as e:
                logger.error(f"Error calling Groq API: {str(e)}")
                operating_components = {
                    "flat/shop/house number": "",
                    "street name": operating_address.split(',')[0] if operating_address else "",
                    "area": "",
                    "city": "",
                    "state": "",
                    "country": ""
                }
        # Generate embeddings only for addresses

        # Create enhanced address strings with repeated components
        if registered_address:
            registered_enhanced = " ".join([
                f"{registered_components['flat/shop/house number']} " * 10,
                f"{registered_components['street name']} " * 3,
                f"{registered_components['area']} " * 2,
                registered_components['city'],
                registered_components['state'],
                registered_components['country']
            ]).strip()
            
        if operating_address:
            operating_enhanced = " ".join([
                f"{operating_components['flat/shop/house number']} " * 10,
                f"{operating_components['street name']} " * 3,
                f"{operating_components['area']} " * 2,
                operating_components['city'],
                operating_components['state'], 
                operating_components['country']
            ]).strip()
        if registered_address:
            embeddings_cache["registered_address"][merchant.id] = embed_text(registered_enhanced, tokenizer, model)
        if operating_address:
            embeddings_cache["operating_address"][merchant.id] = embed_text(operating_enhanced, tokenizer, model)

        # Cache contact details for exact matching
        contact_cache.append({
            'id': merchant.id,
            'phone': phone.strip().lower() if phone else None,
            'email': email.strip().lower() if email else None,
            'mobile': mobile.strip().lower() if mobile else None
        })

        # Create node
        session.run("""
            CREATE (m:Merchant {
                id: $id,
                phone: $phone,
                email: $email,
                mobile: $mobile,
                registered_address: $registered_address,
                operating_address: $operating_address
            })
        """, {
            'id': str(merchant.id),
            'phone': phone,
            'email': email,
            'mobile': mobile,
            'registered_address': registered_address,
            'operating_address': operating_address
        })
    logger.info("Merchant nodes created successfully.")

def create_all_relationships(session, embeddings_cache, contact_cache):
    """Create all relationships between merchant nodes."""
    logger.info("Creating relationships between merchants...")
    
    # Create address-based relationships
    create_address_relationships(session, embeddings_cache["registered_address"], 'SIMILAR_REGISTERED_ADDRESS')
    create_address_relationships(session, embeddings_cache["operating_address"], 'SIMILAR_OPERATING_ADDRESS')
    
    # Create exact match relationships
    create_exact_match_relationships(session, contact_cache)
    
    logger.info("Relationships created successfully.")

def create_address_relationships(session, attribute_embeddings, edge_type):
    """Create relationships based on address similarity."""
    for id1, embed1 in attribute_embeddings.items():
        for id2, embed2 in attribute_embeddings.items():
            if id1 >= id2:
                continue
            similarity = cosine_similarity(embed1, embed2)[0][0]
            if similarity > 0.8:
                session.run("""
                    MATCH (m1:Merchant {id: $id1}), (m2:Merchant {id: $id2})
                    CREATE (m1)-[r:CONNECTED {type: $edge_type, similarity: $similarity}]->(m2)
                    CREATE (m2)-[r2:CONNECTED {type: $edge_type, similarity: $similarity}]->(m1)
                """, {
                    'id1': str(id1),
                    'id2': str(id2),
                    'edge_type': edge_type,
                    'similarity': similarity
                })

def create_exact_match_relationships(session, contact_cache):
    """Create relationships based on exact matches of contact information."""
    # Create dictionaries for each attribute
    phone_dict = defaultdict(list)
    email_dict = defaultdict(list)
    mobile_dict = defaultdict(list)

    # Group merchants by their contact details
    for contact in contact_cache:
        if contact['phone']:
            phone_dict[contact['phone']].append(contact['id'])
        if contact['email']:
            email_dict[contact['email']].append(contact['id'])
        if contact['mobile']:
            mobile_dict[contact['mobile']].append(contact['id'])

    # Create relationships for each matching type
    for attr_dict, rel_type in [
        (phone_dict, 'SAME_PHONE'),
        (email_dict, 'SAME_EMAIL'),
        (mobile_dict, 'SAME_MOBILE')
    ]:
        for _, merchant_ids in attr_dict.items():
            if len(merchant_ids) > 1:
                for i in range(len(merchant_ids)):
                    for j in range(i + 1, len(merchant_ids)):
                        session.run("""
                            MATCH (m1:Merchant {id: $id1}), (m2:Merchant {id: $id2})
                            CREATE (m1)-[r1:CONNECTED {type: $rel_type, similarity: 1.0}]->(m2)
                            CREATE (m2)-[r2:CONNECTED {type: $rel_type, similarity: 1.0}]->(m1)
                        """, {
                            'id1': str(merchant_ids[i]),
                            'id2': str(merchant_ids[j]),
                            'rel_type': rel_type
                        })

@router.get("/test-neo4j")
async def test_neo4j_connection():
    try:
        logger.info("Testing Neo4j connection.")
        driver = GraphDatabase.driver(
            "bolt://localhost:7687",
            auth=("neo4j", "password")
        )
        with driver.session() as session:
            result = session.run("RETURN 1")
            result.single()
        driver.close()
        logger.info("Successfully connected to Neo4j.")
        return {"message": "Successfully connected to Neo4j"}

    except Exception as e:
        logger.exception("Failed to connect to Neo4j.")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to connect to Neo4j: {str(e)}"
        )
      

def get_merchant_graph(merchant_id: UUID, db: Session):
    try:
        logger.info(f"Fetching merchant graph for merchant ID: {merchant_id}")
        
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            logger.warning("Merchant not found.")
            raise HTTPException(status_code=404, detail="Merchant not found")

        driver = GraphDatabase.driver(
            "bolt://localhost:7687",
            auth=("neo4j", "password")
        )

        with driver.session() as session:
            # Modified query to fetch unique relationships
            result = session.run("""
                MATCH (m:Merchant {id: $merchant_id})-[r:CONNECTED]-(other:Merchant)
                WITH other.id as merchant_id, r.type as relationship_type
                RETURN DISTINCT merchant_id, relationship_type
            """, merchant_id=str(merchant_id))

            # Track unique connections using a set of tuples
            unique_connections = set()
            connections = []

            for record in result:
                # Create a tuple of merchant_id and relationship_type
                connection_key = (record["merchant_id"], record["relationship_type"])
                
                # Only process if this is a new unique connection
                if connection_key not in unique_connections:
                    unique_connections.add(connection_key)
                    
                    connected_merchant = db.query(models.Merchant).filter(
                        models.Merchant.id == UUID(record["merchant_id"])
                    ).first()

                    if connected_merchant:
                        connections.append({
                            "merchant_id": record["merchant_id"],
                            "merchant_name": connected_merchant.legal_name,
                            "relationship_type": record["relationship_type"]
                        })

        driver.close()
        logger.info("Merchant graph fetched successfully.")

        return {
            "merchant_id": str(merchant_id),
            "merchant_name": merchant.legal_name,
            "total_connections": len(connections),
            "connections": connections
        }

    except Exception as e:
        logger.exception("Error occurred while retrieving the merchant graph.")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving merchant graph: {str(e)}"
        )


def update_linkages():
    db = next(get_db())
    try:
        merchants = db.query(models.Merchant).all()
        make_merchant_graph()
        
        for merchant in merchants:
            try:
                data = get_merchant_graph(merchant.id, db)  # Pass db here
                print(f"Merchant Graph Data: {data}")
                
                # Delete existing entries for this merchant
                db.query(models.related).filter(models.related.merchant_id == merchant.id).delete()
                db.query(models.network_overview).filter(models.network_overview.merchant_id == merchant.id).delete()
                
                # Add new related entries
                directors_count = merchant.num_directors
                for connection in data["connections"]:
                    related_entry = models.related(
                        merchant_id=merchant.id,
                        related_entity_id=connection["merchant_id"],
                        related_entity_name=connection["merchant_name"],
                        relationship_type=connection["relationship_type"]
                    )
                    db.add(related_entry)
                    directors_count = directors_count + db.query(models.Merchant).filter(models.Merchant.id == connection["merchant_id"]).first().num_directors
                db.commit()
                
                # Calculate high risk connections
                high_risk_count = db.query(models.related).filter(
                    models.related.merchant_id == merchant.id,
                    models.related.relationship_type == "SAME_ADDRESS"
                ).count()
            
                if data["total_connections"] > 0:
                    # Calculate network risk score based on connections
                    network_risk_score = (high_risk_count / data["total_connections"]) * 100
                else:
                    network_risk_score = 0
                
                # Add network overview entry
                network_entry = models.network_overview(
                    merchant_id=merchant.id,
                    total_connections=data["total_connections"],
                    high_risk_connections=high_risk_count,
                    network_risk_score=int(network_risk_score),
                    directors_count=directors_count
                )
                db.add(network_entry)
                
                db.commit()
                
            except Exception as e:
                logger.exception(f"Error processing merchant {merchant.id}")
                db.rollback()
    except Exception as e:
        logger.exception("Error in update_linkages function.")
    finally:
        db.close()


# def run_update_linkages():
#     asyncio.run(update_linkages())

# def run_find_digital_for_all_merchants():
# #     asyncio.run(find_digital_for_all_merchants())

# async def calculate_red_flags(
#     db: Session = Depends(get_db)
# ):
#     merchants = db.query(models.Merchant).all()
#     for merchant in merchants:
#         try:
#             merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant.id).first()
#             if not merchant:
#                 raise HTTPException(status_code=404, detail="Merchant not found")
            
#             # Get merchants and convert to dict first
#             merchants = db.query(models.Merchant).all()
#             merchants_data = [{
#                 'mer_id': m.id,
#                 'mer_onboarding_timestamp': m.onboarding_date,
#                 'mer_incorporation_date': m.incorporation_date,
#                 'mer_first_txn_date': m.first_txn_date,
#                 'mer_lst_txn_date': m.last_txn_date,
#                 'mer_industry': m.industry,
#                 'mer_industry_mca': m.mca_description,
#                 'mer_fraud_flag': bool(m.fraud_flag) if m.fraud_flag is not None else False,
#                 'mer_avg_txn_size': float(m.avg_txn_size) if m.avg_txn_size else 0.0,
#                 'mer_total_txn': int(m.total_txn) if m.total_txn else 0,
#                 'mer_total_txn_fy': int(m.total_txn_fy) if m.total_txn_fy else 0,
#                 'mer_gst_risk_flag': m.gst_risk_flag,
#                 'mer_mca_fillings_risk_flag': m.mca_fillings_risk_flag,
#                 'mer_directors_risk_flag': m.directors_risk_flag,
#                 'mer_num_employees': m.num_employees,
#                 'mer_epfo_reg_status': m.epfo_reg_status,
#                 'mer_is_sanctioned': m.is_sanctioned,
#                 'mer_is_online_business': m.is_online_business,
#                 'mer_online_presence_flag': m.online_presence_flag,
#                 'mer_tax_irregularity_flag': m.tax_irregularity_flag,
#                 'mer_is_pan_compatible': m.is_pan_compatible,
#                 'mer_is_address_compatible': m.is_address_compatible,
#                 'mer_prior_fraud_investigation_flag': m.prior_fraud_investigation_flag,
#                 'mer_is_MCA_submission_taken': m.is_MCA_submission_taken,
#                 'mer_udyam_cert_flag': m.udyam_cert_flag
#             } for m in merchants]

#             # Get transactions and convert to dict first
#             transactions = db.query(models.transactions).all()
#             transactions_data = [{ 
#                 'txn_id': t.id,
#                 'mer_id': t.merchant_id,
#                 'business_type': t.merchant_type or '',
#                 'txn_timestamp': t.timestamp.isoformat() if t.timestamp else None,
#                 'txn_amount': float(t.amount) if t.amount else 0.0,
#                 'is_fraud_transaction': bool(t.is_fraud_transaction) if t.is_fraud_transaction is not None else False,
#                 'cx_id': str(t.cx_id) if t.cx_id else '',
#                 'cx_ip': t.cx_ip or '',
#                 'cx_device_id': t.cx_device_id or '',
#                 'cx_card_number': t.cx_card_number or '',
#                 'cx_pii_linkage_score': int(t.cx_pii_linkage_score) if t.cx_pii_linkage_score else 0,
#                 'is_cardholder_name_match': bool(t.is_cardholder_name_match) if t.is_cardholder_name_match is not None else True,
#                 'is_chargeback': bool(t.is_chargeback) if t.is_chargeback is not None else False,
#                 'is_cx_international': bool(t.is_cx_international) if t.is_cx_international is not None else False,
#                 'txn_status': bool(t.status == 'completed') if t.status else False,
#                 'is_cx_risky': bool(t.is_cx_risky) if t.is_cx_risky is not None else False,
#                 'invoice_amount': float(t.invoice_amount) if t.invoice_amount else 0.0,
#                 'is_cancelled': bool(t.is_cancelled) if t.is_cancelled is not None else False,
#                 'txn_currency': t.txn_currency or 'INR',
#                 'has_cx_complaint': bool(t.has_cx_complaint) if t.has_cx_complaint is not None else False,
#             } for t in transactions]

#             # Create DataFrames from the dictionaries
#             transactions_df = pd.DataFrame(transactions_data)
#             merchants_df = pd.DataFrame(merchants_data)
            
#             # Create the directory if it doesn't exist
#             os.makedirs('.ares/data', exist_ok=True)
            
#             # Save to csv 
#             transactions_df.to_csv('.ares/data/transactions.csv', index=False)
#             merchants_df.to_csv('.ares/data/merchants.csv', index=False)
            
#             # Verify the data was saved correctly
#             saved_df = pd.read_csv('.ares/data/transactions.csv')
#             if saved_df.empty:
#                 print("Warning: Saved transactions DataFrame is empty")
#             else:
#                 print("Data saved successfully")

#             main()
#             data_flags = run_this_please(merchant.id)
            
#             # Delete existing ML transaction flags
#             db.query(models.flags).filter(
#                 models.flags.merchant_id == merchant.id,
#                 models.flags.flag_type == 'transactions ML'
#             ).delete()
            
#             # Add new flags
#             for flag_text in data_flags:
#                 db.add(models.flags(
#                     merchant_id=merchant.id,
#                     flag_type="transactions ML",
#                     severity="high", 
#                     text=flag_text,
#                     timestamp=datetime.now()
#                 ))
            
#             db.commit()

#         except Exception as e:
#             db.rollback()
#             print(f"Error processing merchant {merchant.id}: {str(e)}")
#             continue

#     return {"message": "Red flags calculation completed"}

def update_key_metrics_internal():
    try:
        db = next(get_db())
        
        # Get all merchants
        merchants = db.query(models.Merchant).all()
        
        for merchant in merchants:
            # Get all transactions for this merchant
            transactions = db.query(models.transactions).filter(
                models.transactions.merchant_id == merchant.id,
                models.transactions.status == "completed"
            ).all()
            
            # Get all payouts for this merchant 
            payouts = db.query(models.payouts).filter(
                models.payouts.merchant_id == merchant.id,
                models.payouts.status == "completed"
            ).all()
            
            # Get all investigations for this merchant
            investigations = db.query(models.investigations).filter(
                models.investigations.merchant_id == merchant.id
            ).all()
            
            if transactions:
                # Calculate total amount and count
                total_amount = float(sum(t.amount for t in transactions))
                total_count = len(transactions)
                
                # Calculate total payout amount
                total_payout_amount = float(sum(p.amount for p in payouts))
                
                # Calculate current balance in ledger
                current_balance = float(total_amount - total_payout_amount)
                
                # Calculate average daily transactions
                if transactions:
                    earliest_txn = min(t.timestamp for t in transactions)
                    latest_txn = max(t.timestamp for t in transactions)
                    days_diff = (latest_txn - earliest_txn).days + 1
                    avg_daily_txns = float(total_count / days_diff)
                else:
                    avg_daily_txns = 0.0
                
                # Calculate average payout size
                avg_payout_size = float(total_payout_amount / len(payouts)) if payouts else 0.0
                
                # Count unique customers
                unique_customers = len(set(t.cx_id for t in transactions))
                
                # Update processing metrics
                metrics = db.query(models.processing_metrics).filter(
                    models.processing_metrics.merchant_id == merchant.id
                ).first()
                
                if not metrics:
                    metrics = models.processing_metrics(merchant_id=merchant.id)
                    db.add(metrics)
                
                metrics.monthly_volume = float(total_amount)
                metrics.average_ticket_size = float(total_amount / total_count if total_count else 0)
                
                # Update merchant metrics
                merchant.total_txn = total_count
                merchant.avg_txn_size = float(total_amount / total_count if total_count else 0)
                merchant.first_txn_date = earliest_txn.date() if transactions else None
                merchant.last_txn_date = latest_txn.date() if transactions else None
                active_investigation_cases = db.query(models.investigations).filter(
                    models.investigations.merchant_id == merchant.id,
                    models.investigations.status != "Closed"
                ).count()
                # Update key metrics
                prev_key_metrics = db.query(models.key_metrics).filter(
                    models.key_metrics.merchant_id == merchant.id
                ).first()
                
                if prev_key_metrics:
                    data = models.key_metrics(
                        merchant_id=merchant.id,
                        total_amount=float(total_amount),
                        total_count=total_count,
                        business_category=prev_key_metrics.business_category,
                        business_type=prev_key_metrics.business_type,
                        merchant_legalName=prev_key_metrics.merchant_legalName,
                        chargeback_percentage=float(prev_key_metrics.chargeback_percentage) if prev_key_metrics.chargeback_percentage else 0.0,
                        date_of_onboarding=prev_key_metrics.date_of_onboarding,
                        account_status=prev_key_metrics.account_status,
                        integration_types=prev_key_metrics.integration_types,
                        total_num_investigations=len(investigations),
                        current_balance_in_ledger=current_balance,
                        average_daily_transactions=avg_daily_txns,
                        average_payout_size=avg_payout_size,
                        no_of_unique_customers=unique_customers,
                        active_investigation_cases=active_investigation_cases
                    )
                    db.query(models.key_metrics).filter(
                        models.key_metrics.merchant_id == merchant.id
                    ).delete()
                    db.add(data)
                # update active_investigation_cases

                db.commit()
                
        return {"message": "Key metrics updated successfully"}
        
    except Exception as e:
        db.rollback()
        print(f"Error updating key metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    

def update_merchant_summary():
    # Get merchants and convert to dict first
    db = next(get_db())
    merchants = db.query(models.Merchant).all()
    merchants_data = [{
        'mer_id': m.id,
        'mer_onboarding_timestamp': m.onboarding_date,
        'mer_incorporation_date': m.incorporation_date,
        'mer_first_txn_date': m.first_txn_date,
        'mer_lst_txn_date': m.last_txn_date,
        'mer_industry': m.industry,
        'mer_business_industry': m.industry,
        'mer_industry_mca': m.mca_description,
        'mer_fraud_flag': bool(m.fraud_flag) if m.fraud_flag is not None else False,
        'mer_avg_txn_size': float(m.avg_txn_size) if m.avg_txn_size else 0.0,
        'mer_total_txn': int(m.total_txn) if m.total_txn else 0,
        'mer_total_txn_fy': int(m.total_txn_fy) if m.total_txn_fy else 0,
        'mer_gst_risk_flag': m.gst_risk_flag,
        'mer_mca_fillings_risk_flag': m.mca_fillings_risk_flag,
        'mer_directors_risk_flag': m.directors_risk_flag,
        'mer_num_employees': m.num_employees,
        'mer_epfo_reg_status': m.epfo_reg_status,
        'mer_is_sanctioned': m.is_sanctioned,
        'mer_is_online_business': m.is_online_business,
        'mer_online_presence_flag': m.online_presence_flag,
        'mer_tax_irregularity_flag': m.tax_irregularity_flag,
        'mer_is_pan_compatible': m.is_pan_compatible,
        'mer_is_address_compatible': m.is_address_compatible,
        'mer_prior_fraud_investigation_flag': m.prior_fraud_investigation_flag,
        'mer_is_MCA_submission_taken': m.is_MCA_submission_taken,
        'mer_udyam_cert_flag': m.udyam_cert_flag
    } for m in merchants]

    print(merchants_data)
    # Get transactions and convert to dict first
    transactions_data = []
    transactions = db.query(models.transactions).all()
    transactions_data = [{ 
        'txn_id': t.id,
        'mer_id': t.merchant_id,
        'business_type': t.merchant_type or '',
        'mer_business_industry': t.merchant_type or '',
        'txn_timestamp': t.timestamp.isoformat() if t.timestamp else None,
        'txn_amount': float(t.amount) if t.amount else 0.0,
        'is_fraud_transaction': bool(t.is_fraud_transaction) if t.is_fraud_transaction is not None else False,
        'cx_id': str(t.cx_id) if t.cx_id else '',
        'cx_ip': t.cx_ip or '',
        'cx_device_id': t.cx_device_id or '',
        'cx_card_number': t.cx_card_number or '',
        'cx_pii_linkage_score': int(t.cx_pii_linkage_score) if t.cx_pii_linkage_score else 0,
        'is_cardholder_name_match': bool(t.is_cardholder_name_match) if t.is_cardholder_name_match is not None else True,
        'is_chargeback': bool(t.is_chargeback) if t.is_chargeback is not None else False,
        'is_cx_international': bool(t.is_cx_international) if t.is_cx_international is not None else False,
        'txn_status': bool(t.status == 'completed') if t.status else False,
        'is_cx_risky': bool(t.is_cx_risky) if t.is_cx_risky is not None else False,
        'invoice_amount': float(t.invoice_amount) if t.invoice_amount else 0.0,
        'is_cancelled': bool(t.is_cancelled) if t.is_cancelled is not None else False,
        'txn_currency': t.txn_currency or 'INR',
        'has_cx_complaint': bool(t.has_cx_complaint) if t.has_cx_complaint is not None else False,
    } for t in transactions]

    print(transactions_data)
    # Create DataFrames from the dictionaries
    transactions_df = pd.DataFrame(transactions_data)
    merchants_df = pd.DataFrame(merchants_data)
    print(transactions_df)   
    print(merchants_df)
        
    # Create the directory if it doesn't exist
    os.makedirs('.ares/data', exist_ok=True)
        
    # Save to csv 
    transactions_df.to_csv('.ares/data/transactions.csv', index=False)
    merchants_df.to_csv('.ares/data/merchants.csv', index=False)
        
    # Verify the data was saved correctly
    saved_df = pd.read_csv('.ares/data/transactions.csv')
    if saved_df.empty:
        print("Warning: Saved transactions DataFrame is empty ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
    else:
        print("Data saved successfully")

    # Update summary for each merchant
    for merchant in merchants:
        summary = summer_is_mine(str(merchant.id))
        merchant.summery = summary
        db.add(merchant)
    
    try:
        db.commit()
        return {"message": "Merchant summaries updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating merchant summaries: {str(e)}")


@router.get("/generate_red_flag")
def generate_red_flag_api(current_user: models.User = Depends(get_current_user)):
    try:
        generate_red_flag()
        return {"message": "Red flags generated successfully"}
    except Exception as e:
        return {"message": "Error generating red flags", "error": str(e)}

@router.get("/update_key_metrics")
def update_key_metrics(current_user: models.User = Depends(get_current_user)):
    return update_key_metrics_internal()


@router.get("/update_risk_percentiles_and_risk_scores")
def update_risk_percentiles_and_risk_scores(current_user: models.User = Depends(get_current_user)):
    try:
        calculate_percentiles()
        return {"message": "Risk percentiles and risk scores updated successfully"}
    except Exception as e:
        return {"message": "Error updating risk percentiles and risk scores", "error": str(e)}


@router.get("/update_linkages")
def update_linkages_api(current_user: models.User = Depends(get_current_user)):
    make_merchant_graph()
    update_linkages()
    return {"message": "Linkages updated successfully"}

def linkages_update():
    make_merchant_graph()
    update_linkages()

@router.get("/update_summery")
def update_summery(current_user: models.User = Depends(get_current_user)):
    try:
        update_merchant_summary()
        return {"message": "Summery updated successfully"}
    except Exception as e:
        return {"message": "Error updating summery", "error": str(e)}

# scheduler.add_job(
#     func=linkages_update,
#     trigger=IntervalTrigger(minutes=int(os.getenv("linkage_update_interval"))), 
#     id="update_linkages",  
#     replace_existing=True,
# )

# scheduler.add_job(
#     func=calculate_percentiles,
#     trigger=IntervalTrigger(minutes=int(os.getenv("percentile_update_interval"))), 
#     id="calculate_percentiles",  
#     replace_existing=True,
# )

# scheduler.add_job(
#     func=find_digital_for_all_merchants,
#     trigger=IntervalTrigger(minutes=int(os.getenv("find_digital_for_all_merchants_interval"))), 
#     id="find_digital_footprint_for_all_merchants",  
#     replace_existing=True,
# )

# scheduler.add_job(
#     func=response_time_series_update,
#     trigger=IntervalTrigger(minutes=int(os.getenv("response_time_series_update_interval"))), 
#     id="response_time_series_update",  
#     replace_existing=True,
# )


# scheduler.add_job(
#     func=update_summery,
#     trigger=IntervalTrigger(minutes=int(os.getenv("update_summery_interval"))), 
#     id="update_summery",  
#     replace_existing=True,
# )

# scheduler.add_job(
#     func=update_key_metrics,
#     trigger=IntervalTrigger(minutes=int(os.getenv("update_key_metrics_interval"))), 
#     id="update_key_metrics",  
#     replace_existing=True,
# )

scheduler.start()

def embed_text(text, tokenizer, model):
    # Tokenize the input text
    inputs = tokenizer(text, return_tensors='pt', truncation=True, padding=True)
    
    # Get the embeddings from the model
    with torch.no_grad():
        outputs = model(**inputs)
    
    # Use the mean of the token embeddings as the sentence embedding
    embeddings = outputs.last_hidden_state.mean(dim=1)
    
    return embeddings
