import json
import os
import re
import requests
import datetime
import time
from fastapi import APIRouter, Depends, HTTPException, Path
from sqlalchemy.orm import Session
from uuid import UUID, uuid4
from pydantic import BaseModel
from ..models import models

from .apiProtection import get_current_user

from ..database import get_db
from ..static import parsers_cr, prompts_cr
from ..utils.llm import create_and_run_pipeline
from ..models.models_cr import MetricValueForAllMerchants, PushToPineconeRequest
from ..utils.rag import insert_pdf_to_pinecone, get_context_from_pinecone, dense_index, query_pinecone, get_embedding, upsert_record_to_pinecone_with_retries
from typing import List

router = APIRouter()

# Api's for fetching raw data
def call_perplexity(legal_name: str, industries: str):
    url = "https://api.perplexity.ai/chat/completions"

    if industries:
        prompt = f"""
            Company Name: {legal_name}
            Industry: {industries}
            """
    else:
        prompt = f"""
            Company Name: {legal_name}
            """
        
    payload = {
        "model": "sonar-deep-research",
        "messages": [
            {"role": "user", "content": prompts_cr.PROMPT_DEEP_RESEARCH + prompt},
        ],
    }
    headers = {
        "Authorization": f"Bearer {os.environ.get('PERPLEXITY_API_KEY')}",
        "Content-Type": "application/json"
    }
    print("Calling Perplexity API...")
    response = requests.post(url, json=payload, headers=headers)

    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail="Error fetching data from Perplexity API")

    content = response.json()
    if "choices" not in content or len(content["choices"]) == 0:
        raise HTTPException(status_code=500, detail="No choices found in response")
    # Extract the content from the response
    content = content["choices"][0]["message"]["content"]

    # Parse the content to remove the <think> </think>  and the content between them
    cleaned_text = re.sub(r"<think>.*?</think>", "", content, flags=re.DOTALL).strip()

    # remove the ``` from the start and end of the text
    cleaned_text = cleaned_text.replace("```", "").strip()

    # remove json from the start of the text
    cleaned_text = cleaned_text.replace("json", "").strip()
    # parse the cleaned text to JSON
    print("Cleaned text:", cleaned_text)
    try:
        data = json.loads(cleaned_text)
    except json.JSONDecodeError as e:
        print("Error parsing JSON:", e)
        raise HTTPException(status_code=500, detail="Error parsing JSON response") from e
    return data

class ExternalDataRequest(BaseModel):
    merchant_id: UUID
    product: str

@router.post("/updateExternalData")
async def update_external_data(
    request: ExternalDataRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get external data for a given merchant.
    """
    assert isinstance(request.merchant_id, UUID), "merchant_id should be a UUID"
    assert request.product in ["insolvency", "ipo"]

    if request.product == "insolvency":
        merchant = db.query(models.probe_merchant).filter(models.probe_merchant.merchant_id == request.merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")
    elif request.product == "ipo":
        merchant = db.query(models.ListingCompanies).filter(models.ListingCompanies.id == request.merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")
    else:
        raise HTTPException(status_code=400, detail="Invalid product type. Only 'insolvency' and 'ipo' are supported.")
    
    # get the legal name and industry of the merchant
    legal_name = merchant.legal_name
    merchant_id = merchant.id

    if request.product == "insolvency":
        industries = db.query(models.probe_industry_segments).filter(models.probe_industry_segments.merchant_id == merchant_id).all()
        if not industries:
            raise HTTPException(status_code=404, detail="No industries found for the merchant")
    else:
        industries = []
    
    industries = ",".join([ind.industry for ind in industries])
    print("Legal Name: ", legal_name)
    print("Industries: ", industries)
    data = call_perplexity(legal_name, industries)
    print("Data fetched from Perplexity API")
    print("Data: ", data)
    new_issues_count = 0
    new_issues = {}
    for issue_type, issues in data.items():
        print(f"Processing issue type: {issue_type}")
        new_issues[issue_type] = []
        for i, issue in enumerate(issues):
            for k, v in issue.items():
                if "issue" in k:
                    issue_text = v
                    # find the most similar issue in the database
                    results = query_pinecone(
                        name_space="external_data",
                        query=issue_text,
                        filters={
                            "merchant_id": str(merchant_id),
                            "issue_type": issue_type,
                        },
                        top_k=1
                    )
                    if results and len(results) > 0:
                        for hit in results:
                            score = hit['score']
                            if score > 0.70:
                                # if the score is greater than 0.8, we can consider it as a duplicate
                                print(f"Duplicate issue found: {issue_text}")
                                break
                            else:
                                # if the score is less than 0.7, we can consider it as a new issue
                                print(f"New issue found: {issue_text}")
                                new_issues[issue_type].append(issue)
                                new_issues_count += 1
                                break
                    else:
                        # if no results found, we can consider it as a new issue
                        print(f"New issue found: {issue_text}")
                        new_issues[issue_type].append(issue)
                        new_issues_count += 1
                        break

    # insert the data into the database
    if new_issues_count == 0:
        return {
            "success": False,
            "message": "No new issues found",
            "data": None
        }
    
    # insert the new issues into the database
    data_to_insert = models.ExternalInsights(
        merchant_id=merchant_id,
        insight_type="external_data",
        insight_value=new_issues,
        product = request.product,
        created_at=datetime.datetime.now(),
    )
    db.add(data_to_insert)
    db.commit()

    print("Data inserted into the postgres database")
    print("number of new issues: ", new_issues_count)

    # insert the new issues into the database
    # for issue_type, issues in new_issues.items():
    #     for issue in issues:
    #         for k, v in issue.items():
    #             if "issue" in k:
    #                 issue_text = v
    #                 # insert the issue into the database
    #                 _id = f"{str(merchant_id)}_{str(datetime.datetime.now().timestamp()).replace('.', '')}_{str(uuid4())[:5]}"
    #                 data_to_upsert = {
    #                     "id": _id,
    #                     "values": get_embedding(issue_text),
    #                     "metadata": {
    #                         "text": issue_text,
    #                         "merchant_id": str(merchant_id),
    #                         "issue_type": issue_type,
    #                     }
    #                 }
    #                 _ = upsert_record_to_pinecone_with_retries([data_to_upsert], "external_data", f"Failed to upsert entry: {issue_text} in external data for merchant_id: {merchant_id} and issue_type: {issue_type}")
    print("number of new issues: ", len(new_issues))

    if request.product == "insolvency":
        issue_type_to_red_flag_type = {
            "legal_regulatory_compliance": "insolvency_external_legal",
            "operational_disruptions": "insolvency_external_operational",
            "financial_warning_signs": "insolvency_external_financial",
            "industry_macroeconomic": "insolvency_external_industry",
            "executive_workforce_developments": "insolvency_external_executive",
            "sentiment_brand_reputation": "insolvency_external_brand",
            "financial_disclosures": "insolvency_external_disclosures",
        }
    elif request.product == "ipo":
        issue_type_to_red_flag_type = {
            "legal_regulatory_compliance": "ipo_external_legal",
            "operational_disruptions": "ipo_external_operational",
            "financial_warning_signs": "ipo_external_financial",
            "industry_macroeconomic": "ipo_external_industry",
            "executive_workforce_developments": "ipo_external_executive",
            "sentiment_brand_reputation": "ipo_external_brand",
            "financial_disclosures": "ipo_external_disclosures",
        }
    else:
        raise HTTPException(status_code=400, detail="Invalid product type. Only 'insolvency' and 'ipo' are supported.")

    # Identify and put red flags
    if request.product == "insolvency":
        for issue_type, issues in new_issues.items():
            all_issues_of_this_type = []
            for issue in issues:
                for k, v in issue.items():
                    if "issue" in k:
                        issue_text = v
                        all_issues_of_this_type.append(issue_text)
            identify_and_put_red_flag(
                merchant_id=merchant_id,
                db=db,
                content=all_issues_of_this_type,
                red_flag_type=issue_type_to_red_flag_type[issue_type]
            )
    return {
        "success": True,
        "message": "External data updated successfully in the database",
        "data": data
    }

@router.get("/{merchant_id}/getAuditDisclosures")
async def get_legal_cases(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get legal cases for a given merchant.
    """
    merchant = db.query(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    
    data = merchant.disclosures_auditor_report
    return {
        "success": True,
        "message": "Audit disclosures fetched successfully",
        "data": data
    }

@router.get("/{merchant_id}/updateFlagsFromAuditorDisclosures")
async def update_flags_from_auditor_disclosures(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get flags from auditor disclosures for a given merchant.
    """
    merchant = db.query(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    
    merchant_id = str(merchant.merchant_id)
    # Get the audit data from the merchant
    audit_data = db.query(models.probe_financials).filter(models.probe_financials.merchant_id == merchant_id, models.probe_financials.report_has_adverse_remarks == "true").order_by(models.probe_financials.year.desc()).limit(3).all()
    if not audit_data:
        return {
            "success": True,
            "message": "No audit data found",
            "data": None
        }
    
    # Process and format the audit data and limit to last 3 years
    data = []
    for audit in audit_data:
        data_per_year = {
            "year": audit.year,
            "auditors_comment" : audit.auditor_comments,
        }
        data.append(data_per_year)

    # Call gpt to get the flags

    # write small prompt
    system_prompt = prompts_cr.PROMPT_AUDIT_QUALIFIED
    user_prompt = """
    Audit data:
    """
    user_prompt += str(data)

    response = create_and_run_pipeline(
        system_prompt=system_prompt,
        parser=parsers_cr.audit_concerns_parser,
        user_inp=user_prompt,
    )

    if not response:
        raise HTTPException(status_code=500, detail="Error fetching data from GPT API")
    
    # Identify unique audit concerns
    unique_audit_concerns = []
    for audit in response.get("audit_concerns", []):
        # search for the audit concern on pinecone
        results = query_pinecone(
            name_space="audit_flags",
            query=audit['audit_concern'],
            filters={
                "$and": [
                    {"merchant_id": str(merchant_id)},
                    {"year": audit['year']},
                ]
            },
            top_k=1
        )
        if results and len(results) > 0:
            for hit in results:
                score = hit['score']
                if score > 0.70:
                    # if the score is greater than 0.8, we can consider it as a duplicate
                    print(f"Duplicate issue found: {audit['audit_concern']}")
                    break
                else:
                    # if the score is less than 0.7, we can consider it as a new issue
                    print(f"New issue found: {audit['audit_concern']}")
                    unique_audit_concerns.append(audit)
                    break
        else:
            # if no results found, we can consider it as a new issue
            unique_audit_concerns.append(audit)

    if len(unique_audit_concerns) == 0:
        return {
            "success": False,
            "message": "No new audit concerns found",
            "data": None
        }
    
    data_to_insert = models.ExternalInsights(
        merchant_id=merchant_id,
        insight_type="audit_flags",
        insight_value=unique_audit_concerns,
        created_at=datetime.datetime.now(),
    )
    db.add(data_to_insert)
    db.commit()
    print("Data inserted into the postgres database")

    print("number of unique audit concerns: ", len(unique_audit_concerns))
    # Insert the unique audit concerns into the pinecone
    for audit in unique_audit_concerns:
        _id = f"{str(merchant_id)}_{audit['year']}_{str(datetime.datetime.now().timestamp()).replace('.', '')}_{str(uuid4())[:5]}"
        data_to_upsert = {
            "id": _id,
            "values": get_embedding(audit['audit_concern']),
            "metadata": {
                "text": audit['audit_concern'],
                "merchant_id": str(merchant_id),
                "year": audit['year'],
            }
        }
        _ = upsert_record_to_pinecone_with_retries([data_to_upsert], "audit_flags", f"Failed to upsert entry: {audit['audit_concern']} in audit flags for merchant_id: {merchant_id} and year: {audit['year']}")

    # Identify and put red flags
    audit_concerns = unique_audit_concerns
    audit_concerns = [audit['audit_concern'] + f"\n\n Audit report year: {audit['year']}" for audit in audit_concerns]
    identify_and_put_red_flag(
        merchant_id=merchant_id,
        db=db,
        content=audit_concerns,
        red_flag_type="insolvency_external_audit"
    )
    return {
        "success": True,
        "message": "Audit flags updated successfully in the database",
        "data": response
    }

@router.post("/{merchant_id}/pushAnnualReportToPinecone/")
async def push_annual_report_to_pinecone(
    path_to_pdf: PushToPineconeRequest,
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Push annual report to Pinecone
    """
    # Check if the path exists
    if not os.path.exists(path_to_pdf.path_to_pdf):
        raise HTTPException(status_code=404, detail="Path does not exist")
    
    # Insert PDF files to Pinecone
    counts = insert_pdf_to_pinecone(path_to_pdf.path_to_pdf, path_to_pdf.year_of_reports, str(merchant_id))
    if counts == 0:
        return {
            "success": False,
            "message": "No PDF files found in the given path",
            "data": None
        }

    return {
        "success": True,
        "message": "Annual report pushed to Pinecone successfully",
        "data": {
            "count_of_records_inserted": counts,
        }
    }


@router.get("/{merchant_id}/updateInsightsFromAnnualReport")
async def update_insights_from_annual_report(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    merchant = db.query(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    
    filters = {
        "merchant_id": str(merchant_id),
    }
    context = get_context_from_pinecone(merchant_id=str(merchant_id), filters=filters)

    if not context:
        raise HTTPException(status_code=404, detail="No context found in Pinecone")
    
    # Call gpt to get the insights
    # write small prompt
    system_prompt = prompts_cr.PROMPT_FINANCIAL_REPORT_INSIGHT
    user_prompt = f"""
    Company Name: {merchant.legal_name}
    Queries and Context:
    {context}
    """
    
    response = create_and_run_pipeline(
        system_prompt=system_prompt,
        parser=parsers_cr.financial_report_parser,
        user_inp=user_prompt,
    )
    if not response:
        raise HTTPException(status_code=500, detail="Error fetching data from GPT API")
    # Identify unique insights
    unique_insights = []
    print("number of insights: ", len(response.get("insights_and_flags", [])))
    for i, insight in enumerate(response.get("insights_and_flags", [])):
        print("index: ", i, " year: ", insight.keys())
        # search for the insight on pinecone
        results = query_pinecone(
            name_space="annual_report_insights",
            query=insight['insight'],
            filters={
                "$and": [
                    {"merchant_id": str(merchant_id)},
                    {"year": insight['year']},
                ]
            },
            top_k=1
        )
        if results and len(results) > 0:
            for hit in results:
                score = hit['score']
                if score > 0.70:
                    # if the score is greater than 0.8, we can consider it as a duplicate
                    print(f"Duplicate issue found: {insight['insight']}")
                    break
                else:
                    # if the score is less than 0.7, we can consider it as a new issue
                    print(f"New issue found: {insight['insight']}")
                    unique_insights.append(insight)
                    break
        else:
            # if no results found, we can consider it as a new issue
            unique_insights.append(insight)
    # Insert the insights into the database
    data_to_insert = models.ExternalInsights(
        merchant_id=merchant_id,
        insight_type="annual_report_insights",
        insight_value=unique_insights,
        created_at=datetime.datetime.now(),
    )
    db.add(data_to_insert)
    db.commit()
    print("Data inserted into the postgres database")

    print("number of unique insights: ", len(unique_insights)) 
    # Insert the unique insights into the pinecone
    for i, insight in enumerate(unique_insights):
        print("inserting data...", i)
        _id = f"{str(merchant_id)}_{insight['year']}_{str(datetime.datetime.now().timestamp()).replace(".", "")}_{str(uuid4())[:5]}"
        data_to_upsert = {
            "id": _id,
            "values": get_embedding(insight['insight']),
            "metadata": {
                "text": insight['insight'],
                "merchant_id": str(merchant_id),
                "year": insight['year'],
            }
        }
        _ = upsert_record_to_pinecone_with_retries([data_to_upsert], "annual_report_insights", f"Failed to upsert entry: {i} in annual report for merchant_id: {merchant_id} and year: {insight['year']}")

    # Identify and put red flags
    metadatas = [{"year": insight['year']} for insight in unique_insights]
    insights_and_flags = [insight['insight'] for insight in unique_insights]
    assert len(unique_insights) == len(metadatas), "Length of insights and metadatas should be same"
    identify_and_put_red_flag(
        merchant_id=merchant_id,
        db=db,
        content=insights_and_flags,
        red_flag_type="insolvency_external_annualReport",
        metadatas=metadatas
    )

    return {
        "success": True,
        "message": "Insights updated successfully in the database",
        "data": response
    }

def identify_and_put_red_flag(
    merchant_id: UUID,
    db: Session,
    content: List[str],
    red_flag_type: str,
    metadatas: List[str] = None
):
    # Summarize the red flag and give it a severity level
    system_prompt = prompts_cr.PROMPT_TO_SUMMARIZE_RED_FLAGS
    print("number of red flags: ", len(content))
    for i, con in enumerate(content):
        print("index: ", i)
        user_prompt = f"""
        Given the following red flag, please summarize it and give it a severity level:
        red flag content: {con} 
        """
        resp = create_and_run_pipeline(
            system_prompt=system_prompt,
            parser=parsers_cr.external_red_flags_parser,
            user_inp=user_prompt,
        )
        if not resp:
            raise HTTPException(status_code=500, detail="Error fetching data from GPT API")
        
        # Insert the red flag into the database
        data_to_insert = models.merchant_red_flags(
            merchant_id=merchant_id,
            rule_code=red_flag_type,
            description=resp["red_flag"],
            severity=resp["severity"],
            metric_values=metadatas[i] if metadatas else None,
            created_at=datetime.datetime.now(),
            updated_at=datetime.datetime.now(),
            metric_data_timestamp=None,
            notes=None,
            category=red_flag_type,
        )

        db.add(data_to_insert)
        db.commit()
        time.sleep(1.5)
    return

@router.get("/{merchant_id}/unearnedRevenue")
async def get_unearned_revenue(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get unearned revenue for a given merchant.
    """
    merchant = db.query(models.probe_merchant).filter(models.probe_merchant.merchant_id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    
    year = "2024"
    # Process and format the unearned revenue data
    data = []
    system_prompt = prompts_cr.PROMPT_UNEARNED_REVENUE
    user_prompt = f"""
    Company Name: {merchant.legal_name}
    Year: {year}
    """

    filters = {
        "$or": [{"merchant_id": str(merchant_id)},
        {"year": year}]
    }
    context = get_context_from_pinecone(merchant_id=str(merchant_id), filters=filters)
    if not context:
        raise HTTPException(status_code=404, detail="No context found in Pinecone")
    
    user_prompt += f"""
    Context: {context}
    """

    response = create_and_run_pipeline(
        system_prompt=system_prompt,
        parser=parsers_cr.unearned_revenue_parser,
        user_inp=user_prompt,
    )

    return {
        "success": True,
        "message": "Unearned revenue fetched successfully",
        "data": response
    }