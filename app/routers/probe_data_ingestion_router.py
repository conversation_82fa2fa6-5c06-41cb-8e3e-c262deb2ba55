from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, status
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any, List
import pandas as pd
import json
import logging
from uuid import uuid4
from datetime import datetime
import asyncio
from io import BytesIO

from ..database import get_db
from ..models.probe_model import probe_merchant, probe_financials
from .apiProtection import get_current_user
from ..models import models
from ..utils.openai_service import OpenAIService
from ..schemas.request_models import ProbeDataUploadResponse, TableProcessingResult

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

router = APIRouter()

# Table configuration - maps table names to their corresponding models and sheets
TABLE_CONFIG = {
    "merchant": {
        "model": probe_merchant,
        "sheet_names": ["merchant", "company", "basic_info"],  # Possible sheet names to look for
        "description": "Company basic information including legal name, CIN, PAN, etc."
    },
    "financials": {
        "model": probe_financials,
        "sheet_names": ["financials", "financial", "balance_sheet", "profit_loss"],  # Possible sheet names
        "description": "Financial statements including balance sheet, profit & loss, and cash flow data"
    }
}

class ProbeDataProcessor:
    """Handles the processing of probe data through OpenAI integration"""
    
    def __init__(self, db: Session):
        self.db = db
        try:
            self.openai_service = OpenAIService()
        except ValueError as e:
            logger.error(f"OpenAI service initialization failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="OpenAI service not properly configured. Please check environment variables."
            )
        
    async def process_excel_file(self, file: UploadFile) -> Dict[str, Any]:
        """Process uploaded Excel file and return processing results"""
        logger.info(f"Starting Excel file processing for {file.filename}")
        try:
            # Read Excel file
            logger.info("Step 1: Reading Excel file")
            excel_data = await self._read_excel_file(file)
            logger.info("Step 1: Excel file read successfully")
            
            # Process each configured table
            results = {}
            processed_merchant_id = None
            logger.info("Step 2: Processing tables sequentially: merchant, then financials")
            for table_name in ["merchant", "financials"]:  # Process only these two tables for now
                logger.info(f"--- Processing table: {table_name} ---")

                if table_name == "financials" and not processed_merchant_id:
                    logger.warning("Skipping financials processing because merchant processing failed or was skipped.")
                    results[table_name] = {
                        "status": "skipped",
                        "records_processed": 0,
                        "message": "Financials processing skipped because merchant data was not processed successfully."
                    }
                    continue
                
                # Extract data for this table
                logger.info(f"Step 2a: Extracting data for {table_name}")
                table_data = await self._extract_table_data(excel_data, table_name)
                
                if table_data is not None:
                    logger.info(f"Step 2a: Data extracted for {table_name}. Shape: {table_data.shape}")
                    # Send to OpenAI for structuring
                    logger.info(f"Step 2b: Structuring data for {table_name} with OpenAI")
                    structured_data = await self._structure_data_with_openai(
                        table_data, table_name
                    )
                    
                    if structured_data:
                        logger.info(f"Step 2b: OpenAI structuring successful for {table_name}. Records: {len(structured_data)}")
                        if table_name == "financials":
                            logger.info(f"Assigning merchant_id '{processed_merchant_id}' to {len(structured_data)} financial records")
                            for record in structured_data:
                                record['merchant_id'] = processed_merchant_id
                        
                        # Save to database
                        logger.info(f"Step 2c: Saving structured data for {table_name} to database")
                        saved_records = await self._save_to_database(
                            structured_data, table_name
                        )
                        
                        if saved_records:
                            logger.info(f"Step 2c: Data for {table_name} saved successfully. Records: {len(saved_records)}")
                            if table_name == "merchant":
                                processed_merchant_id = saved_records[0].merchant_id
                                logger.info(f"Captured new merchant_id: {processed_merchant_id}")

                            results[table_name] = {
                                "status": "success",
                                "records_processed": len(saved_records),
                                "message": f"Successfully processed {table_name} data"
                            }
                        else:
                            logger.error(f"Step 2c: Failed to save data for {table_name}")
                            if table_name == "merchant":
                                processed_merchant_id = None # Invalidate on failure
                            results[table_name] = {
                                "status": "failed",
                                "records_processed": 0,
                                "message": f"Failed to save {table_name} data"
                            }
                    else:
                        logger.error(f"Step 2b: OpenAI failed to structure data for {table_name}")
                        if table_name == "merchant":
                            processed_merchant_id = None
                        results[table_name] = {
                            "status": "failed",
                            "records_processed": 0,
                            "message": f"OpenAI failed to structure {table_name} data"
                        }
                else:
                    logger.warning(f"Step 2a: No data found for {table_name} in Excel file")
                    if table_name == "merchant":
                        processed_merchant_id = None
                    results[table_name] = {
                        "status": "skipped",
                        "records_processed": 0,
                        "message": f"No data found for {table_name} in Excel file"
                    }
            
            logger.info(f"Excel file processing finished for {file.filename}")
            return results
            
        except Exception as e:
            logger.error(f"Error processing Excel file: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error processing Excel file: {str(e)}"
            )
    
    async def _read_excel_file(self, file: UploadFile) -> Dict[str, pd.DataFrame]:
        """Read Excel file and return all sheets as DataFrames"""
        try:
            # Reset file pointer to beginning
            await file.seek(0)
            
            # Read file content
            content = await file.read()
            
            # Validate file content
            if len(content) == 0:
                raise ValueError("File is empty")
            
            # Try to read as Excel file with better error handling
            try:
                # Use BytesIO to properly handle the file content
                excel_buffer = BytesIO(content)
                
                # Read all sheets with openpyxl engine (for .xlsx files)
                excel_data = pd.read_excel(excel_buffer, sheet_name=None, engine='openpyxl')
                
            except Exception as openpyxl_error:
                logger.warning(f"Failed with openpyxl engine: {str(openpyxl_error)}")
                
                # Try with xlrd engine for older Excel files (.xls)
                try:
                    excel_buffer = BytesIO(content)
                    excel_data = pd.read_excel(excel_buffer, sheet_name=None, engine='xlrd')
                    logger.info("Successfully read file with xlrd engine")
                    
                except ImportError:
                    logger.error("xlrd library not available for .xls files")
                    raise ValueError(
                        f"Cannot read Excel file. openpyxl failed: {str(openpyxl_error)[:100]}... "
                        f"For .xls files, please convert to .xlsx format or install xlrd library."
                    )
                except Exception as xlrd_error:
                    logger.error(f"Failed with xlrd engine: {str(xlrd_error)}")
                    
                    # Try without specifying engine (let pandas decide)
                    try:
                        excel_buffer = BytesIO(content)
                        excel_data = pd.read_excel(excel_buffer, sheet_name=None)
                        logger.info("Successfully read file with default engine")
                        
                    except Exception as default_error:
                        # If all engines fail, provide a detailed error message
                        raise ValueError(
                            f"Unable to read Excel file with any available engine. "
                            f"openpyxl error: {str(openpyxl_error)[:100]}... "
                            f"xlrd error: {str(xlrd_error)[:100]}... "
                            f"default error: {str(default_error)[:100]}... "
                            f"Please ensure the file is a valid Excel file (.xlsx recommended)"
                        )
            
            if not excel_data:
                raise ValueError("No sheets found in Excel file")
            
            logger.info(f"Found {len(excel_data)} sheets: {list(excel_data.keys())}")
            return excel_data
            
        except ValueError as ve:
            logger.error(f"Validation error reading Excel file: {str(ve)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid Excel file: {str(ve)}"
            )
        except Exception as e:
            logger.error(f"Error reading Excel file: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error reading Excel file: {str(e)}"
            )
    
    async def _extract_table_data(self, excel_data: Dict[str, pd.DataFrame], table_name: str) -> pd.DataFrame:
        """Extract data for a specific table from Excel sheets"""
        config = TABLE_CONFIG[table_name]
        
        # Look for sheets that match the table configuration
        for sheet_name, df in excel_data.items():
            for pattern in config["sheet_names"]:
                if pattern.lower() in sheet_name.lower():
                    logger.info(f"Found data for {table_name} in sheet: {sheet_name}")
                    return df
        
        # If no specific sheet found, try to find data based on column patterns
        for sheet_name, df in excel_data.items():
            if table_name == "merchant":
                # Look for merchant-specific columns
                merchant_columns = ['cin', 'legal_name', 'pan', 'company_name']
                if any(col.lower() in [c.lower() for c in df.columns] for col in merchant_columns):
                    logger.info(f"Found merchant data based on columns in sheet: {sheet_name}")
                    return df
            elif table_name == "financials":
                # Look for financial-specific columns
                financial_columns = ['revenue', 'profit', 'assets', 'liabilities', 'cash_flow']
                if any(col.lower() in [c.lower() for c in df.columns] for col in financial_columns):
                    logger.info(f"Found financial data based on columns in sheet: {sheet_name}")
                    return df
        
        logger.warning(f"No data found for table: {table_name}")
        return None
    
    async def _structure_data_with_openai(self, data: pd.DataFrame, table_name: str) -> List[Dict]:
        """Send data to OpenAI to structure it according to DB schema"""
        logger.info(f"Structuring data for table '{table_name}' using OpenAI.")
        try:
            # Get the database schema for this table
            schema = self._get_table_schema(table_name)
            
            # Convert DataFrame to JSON for OpenAI
            data_json = data.to_json(orient='records', date_format='iso')
            
            # Create prompt for OpenAI
            prompt = self._create_openai_prompt(data_json, schema, table_name)
            logger.info(f"OpenAI Prompt for {table_name}:\n{prompt}")
            
            # Call OpenAI service
            structured_data = await self.openai_service.structure_table_data(
                prompt, table_name
            )
            
            if structured_data:
                # Log the response from OpenAI
                try:
                    # Pretty-print JSON if possible
                    response_log = json.dumps(structured_data, indent=2)
                except (TypeError, ValueError):
                    response_log = str(structured_data)
                logger.info(f"OpenAI Response for {table_name}:\n{response_log}")
            else:
                logger.warning(f"OpenAI returned no data for {table_name}.")

            return structured_data
            
        except Exception as e:
            logger.error(f"Error structuring data with OpenAI for {table_name}: {str(e)}", exc_info=True)
            return None
    
    def _get_table_schema(self, table_name: str) -> Dict[str, str]:
        """Get the database schema for a table"""
        model = TABLE_CONFIG[table_name]["model"]
        schema = {}
        
        for column in model.__table__.columns:
            column_type = str(column.type)
            schema[column.name] = {
                "type": column_type,
                "nullable": column.nullable,
                "primary_key": column.primary_key
            }
        
        return schema
    
    def _create_openai_prompt(self, data_json: str, schema: Dict, table_name: str) -> str:
        """Create a prompt for OpenAI to structure the data"""
        config = TABLE_CONFIG[table_name]
        
        prompt = f"""
        You are a data processing expert. I need you to structure the following raw data according to a specific database schema.

        Table: {table_name}
        Description: {config['description']}
        
        Database Schema:
        {json.dumps(schema, indent=2)}
        
        Raw Data:
        {data_json}
        
        Instructions:
        1. Map the raw data fields to the correct database schema fields
        2. Handle data type conversions (dates, numbers, booleans, etc.)
        3. Generate UUIDs for any ID fields that are empty
        4. Set reasonable defaults for missing required fields
        5. Validate and clean the data
        6. Return the structured data as a JSON array of objects
        
        Important:
        - Each object in the array should match the database schema exactly
        - Use null for missing optional fields
        - Convert dates to ISO format (YYYY-MM-DD)
        - For merchant_id fields, generate a new UUID if not provided
        - Ensure all required fields have values
        
        Return only valid JSON without any explanations or markdown formatting.
        """
        
        return prompt
    
    async def _save_to_database(self, structured_data: List[Dict], table_name: str) -> List[Any]:
        """Save structured data to the database and return saved instances."""
        logger.info(f"Saving {len(structured_data)} records to table '{table_name}'.")
        saved_instances = []
        try:
            model = TABLE_CONFIG[table_name]["model"]
            
            # Convert data to model instances
            for record in structured_data:
                # Handle UUID generation for merchant_id if needed
                if 'merchant_id' in record and not record['merchant_id']:
                    record['merchant_id'] = str(uuid4())
                
                # Create model instance
                instance = model(**record)
                self.db.add(instance)
                saved_instances.append(instance)
            
            # Commit transaction
            self.db.commit()
            for instance in saved_instances:
                self.db.refresh(instance)
            logger.info(f"Successfully saved {len(saved_instances)} records to {table_name}")
            return saved_instances
            
        except Exception as e:
            logger.error(f"Error saving data to {table_name}: {str(e)}", exc_info=True)
            self.db.rollback()
            return []


@router.post("/upload-excel", response_model=ProbeDataUploadResponse)
async def upload_probe_data_excel(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Upload and process probe data from Excel file
    
    This endpoint:
    1. Accepts an Excel file upload
    2. Processes merchant and financials tables
    3. Uses OpenAI to structure the data according to DB schema
    4. Saves the processed data to the database
    """
    
    # Comprehensive file validation
    if not file.filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No file provided"
        )
    
    # Validate file extension
    file_ext = file.filename.lower().split('.')[-1]
    if file_ext not in ['xlsx', 'xls']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid file type '{file_ext}'. File must be an Excel file (.xlsx or .xls)"
        )
    
    # Validate content type if provided
    if file.content_type and not any(ct in file.content_type for ct in [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'application/excel'
    ]):
        logger.warning(f"Unexpected content type: {file.content_type}, but proceeding based on file extension")
    
    # Check file size (reasonable limit for Excel files)
    file.file.seek(0, 2)  # Seek to end
    file_size = file.file.tell()
    await file.seek(0)  # Reset to beginning
    
    if file_size == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File is empty"
        )
    
    # Set a reasonable size limit (50MB)
    max_size = 50 * 1024 * 1024  # 50MB
    if file_size > max_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large ({file_size} bytes). Maximum size is {max_size} bytes"
        )
    
    logger.info(f"Processing Excel file: {file.filename} ({file_size} bytes) for user: {current_user.email}")
    
    try:
        # Initialize processor
        processor = ProbeDataProcessor(db)
        
        # Process the file
        results = await processor.process_excel_file(file)
        
        # Calculate overall status
        total_processed = sum(r.get("records_processed", 0) for r in results.values())
        failed_tables = [table for table, result in results.items() if result["status"] == "failed"]
        
        overall_status = "success"
        if failed_tables:
            overall_status = "partial_success" if any(r["status"] == "success" for r in results.values()) else "failed"
        
        response_data = {
            "success": overall_status in ["success", "partial_success"],
            "message": f"Processed {total_processed} total records across {len(results)} tables",
            "overall_status": overall_status,
            "table_results": results,
            "processed_by": current_user.email,
            "processed_at": datetime.now().isoformat()
        }
        
        logger.info(f"Excel processing completed with status: {overall_status}")
        return ProbeDataUploadResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing Excel file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}"
        )


@router.get("/tables/config")
async def get_table_configuration(
    current_user: models.User = Depends(get_current_user)
):
    """
    Get the configuration for supported tables
    
    Returns information about which tables are supported for data ingestion
    and their expected data formats.
    """
    
    config_info = {}
    for table_name, config in TABLE_CONFIG.items():
        # Get schema information
        model = config["model"]
        schema_info = {}
        
        for column in model.__table__.columns:
            schema_info[column.name] = {
                "type": str(column.type),
                "nullable": column.nullable,
                "primary_key": column.primary_key
            }
        
        config_info[table_name] = {
            "description": config["description"],
            "expected_sheet_names": config["sheet_names"],
            "schema": schema_info
        }
    
    return {
        "supported_tables": config_info,
        "processing_order": ["merchant", "financials"],
        "notes": [
            "Tables are processed sequentially in the specified order",
            "Merchant data should be processed before financials due to foreign key dependencies",
            "Sheet names are matched using case-insensitive pattern matching"
        ]
    }


@router.get("/processing/status/{processing_id}")
async def get_processing_status(
    processing_id: str,
    current_user: models.User = Depends(get_current_user)
):
    """
    Get the status of a data processing operation
    
    This is a placeholder for future implementation of asynchronous processing
    with background tasks and status tracking.
    """
    
    # TODO: Implement actual status tracking with background tasks
    return {
        "processing_id": processing_id,
        "status": "not_implemented",
        "message": "Status tracking is not yet implemented. Processing is currently synchronous."
    }


@router.post("/debug/validate-excel")
async def debug_validate_excel(
    file: UploadFile = File(...),
    current_user: models.User = Depends(get_current_user)
):
    """
    Debug endpoint to validate Excel file without processing
    
    This endpoint helps diagnose Excel file issues by:
    1. Checking file metadata
    2. Testing file reading with different engines
    3. Showing sheet structure
    """
    
    debug_info = {
        "filename": file.filename,
        "content_type": file.content_type,
        "file_size": 0,
        "validation_results": {},
        "sheet_info": {},
        "errors": []
    }
    
    try:
        # Get file size
        await file.seek(0, 2)
        debug_info["file_size"] = file.file.tell()
        await file.seek(0)
        
        # Read content
        content = await file.read()
        debug_info["actual_content_size"] = len(content)
        
        if len(content) == 0:
            debug_info["errors"].append("File is empty")
            return debug_info
        
        # Test different engines
        engines_to_test = ['openpyxl']
        
        # Add xlrd if available
        try:
            import xlrd
            engines_to_test.append('xlrd')
        except ImportError:
            debug_info["validation_results"]["xlrd"] = {
                "success": False,
                "error": "xlrd library not installed"
            }
        
        # Test default engine (no engine specified)
        engines_to_test.append('default')
        
        for engine in engines_to_test:
            try:
                excel_buffer = BytesIO(content)
                if engine == 'default':
                    test_data = pd.read_excel(excel_buffer, sheet_name=None)
                else:
                    test_data = pd.read_excel(excel_buffer, sheet_name=None, engine=engine)
                
                debug_info["validation_results"][engine] = {
                    "success": True,
                    "sheets_found": len(test_data),
                    "sheet_names": list(test_data.keys())
                }
                
                # Get detailed sheet info for the first successful engine
                if not debug_info["sheet_info"]:
                    for sheet_name, df in test_data.items():
                        debug_info["sheet_info"][sheet_name] = {
                            "rows": len(df),
                            "columns": len(df.columns),
                            "column_names": list(df.columns)[:10],  # First 10 columns
                            "sample_data": df.head(2).to_dict() if len(df) > 0 else {}
                        }
                
            except Exception as e:
                debug_info["validation_results"][engine] = {
                    "success": False,
                    "error": str(e)
                }
        
        # Check if any engine worked
        successful_engines = [engine for engine, result in debug_info["validation_results"].items() 
                            if result.get("success", False)]
        
        debug_info["can_be_processed"] = len(successful_engines) > 0
        debug_info["recommended_engine"] = successful_engines[0] if successful_engines else None
        
    except Exception as e:
        debug_info["errors"].append(f"Critical error: {str(e)}")
    
    return debug_info
