import asyncio
import csv
import json
import os
import re
from datetime import datetime
from typing import List, Dict, Any, Tuple
from uuid import uuid4
import pandas as pd
from sqlalchemy.orm import Session
from openai import AsyncOpenAI
from ...database import get_db
from ..models.customer_models import SMSList, Customer, CustomerTransaction, CustomerEvent
from .sms_constants import FINANCIAL_KEYWORDS, CURRENCY_KEYWORDS, BANK_NAMES, FINTECH_NAMES, NBFC_NAMES

class SMSProcessor:
    """Handles SMS processing with classification and OpenAI analysis"""
    
    def __init__(self, use_openai: bool = True):
        self.use_openai = use_openai
        if self.use_openai:
            self.openai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.chunk_size = 2
        
    def classify_sms(self, sender: str, message: str) -> str:
        """
        Classify SMS into two types:
        1. Marketing/Spam/OTP messages
        2. Financial messages (debit, credit, UPI, etc.)
        """
        print(f"DEBUG: Classifying SMS - Sender: {sender[:20]}..., Message: {message[:50]}...")
        
        # Check for OTP/marketing patterns
        if self._is_otp_or_marketing(sender, message):
            print(f"DEBUG: SMS classified as MARKETING - Sender: {sender[:20]}...")
            return "marketing"
        
        # Check for financial patterns
        if self._is_financial_message(sender, message):
            print(f"DEBUG: SMS classified as FINANCIAL - Sender: {sender[:20]}...")
            return "financial"
        
        # Default to marketing if unclear
        print(f"DEBUG: SMS classified as MARKETING (default) - Sender: {sender[:20]}...")
        return "marketing"
    
    def _is_otp_or_marketing(self, sender: str, message: str) -> bool:
        """Check if SMS is OTP or marketing related"""
        text = message.lower()
        
        # OTP patterns
        otp_patterns = [
            r'\botp\b', r'\bpassword\b', r'\bverification code\b', r'\bverify\b',
            r'\bconfirm\b', r'\bactivate\b', r'\bone time password\b', r'\bsecurity code\b',
            r'\bauthentication code\b', r'\blogin code\b', r'\baccess code\b',
            r'\bpin\b.*\bcode\b', r'\bcode\b.*\bverification\b', r'\bverification\b.*\bcode\b'
        ]
        
        # Marketing patterns
        marketing_patterns = [
            r'\boffer\b', r'\bdiscount\b', r'\bsale\b', r'\bpromo\b', r'\bdeal\b',
            r'\bwin\b', r'\bprize\b', r'\bcontest\b', r'\bwinner\b', r'\bclaim\b',
            r'\bunlimited\b', r'\bfree\b', r'\bcashback\b', r'\breward\b',
            r'\bnewsletter\b', r'\bupdate\b', r'\bnotification\b', r'\balert\b'
        ]
        
        # Check patterns
        for pattern in otp_patterns + marketing_patterns:
            if re.search(pattern, text):
                return True
        
        # Check for mobile number sender
        if self._is_mobile_number(sender):
            return True
            
        return False
    
    def _is_financial_message(self, sender: str, message: str) -> bool:
        """Check if SMS contains financial information"""
        text = message.lower()
        
        # Financial keywords
        financial_patterns = FINANCIAL_KEYWORDS + CURRENCY_KEYWORDS + BANK_NAMES + FINTECH_NAMES + NBFC_NAMES
        
        # Transaction patterns
        transaction_patterns = [
            r'\bdebit\b', r'\bcredit\b', r'\bupi\b', r'\btransfer\b', r'\bpayment\b',
            r'\bwithdrawal\b', r'\bdeposit\b', r'\btransaction\b', r'\baccount\b',
            r'\bbalance\b', r'\bamount\b', r'\brs\.?\s*\d+', r'\b₹\s*\d+',
            r'\bpaid\b', r'\breceived\b', r'\bsent\b', r'\bcharged\b', r'\bdeducted\b'
        ]
        
        # Check patterns
        for pattern in financial_patterns + transaction_patterns:
            if re.search(pattern, text):
                return True
        
        return False
    
    def _is_mobile_number(self, sender: str) -> bool:
        """Check if sender is a mobile number"""
        cleaned = re.sub(r'^[+]?', '', sender)
        cleaned = re.sub(r'[^\d]', '', cleaned)
        
        patterns = [
            r'^[6-9]\d{9}$',           # 10-digit Indian mobile
            r'^91[6-9]\d{9}$',         # Indian mobile with 91
            r'^\d{10,15}$'             # Any 10-15 digit number
        ]
        
        return any(re.match(pattern, cleaned) for pattern in patterns)
    
    def process_csv_file(self, file_path: str, customer_id: str) -> Dict[str, Any]:
        """Process CSV file and classify SMS messages"""
        print(f"INFO: Processing CSV file: {file_path}")
        
        marketing_sms = []
        financial_sms = []
        total_sms = 0
        processed_sms = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row_num, row in enumerate(reader, 1):
                    total_sms += 1
                    sender = row.get('senderAddress', '')
                    message = row.get('text', '')
                    
                    if not message.strip():
                        print(f"DEBUG: Row {row_num}: Skipping empty message")
                        continue
                    
                    print(f"DEBUG: Row {row_num}: Processing SMS from {sender[:20]}...")
                    sms_type = self.classify_sms(sender, message)
                    
                    sms_data = {
                        'sender': sender,
                        'message': message,
                        'phone_number': row.get('phoneNumber', ''),
                        'sms_date': row.get('updateAt', ''),
                        'sms_id': row.get('id', ''),
                        'customer_id': customer_id
                    }
                    
                    if sms_type == "financial":
                        financial_sms.append(sms_data)
                        print(f"DEBUG: Row {row_num}: Added to financial SMS list")
                    else:
                        marketing_sms.append(sms_data)
                        print(f"DEBUG: Row {row_num}: Added to marketing SMS list")
                    
                    processed_sms += 1
                    
                    # Log progress every 100 SMS
                    if processed_sms % 100 == 0:
                        print(f"INFO: Processed {processed_sms} SMS messages...")
            
            # Save marketing SMS to CSV
            marketing_csv_path = self._save_marketing_sms_to_csv(marketing_sms, customer_id)
            
            print(f"INFO: CSV processing completed:")
            print(f"  - Total SMS processed: {processed_sms}")
            print(f"  - Marketing SMS: {len(marketing_sms)}")
            print(f"  - Financial SMS: {len(financial_sms)}")
            print(f"  - Marketing CSV saved to: {marketing_csv_path}")
            
            return {
                'marketing_count': len(marketing_sms),
                'financial_count': len(financial_sms),
                'marketing_csv_path': marketing_csv_path,
                'financial_sms': financial_sms
            }
            
        except Exception as e:
            logger.error(f"Error processing CSV file: {str(e)}")
            raise
    
    def _save_marketing_sms_to_csv(self, marketing_sms: List[Dict], customer_id: str) -> str:
        """Save marketing SMS to CSV file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"marketing_sms_{customer_id}_{timestamp}.csv"
        filepath = f"data/marketing_sms/{filename}"
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as file:
            if marketing_sms:
                writer = csv.DictWriter(file, fieldnames=marketing_sms[0].keys())
                writer.writeheader()
                writer.writerows(marketing_sms)
        
        print(f"INFO: Saved {len(marketing_sms)} marketing SMS to {filepath}")
        return filepath
    
    def chunk_sms_list(self, sms_list: List[Dict]) -> List[List[Dict]]:
        """Split SMS list into chunks of specified size"""
        return [sms_list[i:i + self.chunk_size] for i in range(0, len(sms_list), self.chunk_size)]
    
    async def process_sms_chunk_with_openai(self, sms_chunk: List[Dict]) -> List[Dict]:
        """Process a chunk of SMS messages with OpenAI with retry logic"""
        print(f"INFO: Processing SMS chunk with {len(sms_chunk)} messages using OpenAI")
        
        # Log first few SMS for debugging
        for i, sms in enumerate(sms_chunk[:3]):
            print(f"DEBUG:   SMS {i+1}: {sms['sender'][:20]}... - {sms['message'][:50]}...")
        
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            print(f"INFO: OpenAI API attempt {attempt + 1}/{max_retries}")
            
            try:
                # Prepare SMS data for OpenAI
                sms_texts = []
                for sms in sms_chunk:
                    sms_texts.append(f"Sender: {sms['sender']}\nMessage: {sms['message']}\nDate: {sms['sms_date']}")
                
                combined_text = "\n\n---\n\n".join(sms_texts)
                print(f"DEBUG: Combined SMS text length: {len(combined_text)} characters")
                
                # Create prompt for OpenAI
                prompt = self._create_openai_prompt(combined_text)
                
                # Print the prompt being sent to OpenAI
                print("=" * 80)
                print("OPENAI PROMPT:")
                print("=" * 80)
                print(prompt)
                print("=" * 80)
                
                # Call OpenAI API with timeout
                print("DEBUG: Calling OpenAI API...")
                start_time = datetime.now()
                
                # Print the system prompt
                system_prompt = self._get_system_prompt()
                print("=" * 80)
                print("OPENAI SYSTEM PROMPT:")
                print("=" * 80)
                print(system_prompt)
                print("=" * 80)
                
                response = await asyncio.wait_for(
                    self.openai_client.chat.completions.create(
                        model="gpt-4.1-nano",
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=0.1,
                        max_tokens=4000,
                        response_format={"type": "json_object"}
                    ),
                    timeout=60  # 60 second timeout
                )
                
                api_time = (datetime.now() - start_time).total_seconds()
                print(f"INFO: OpenAI API call completed in {api_time:.2f} seconds")
                
                # Parse response
                response_content = response.choices[0].message.content
                print(f"DEBUG: OpenAI response length: {len(response_content)} characters")
                
                # Print the full OpenAI response
                print("=" * 80)
                print("OPENAI RESPONSE:")
                print("=" * 80)
                print(response_content)
                print("=" * 80)
                
                parsed_data = json.loads(response_content)
                print(f"DEBUG: Parsed JSON keys: {list(parsed_data.keys())}")
                
                # Process and validate the response
                processed_data = self._process_openai_response(parsed_data, sms_chunk)
                
                print(f"INFO: Successfully processed SMS chunk on attempt {attempt + 1}")
                return processed_data
                
            except asyncio.TimeoutError:
                print(f"WARNING: OpenAI API timeout on attempt {attempt + 1}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (attempt + 1))
                continue
                
            except Exception as e:
                error_msg = str(e)
                print(f"WARNING: OpenAI API error on attempt {attempt + 1}: {error_msg}")
                
                # Check if it's a server error (503, 502, etc.)
                if any(code in error_msg for code in ['503', '502', '500', 'server_error']):
                    if attempt < max_retries - 1:
                        wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                        print(f"INFO: Retrying in {wait_time} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                
                # For other errors, don't retry
                print(f"ERROR: Fatal error processing SMS chunk: {error_msg}")
                break
        
        # If all retries failed, use fallback processing
        print(f"WARNING: OpenAI processing failed after {max_retries} attempts, using fallback processing")
        return self._fallback_sms_processing(sms_chunk)
    
    def _get_system_prompt(self) -> str:
        """Get optimized system prompt for OpenAI"""
        return """You are a financial data extraction expert. Extract structured data from SMS messages.

TABLES: transactions, bank_accounts, credit_cards, upi_accounts, wallets, loans, customer_salaries, loan_repayments, credit_card_repayments, service_accounts

KEY FIELDS:
- transactions: source_type, amount, currency(INR), transaction_date, platform, transaction_type, description, reference_id
- bank_accounts: account_number, bank_name, last_updated_balance_amount, status(Active)
- credit_cards: card_number, issuer_name, credit_line, last_updated_balance, status(Active)
- upi_accounts: vba, provider_bank_name, app_name, status(Active)
- wallets: account_id, app_name, current_balance, status(Active)
- loans: loan_id, loan_type, lender_name, loan_amount, status(Active)
- customer_salaries: employer_name, salary_amount, salary_date, currency(INR)
- loan_repayments: repayment_amount, repayment_date, payment_status(on_time), currency(INR)
- credit_card_repayments: repayment_amount, repayment_date, payment_status(on_time), currency(INR)
- service_accounts: account_type, app_name, total_amount_spent, status(Active)

RULES:
- Extract data for applicable tables only
- Use exact field names
- Set amounts as Float, dates as ISO format
- Include sms_id for linking
- Set defaults: INR currency, success/Active status

Return JSON: {"transactions":[], "bank_accounts":[], "credit_cards":[], "upi_accounts":[], "wallets":[], "loans":[], "customer_salaries":[], "loan_repayments":[], "credit_card_repayments":[], "service_accounts":[]}"""
    
    def _create_openai_prompt(self, sms_texts: str) -> str:
        """Create prompt for OpenAI analysis"""
        return f"""Analyze the following SMS messages and extract financial transaction information:

{sms_texts}

Please extract all financial transactions from these SMS messages. Focus on:
- Debit/credit transactions
- UPI payments
- Bank transfers
- Card transactions
- Loan payments
- Investment transactions

Return the data in the specified JSON format."""
    
    def _process_openai_response(self, parsed_data: Dict, original_sms: List[Dict]) -> Dict[str, List[Dict]]:
        """Process and validate OpenAI response for all table types"""
        print("INFO: Processing OpenAI response...")
        
        processed_data = {
            'transactions': [],
            'bank_accounts': [],
            'credit_cards': [],
            'upi_accounts': [],
            'wallets': [],
            'loans': [],
            'customer_salaries': [],
            'loan_repayments': [],
            'credit_card_repayments': [],
            'service_accounts': []
        }
        
        # Process each table type
        for table_name in processed_data.keys():
            if table_name in parsed_data and isinstance(parsed_data[table_name], list):
                print(f"DEBUG: Processing {table_name}: {len(parsed_data[table_name])} records")
                for i, record in enumerate(parsed_data[table_name]):
                    cleaned_record = self._clean_record_data(record, table_name)
                    if cleaned_record:
                        processed_data[table_name].append(cleaned_record)
                        print(f"DEBUG:   {table_name} record {i+1}: Validated and added")
                    else:
                        print(f"DEBUG:   {table_name} record {i+1}: Failed validation, skipped")
            else:
                print(f"DEBUG: No data found for {table_name}")
        
        # Log summary
        total_records = sum(len(records) for records in processed_data.values())
        print(f"INFO: OpenAI response processing completed: {total_records} valid records extracted")
        
        return processed_data
    
    def _clean_record_data(self, record: Dict, table_name: str) -> Dict:
        """Clean and validate data for any table type"""
        try:
            # Basic validation based on table type
            if table_name == 'transactions':
                return self._clean_transaction_data(record)
            elif table_name == 'bank_accounts':
                return self._clean_bank_account_data(record)
            elif table_name == 'credit_cards':
                return self._clean_credit_card_data(record)
            elif table_name == 'upi_accounts':
                return self._clean_upi_account_data(record)
            elif table_name == 'wallets':
                return self._clean_wallet_data(record)
            elif table_name == 'loans':
                return self._clean_loan_data(record)
            elif table_name == 'customer_salaries':
                return self._clean_salary_data(record)
            elif table_name == 'loan_repayments':
                return self._clean_loan_repayment_data(record)
            elif table_name == 'credit_card_repayments':
                return self._clean_credit_card_repayment_data(record)
            elif table_name == 'service_accounts':
                return self._clean_service_account_data(record)
            else:
                print(f"WARNING: Unknown table type: {table_name}")
                return None
                
        except Exception as e:
            print(f"ERROR: Error cleaning {table_name} data: {str(e)}")
            return None
    
    def _clean_transaction_data(self, transaction: Dict) -> Dict:
        """Clean and validate transaction data"""
        try:
            # Basic validation
            required_fields = ['transaction_type', 'amount']
            for field in required_fields:
                if field not in transaction or not transaction[field]:
                    return None
            
            # Clean amount
            if isinstance(transaction['amount'], str):
                # Extract numeric value from string
                amount_match = re.search(r'[\d,]+\.?\d*', transaction['amount'])
                if amount_match:
                    transaction['amount'] = float(amount_match.group().replace(',', ''))
                else:
                    return None
            
            # Set defaults
            transaction.setdefault('currency', 'INR')
            transaction.setdefault('status', 'success')
            transaction.setdefault('txn_status', 'success')
            
            return transaction
            
        except Exception as e:
            print(f"ERROR: Error cleaning transaction data: {str(e)}")
            return None
    
    def _clean_bank_account_data(self, account: Dict) -> Dict:
        """Clean and validate bank account data"""
        try:
            # Basic validation
            if 'account_number' not in account or not account['account_number']:
                return None
            
            # Clean balance amount
            if 'last_updated_balance_amount' in account and isinstance(account['last_updated_balance_amount'], str):
                amount_match = re.search(r'[\d,]+\.?\d*', account['last_updated_balance_amount'])
                if amount_match:
                    account['last_updated_balance_amount'] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            account.setdefault('status', 'Active')
            
            return account
            
        except Exception as e:
            print(f"ERROR: Error cleaning bank account data: {str(e)}")
            return None
    
    def _clean_credit_card_data(self, card: Dict) -> Dict:
        """Clean and validate credit card data"""
        try:
            # Basic validation
            if 'card_number' not in card or not card['card_number']:
                return None
            
            # Clean amounts
            for amount_field in ['credit_line', 'last_updated_balance']:
                if amount_field in card and isinstance(card[amount_field], str):
                    amount_match = re.search(r'[\d,]+\.?\d*', card[amount_field])
                    if amount_match:
                        card[amount_field] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            card.setdefault('status', 'Active')
            
            return card
            
        except Exception as e:
            print(f"ERROR: Error cleaning credit card data: {str(e)}")
            return None
    
    def _clean_upi_account_data(self, upi: Dict) -> Dict:
        """Clean and validate UPI account data"""
        try:
            # Basic validation
            if 'vba' not in upi or not upi['vba']:
                return None
            
            # Set defaults
            upi.setdefault('status', 'Active')
            
            return upi
            
        except Exception as e:
            print(f"ERROR: Error cleaning UPI account data: {str(e)}")
            return None
    
    def _clean_wallet_data(self, wallet: Dict) -> Dict:
        """Clean and validate wallet data"""
        try:
            # Basic validation
            if 'account_id' not in wallet or not wallet['account_id']:
                return None
            
            # Clean balance
            if 'current_balance' in wallet and isinstance(wallet['current_balance'], str):
                amount_match = re.search(r'[\d,]+\.?\d*', wallet['current_balance'])
                if amount_match:
                    wallet['current_balance'] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            wallet.setdefault('status', 'Active')
            
            return wallet
            
        except Exception as e:
            print(f"ERROR: Error cleaning wallet data: {str(e)}")
            return None
    
    def _clean_loan_data(self, loan: Dict) -> Dict:
        """Clean and validate loan data"""
        try:
            # Basic validation
            if 'loan_id' not in loan or not loan['loan_id']:
                return None
            
            # Clean amounts
            for amount_field in ['loan_amount', 'pending_principal', 'pending_interest']:
                if amount_field in loan and isinstance(loan[amount_field], str):
                    amount_match = re.search(r'[\d,]+\.?\d*', loan[amount_field])
                    if amount_match:
                        loan[amount_field] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            loan.setdefault('status', 'Active')
            
            return loan
            
        except Exception as e:
            print(f"ERROR: Error cleaning loan data: {str(e)}")
            return None
    
    def _clean_salary_data(self, salary: Dict) -> Dict:
        """Clean and validate salary data"""
        try:
            # Basic validation
            required_fields = ['salary_amount', 'salary_date']
            for field in required_fields:
                if field not in salary or not salary[field]:
                    return None
            
            # Clean amount
            if isinstance(salary['salary_amount'], str):
                amount_match = re.search(r'[\d,]+\.?\d*', salary['salary_amount'])
                if amount_match:
                    salary['salary_amount'] = float(amount_match.group().replace(',', ''))
                else:
                    return None
            
            # Set defaults
            salary.setdefault('currency', 'INR')
            
            return salary
            
        except Exception as e:
            print(f"ERROR: Error cleaning salary data: {str(e)}")
            return None
    
    def _clean_loan_repayment_data(self, repayment: Dict) -> Dict:
        """Clean and validate loan repayment data"""
        try:
            # Basic validation
            required_fields = ['repayment_amount', 'repayment_date']
            for field in required_fields:
                if field not in repayment or not repayment[field]:
                    return None
            
            # Clean amounts
            for amount_field in ['repayment_amount', 'principal_amount', 'interest_amount', 'penalty_amount', 'outstanding_balance']:
                if amount_field in repayment and isinstance(repayment[amount_field], str):
                    amount_match = re.search(r'[\d,]+\.?\d*', repayment[amount_field])
                    if amount_match:
                        repayment[amount_field] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            repayment.setdefault('currency', 'INR')
            repayment.setdefault('payment_status', 'on_time')
            
            return repayment
            
        except Exception as e:
            print(f"ERROR: Error cleaning loan repayment data: {str(e)}")
            return None
    
    def _clean_credit_card_repayment_data(self, repayment: Dict) -> Dict:
        """Clean and validate credit card repayment data"""
        try:
            # Basic validation
            required_fields = ['repayment_amount', 'repayment_date']
            for field in required_fields:
                if field not in repayment or not repayment[field]:
                    return None
            
            # Clean amounts
            for amount_field in ['repayment_amount', 'minimum_due', 'total_due', 'outstanding_balance', 'late_fee_charged', 'interest_charged', 'cashback_earned']:
                if amount_field in repayment and isinstance(repayment[amount_field], str):
                    amount_match = re.search(r'[\d,]+\.?\d*', repayment[amount_field])
                    if amount_match:
                        repayment[amount_field] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            repayment.setdefault('currency', 'INR')
            repayment.setdefault('payment_status', 'on_time')
            
            return repayment
            
        except Exception as e:
            print(f"ERROR: Error cleaning credit card repayment data: {str(e)}")
            return None
    
    def _clean_service_account_data(self, account: Dict) -> Dict:
        """Clean and validate service account data"""
        try:
            # Basic validation
            if 'account_type' not in account or not account['account_type']:
                return None
            
            # Clean amount
            if 'total_amount_spent' in account and isinstance(account['total_amount_spent'], str):
                amount_match = re.search(r'[\d,]+\.?\d*', account['total_amount_spent'])
                if amount_match:
                    account['total_amount_spent'] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            account.setdefault('status', 'Active')
            account.setdefault('total_amount_spent', 0.0)
            
            return account
            
        except Exception as e:
            print(f"ERROR: Error cleaning service account data: {str(e)}")
            return None
    
    def _fallback_sms_processing(self, sms_chunk: List[Dict]) -> Dict[str, List[Dict]]:
        """Fallback processing when OpenAI fails - basic regex-based extraction"""
        print(f"INFO: Using fallback SMS processing for {len(sms_chunk)} messages")
        
        # Step 1: Create chunks for fallback processing
        print("INFO: Step 1: Creating fallback processing chunks...")
        fallback_chunks = self.chunk_sms_list(sms_chunk)
        print(f"INFO: Created {len(fallback_chunks)} fallback chunks of size {self.chunk_size}")
        
        # Log fallback chunk details
        for i, chunk in enumerate(fallback_chunks):
            print(f"INFO:   Fallback Chunk {i+1}: {len(chunk)} SMS messages")
            for j, sms in enumerate(chunk[:2]):  # Show first 2 SMS per chunk
                print(f"INFO:     SMS {j+1}: {sms.get('sender', 'unknown')[:20]}... - {sms.get('message', '')[:40]}...")
            if len(chunk) > 2:
                print(f"INFO:     ... and {len(chunk) - 2} more SMS")
        
        # Step 2: Execute fallback chunks in series
        print("INFO: Step 2: Executing fallback chunks in series...")
        result = {
            'transactions': [],
            'bank_accounts': [],
            'credit_cards': [],
            'upi_accounts': [],
            'wallets': [],
            'loans': [],
            'customer_salaries': [],
            'loan_repayments': [],
            'credit_card_repayments': [],
            'service_accounts': []
        }
        
        for i, chunk in enumerate(fallback_chunks):
            print(f"INFO: Executing fallback chunk {i+1}/{len(fallback_chunks)} with {len(chunk)} SMS messages")
            
            for j, sms in enumerate(chunk):
                print(f"DEBUG: Fallback processing SMS {j+1}/{len(chunk)}: {sms.get('sender', 'unknown')[:20]}...")
                
                try:
                    # Basic transaction extraction
                    transaction = self._extract_basic_transaction(sms)
                    if transaction:
                        result['transactions'].append(transaction)
                        print(f"DEBUG:   Extracted transaction: {transaction.get('amount', 0)} INR")
                    
                    # Basic account extraction
                    account = self._extract_basic_account(sms)
                    if account:
                        result['bank_accounts'].append(account)
                        print(f"DEBUG:   Extracted bank account: {account.get('account_number', 'unknown')}")
                    
                    # Basic credit card extraction
                    card = self._extract_basic_credit_card(sms)
                    if card:
                        result['credit_cards'].append(card)
                        print(f"DEBUG:   Extracted credit card: {card.get('card_number', 'unknown')}")
                    
                    # Basic wallet extraction
                    wallet = self._extract_basic_wallet(sms)
                    if wallet:
                        result['wallets'].append(wallet)
                        print(f"DEBUG:   Extracted wallet: {wallet.get('app_name', 'unknown')}")
                    
                    # Basic loan extraction
                    loan = self._extract_basic_loan(sms)
                    if loan:
                        result['loans'].append(loan)
                        print(f"DEBUG:   Extracted loan: {loan.get('loan_type', 'unknown')}")
                    
                except Exception as e:
                    print(f"ERROR: Error in fallback processing for SMS {sms.get('sms_id', 'unknown')}: {str(e)}")
            
            print(f"INFO: Successfully executed fallback chunk {i+1}/{len(fallback_chunks)}")
        
        total_records = sum(len(v) for v in result.values())
        print(f"INFO: Fallback processing completed: {total_records} records extracted")
        
        # Log breakdown
        for table_name, records in result.items():
            if records:
                print(f"INFO:   {table_name}: {len(records)} records")
        
        return result
    
    def _extract_basic_transaction(self, sms: Dict) -> Dict:
        """Extract basic transaction information using regex"""
        text = sms['message'].lower()
        sender = sms['sender'].lower()
        
        # Extract amount
        amount_match = re.search(r'rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        if not amount_match:
            return None
        
        amount = float(amount_match.group(1).replace(',', ''))
        
        # Determine transaction type
        transaction_type = 'debit'
        if any(word in text for word in ['credited', 'received', 'added']):
            transaction_type = 'credit'
        elif any(word in text for word in ['payment', 'paid']):
            transaction_type = 'payment'
        
        # Determine platform
        platform = sender.upper()
        if 'upi' in text:
            platform = 'UPI'
        elif any(word in text for word in ['paytm', 'phonepe', 'gpay']):
            platform = text.split()[0].upper()
        
        return {
            'sms_id': sms.get('sms_id'),
            'source_type': 'sms_analysis',
            'source_id': 0,
            'amount': amount,
            'currency': 'INR',
            'transaction_date': sms.get('sms_date', datetime.now().isoformat()),
            'platform': platform,
            'transaction_type': transaction_type,
            'description': sms['message'][:100],
            'status': 'success',
            'txn_status': 'success'
        }
    
    def _extract_basic_account(self, sms: Dict) -> Dict:
        """Extract basic bank account information"""
        text = sms['message'].lower()
        sender = sms['sender'].lower()
        
        # Extract account number
        account_match = re.search(r'a/c\s*([a-z0-9*]+)', text, re.IGNORECASE)
        if not account_match:
            return None
        
        # Extract balance
        balance_match = re.search(r'bal[ance]*\s*:?\s*rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        balance = None
        if balance_match:
            balance = float(balance_match.group(1).replace(',', ''))
        
        return {
            'sms_id': sms.get('sms_id'),
            'account_number': account_match.group(1),
            'bank_name': sender.upper(),
            'last_updated_balance_amount': balance,
            'status': 'Active'
        }
    
    def _extract_basic_credit_card(self, sms: Dict) -> Dict:
        """Extract basic credit card information"""
        text = sms['message'].lower()
        sender = sms['sender'].lower()
        
        # Check if it's a credit card message
        if not any(word in text for word in ['credit card', 'card', 'limit']):
            return None
        
        # Extract card number
        card_match = re.search(r'card\s*([a-z0-9*]+)', text, re.IGNORECASE)
        if not card_match:
            return None
        
        # Extract credit limit
        limit_match = re.search(r'limit\s*:?\s*rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        limit = None
        if limit_match:
            limit = float(limit_match.group(1).replace(',', ''))
        
        return {
            'sms_id': sms.get('sms_id'),
            'card_number': card_match.group(1),
            'issuer_name': sender.upper(),
            'credit_line': limit,
            'status': 'Active'
        }
    
    def _extract_basic_wallet(self, sms: Dict) -> Dict:
        """Extract basic wallet information"""
        text = sms['message'].lower()
        
        # Check if it's a wallet message
        if not any(word in text for word in ['wallet', 'paytm', 'phonepe', 'gpay']):
            return None
        
        # Extract balance
        balance_match = re.search(r'balance\s*:?\s*rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        if not balance_match:
            return None
        
        balance = float(balance_match.group(1).replace(',', ''))
        
        # Determine app name
        app_name = 'Unknown'
        if 'paytm' in text:
            app_name = 'Paytm'
        elif 'phonepe' in text:
            app_name = 'PhonePe'
        elif 'gpay' in text:
            app_name = 'Google Pay'
        
        return {
            'sms_id': sms.get('sms_id'),
            'account_id': f"wallet_{sms.get('sms_id')}",
            'app_name': app_name,
            'current_balance': balance,
            'status': 'Active'
        }
    
    def _extract_basic_loan(self, sms: Dict) -> Dict:
        """Extract basic loan information"""
        text = sms['message'].lower()
        
        # Check if it's a loan message
        if not any(word in text for word in ['loan', 'emi', 'outstanding']):
            return None
        
        # Extract loan amount
        amount_match = re.search(r'rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        if not amount_match:
            return None
        
        amount = float(amount_match.group(1).replace(',', ''))
        
        # Determine loan type
        loan_type = 'Personal'
        if 'home' in text:
            loan_type = 'Home'
        elif 'business' in text:
            loan_type = 'Business'
        elif 'auto' in text:
            loan_type = 'Auto'
        
        return {
            'sms_id': sms.get('sms_id'),
            'loan_id': f"loan_{sms.get('sms_id')}",
            'loan_type': loan_type,
            'lender_name': sms['sender'].upper(),
            'loan_amount': amount,
            'status': 'Active'
        }
    
    async def process_financial_sms_parallel(self, financial_sms: List[Dict]) -> Dict[str, List[Dict]]:
        """Process financial SMS in serial chunks for testing"""
        print(f"INFO: Processing {len(financial_sms)} financial SMS in serial mode for testing")
        
        if not self.use_openai:
            print("INFO: OpenAI processing disabled, using fallback processing")
            return self._fallback_sms_processing(financial_sms)
        
        # Step 1: Create all chunks first
        print("INFO: Step 1: Creating SMS chunks...")
        chunks = self.chunk_sms_list(financial_sms)
        print(f"INFO: Created {len(chunks)} chunks of size {self.chunk_size}")
        
        # Log chunk details
        for i, chunk in enumerate(chunks):
            print(f"INFO:   Chunk {i+1}: {len(chunk)} SMS messages")
            for j, sms in enumerate(chunk[:2]):  # Show first 2 SMS per chunk
                print(f"INFO:     SMS {j+1}: {sms['sender'][:20]}... - {sms['message'][:40]}...")
            if len(chunk) > 2:
                print(f"INFO:     ... and {len(chunk) - 2} more SMS")
        
        # Step 2: Execute chunks in series
        print("INFO: Step 2: Executing chunks in series...")
        results = []
        for i, chunk in enumerate(chunks):
            print(f"INFO: Executing chunk {i+1}/{len(chunks)} with {len(chunk)} SMS messages")
            try:
                result = await self.process_sms_chunk_with_openai(chunk)
                results.append(result)
                print(f"INFO: Successfully executed chunk {i+1}/{len(chunks)}")
            except Exception as e:
                print(f"ERROR: Error executing chunk {i+1}/{len(chunks)}: {str(e)}")
                results.append({
                    'transactions': [],
                    'bank_accounts': [],
                    'credit_cards': [],
                    'upi_accounts': [],
                    'wallets': [],
                    'loans': [],
                    'customer_salaries': [],
                    'loan_repayments': [],
                    'credit_card_repayments': [],
                    'service_accounts': []
                })
        
        # Combine results from all table types
        combined_data = {
            'transactions': [],
            'bank_accounts': [],
            'credit_cards': [],
            'upi_accounts': [],
            'wallets': [],
            'loans': [],
            'customer_salaries': [],
            'loan_repayments': [],
            'credit_card_repayments': [],
            'service_accounts': []
        }
        
        for result in results:
            if isinstance(result, dict):
                for table_name, records in result.items():
                    if table_name in combined_data and isinstance(records, list):
                        combined_data[table_name].extend(records)
            else:
                print(f"ERROR: Error in chunk processing: {result}")
        
        # Log summary
        total_records = sum(len(records) for records in combined_data.values())
        print(f"INFO: Successfully processed {total_records} records across all table types")
        
        return combined_data
    
    async def save_data_to_db(self, processed_data: Dict[str, List[Dict]], customer_id: str, db: Session):
        """Save processed data to database for all table types"""
        print(f"INFO: Saving processed data to database for customer {customer_id}")
        
        # Log data summary before saving
        total_records = sum(len(records) for records in processed_data.values())
        print(f"INFO: Total records to save: {total_records}")
        
        for table_name, records in processed_data.items():
            if records:
                print(f"INFO:   {table_name}: {len(records)} records")
        
        try:
            total_saved = 0
            
            # Save transactions
            if processed_data.get('transactions'):
                for transaction_data in processed_data['transactions']:
                    transaction = CustomerTransaction(
                        customer_id=customer_id,
                        source_type=transaction_data.get('source_type', 'sms_analysis'),
                        source_id=transaction_data.get('source_id', 0),
                        from_account_id=transaction_data.get('from_account_id'),
                        to_account_id=transaction_data.get('to_account_id'),
                        amount=transaction_data.get('amount', 0.0),
                        currency=transaction_data.get('currency', 'INR'),
                        transaction_date=datetime.fromisoformat(transaction_data.get('transaction_date', datetime.now().isoformat())),
                        platform=transaction_data.get('platform'),
                        txn_status=transaction_data.get('txn_status', 'success'),
                        information_type=transaction_data.get('information_type'),
                        transaction_type=transaction_data.get('transaction_type'),
                        transaction_sub_type=transaction_data.get('transaction_sub_type'),
                        description=transaction_data.get('description'),
                        status=transaction_data.get('status', 'success'),
                        reference_id=transaction_data.get('reference_id'),
                        additional_details=transaction_data.get('additional_details'),
                        sms_id=transaction_data.get('sms_id')
                    )
                    db.add(transaction)
                    total_saved += 1
            
            # Save bank accounts
            if processed_data.get('bank_accounts'):
                for account_data in processed_data['bank_accounts']:
                    from ..models.customer_models import BankAccount
                    account = BankAccount(
                        customer_id=customer_id,
                        account_number=account_data.get('account_number'),
                        ifsc_code=account_data.get('ifsc_code'),
                        account_sub_type=account_data.get('account_sub_type'),
                        bank_name=account_data.get('bank_name'),
                        account_opening_date=datetime.fromisoformat(account_data.get('account_opening_date', datetime.now().isoformat())) if account_data.get('account_opening_date') else None,
                        account_closing_date=datetime.fromisoformat(account_data.get('account_closing_date', datetime.now().isoformat())) if account_data.get('account_closing_date') else None,
                        savings_interest_rate=account_data.get('savings_interest_rate'),
                        status=account_data.get('status', 'Active'),
                        last_updated_balance_amount=account_data.get('last_updated_balance_amount'),
                        linked_vbas=account_data.get('linked_vbas'),
                        additional_details=account_data.get('additional_details'),
                        sms_ids=[account_data.get('sms_id')] if account_data.get('sms_id') else None
                    )
                    db.add(account)
                    total_saved += 1
            
            # Save credit cards
            if processed_data.get('credit_cards'):
                for card_data in processed_data['credit_cards']:
                    from ..models.customer_models import CreditCard
                    card = CreditCard(
                        customer_id=customer_id,
                        card_number=card_data.get('card_number'),
                        card_type=card_data.get('card_type'),
                        issuer_name=card_data.get('issuer_name'),
                        network=card_data.get('network'),
                        issuance_date=datetime.fromisoformat(card_data.get('issuance_date', datetime.now().isoformat())) if card_data.get('issuance_date') else None,
                        deactivation_date=datetime.fromisoformat(card_data.get('deactivation_date', datetime.now().isoformat())) if card_data.get('deactivation_date') else None,
                        credit_card_apr=card_data.get('credit_card_apr'),
                        status=card_data.get('status', 'Active'),
                        default_status=card_data.get('default_status'),
                        credit_line=card_data.get('credit_line'),
                        last_updated_balance=card_data.get('last_updated_balance'),
                        additional_details=card_data.get('additional_details'),
                        sms_ids=[card_data.get('sms_id')] if card_data.get('sms_id') else None
                    )
                    db.add(card)
                    total_saved += 1
            
            # Save UPI accounts
            if processed_data.get('upi_accounts'):
                for upi_data in processed_data['upi_accounts']:
                    from ..models.customer_models import UPIAccount
                    upi = UPIAccount(
                        customer_id=customer_id,
                        vba=upi_data.get('vba'),
                        provider_bank_name=upi_data.get('provider_bank_name'),
                        app_name=upi_data.get('app_name'),
                        vba_creation_date=datetime.fromisoformat(upi_data.get('vba_creation_date', datetime.now().isoformat())).date() if upi_data.get('vba_creation_date') else None,
                        vba_closing_date=datetime.fromisoformat(upi_data.get('vba_closing_date', datetime.now().isoformat())).date() if upi_data.get('vba_closing_date') else None,
                        status=upi_data.get('status', 'Active'),
                        additional_details=upi_data.get('additional_details'),
                        sms_ids=[upi_data.get('sms_id')] if upi_data.get('sms_id') else None
                    )
                    db.add(upi)
                    total_saved += 1
            
            # Save wallets
            if processed_data.get('wallets'):
                for wallet_data in processed_data['wallets']:
                    from ..models.customer_models import Wallet
                    wallet = Wallet(
                        customer_id=customer_id,
                        account_id=wallet_data.get('account_id'),
                        wallet_type=wallet_data.get('wallet_type'),
                        app_name=wallet_data.get('app_name'),
                        account_opening_date=datetime.fromisoformat(wallet_data.get('account_opening_date', datetime.now().isoformat())) if wallet_data.get('account_opening_date') else None,
                        account_closing_date=datetime.fromisoformat(wallet_data.get('account_closing_date', datetime.now().isoformat())) if wallet_data.get('account_closing_date') else None,
                        status=wallet_data.get('status', 'Active'),
                        current_balance=wallet_data.get('current_balance'),
                        currency_breakdown=wallet_data.get('currency_breakdown'),
                        additional_details=wallet_data.get('additional_details'),
                        sms_ids=[wallet_data.get('sms_id')] if wallet_data.get('sms_id') else None
                    )
                    db.add(wallet)
                    total_saved += 1
            
            # Save loans
            if processed_data.get('loans'):
                for loan_data in processed_data['loans']:
                    from ..models.customer_models import Loan
                    loan = Loan(
                        customer_id=customer_id,
                        loan_id=loan_data.get('loan_id'),
                        loan_type=loan_data.get('loan_type'),
                        lender_name=loan_data.get('lender_name'),
                        issuance_date=datetime.fromisoformat(loan_data.get('issuance_date', datetime.now().isoformat())) if loan_data.get('issuance_date') else None,
                        closure_date=datetime.fromisoformat(loan_data.get('closure_date', datetime.now().isoformat())) if loan_data.get('closure_date') else None,
                        loan_interest_rate=loan_data.get('loan_interest_rate'),
                        status=loan_data.get('status', 'Active'),
                        default_status=loan_data.get('default_status'),
                        loan_tenure=loan_data.get('loan_tenure'),
                        loan_amount=loan_data.get('loan_amount'),
                        pending_principal=loan_data.get('pending_principal'),
                        pending_interest=loan_data.get('pending_interest'),
                        additional_details=loan_data.get('additional_details'),
                        sms_ids=[loan_data.get('sms_id')] if loan_data.get('sms_id') else None
                    )
                    db.add(loan)
                    total_saved += 1
            
            # Save salaries
            if processed_data.get('customer_salaries'):
                for salary_data in processed_data['customer_salaries']:
                    from ..models.customer_models import CustomerSalary
                    salary = CustomerSalary(
                        customer_id=customer_id,
                        bank_account_id=salary_data.get('bank_account_id', 0),  # Default to 0 if not specified
                        employer_name=salary_data.get('employer_name'),
                        salary_amount=salary_data.get('salary_amount', 0.0),
                        salary_date=datetime.fromisoformat(salary_data.get('salary_date', datetime.now().isoformat())),
                        salary_month=salary_data.get('salary_month'),
                        salary_year=salary_data.get('salary_year'),
                        salary_type=salary_data.get('salary_type'),
                        reference_id=salary_data.get('reference_id'),
                        description=salary_data.get('description'),
                        currency=salary_data.get('currency', 'INR'),
                        additional_details=salary_data.get('additional_details'),
                        sms_id=salary_data.get('sms_id')
                    )
                    db.add(salary)
                    total_saved += 1
            
            # Save loan repayments
            if processed_data.get('loan_repayments'):
                for repayment_data in processed_data['loan_repayments']:
                    from ..models.customer_models import LoanRepayment
                    repayment = LoanRepayment(
                        customer_id=customer_id,
                        loan_id=repayment_data.get('loan_id', 0),  # Default to 0 if not specified
                        bank_account_id=repayment_data.get('bank_account_id'),
                        repayment_amount=repayment_data.get('repayment_amount', 0.0),
                        principal_amount=repayment_data.get('principal_amount'),
                        interest_amount=repayment_data.get('interest_amount'),
                        penalty_amount=repayment_data.get('penalty_amount'),
                        repayment_date=datetime.fromisoformat(repayment_data.get('repayment_date', datetime.now().isoformat())),
                        due_date=datetime.fromisoformat(repayment_data.get('due_date', datetime.now().isoformat())) if repayment_data.get('due_date') else None,
                        emi_number=repayment_data.get('emi_number'),
                        repayment_type=repayment_data.get('repayment_type'),
                        payment_status=repayment_data.get('payment_status', 'on_time'),
                        days_delayed=repayment_data.get('days_delayed', 0),
                        outstanding_balance=repayment_data.get('outstanding_balance'),
                        reference_id=repayment_data.get('reference_id'),
                        description=repayment_data.get('description'),
                        currency=repayment_data.get('currency', 'INR'),
                        additional_details=repayment_data.get('additional_details'),
                        sms_id=repayment_data.get('sms_id')
                    )
                    db.add(repayment)
                    total_saved += 1
            
            # Save credit card repayments
            if processed_data.get('credit_card_repayments'):
                for repayment_data in processed_data['credit_card_repayments']:
                    from ..models.customer_models import CreditCardRepayment
                    repayment = CreditCardRepayment(
                        customer_id=customer_id,
                        credit_card_id=repayment_data.get('credit_card_id', 0),  # Default to 0 if not specified
                        bank_account_id=repayment_data.get('bank_account_id'),
                        repayment_amount=repayment_data.get('repayment_amount', 0.0),
                        minimum_due=repayment_data.get('minimum_due'),
                        total_due=repayment_data.get('total_due'),
                        repayment_date=datetime.fromisoformat(repayment_data.get('repayment_date', datetime.now().isoformat())),
                        due_date=datetime.fromisoformat(repayment_data.get('due_date', datetime.now().isoformat())) if repayment_data.get('due_date') else None,
                        billing_cycle_start=datetime.fromisoformat(repayment_data.get('billing_cycle_start', datetime.now().isoformat())).date() if repayment_data.get('billing_cycle_start') else None,
                        billing_cycle_end=datetime.fromisoformat(repayment_data.get('billing_cycle_end', datetime.now().isoformat())).date() if repayment_data.get('billing_cycle_end') else None,
                        repayment_type=repayment_data.get('repayment_type'),
                        payment_status=repayment_data.get('payment_status', 'on_time'),
                        days_delayed=repayment_data.get('days_delayed', 0),
                        outstanding_balance=repayment_data.get('outstanding_balance'),
                        late_fee_charged=repayment_data.get('late_fee_charged'),
                        interest_charged=repayment_data.get('interest_charged'),
                        cashback_earned=repayment_data.get('cashback_earned'),
                        reward_points_earned=repayment_data.get('reward_points_earned'),
                        reference_id=repayment_data.get('reference_id'),
                        description=repayment_data.get('description'),
                        currency=repayment_data.get('currency', 'INR'),
                        additional_details=repayment_data.get('additional_details'),
                        sms_id=repayment_data.get('sms_id')
                    )
                    db.add(repayment)
                    total_saved += 1
            
            # Save service accounts
            if processed_data.get('service_accounts'):
                for account_data in processed_data['service_accounts']:
                    from ..models.customer_models import ServiceAccount
                    account = ServiceAccount(
                        customer_id=customer_id,
                        account_type=account_data.get('account_type'),
                        app_name=account_data.get('app_name'),
                        account_id=account_data.get('account_id'),
                        account_opening_date=datetime.fromisoformat(account_data.get('account_opening_date', datetime.now().isoformat())) if account_data.get('account_opening_date') else None,
                        status=account_data.get('status', 'Active'),
                        total_amount_spent=account_data.get('total_amount_spent', 0.0),
                        last_transaction_date=datetime.fromisoformat(account_data.get('last_transaction_date', datetime.now().isoformat())) if account_data.get('last_transaction_date') else None,
                        additional_details=account_data.get('additional_details'),
                        sms_ids=[account_data.get('sms_id')] if account_data.get('sms_id') else None
                    )
                    db.add(account)
                    total_saved += 1
            
            db.commit()
            print(f"INFO: Successfully saved {total_saved} records to database across all table types")
            
            # Log final breakdown
            print("INFO: Database save breakdown:")
            for table_name, records in processed_data.items():
                if records:
                    print(f"INFO:   {table_name}: {len(records)} records saved")
            
        except Exception as e:
            db.rollback()
            print(f"ERROR: Error saving data to database: {str(e)}")
            raise
    
    async def process_sms_file(self, file_path: str, customer_id: str) -> Dict[str, Any]:
        """Main method to process SMS file end-to-end"""
        start_time = datetime.now()
        print(f"INFO: Starting SMS processing for customer {customer_id}")
        print(f"INFO: File path: {file_path}")
        print(f"INFO: OpenAI processing enabled: {self.use_openai}")
        
        try:
            # Step 1: Process CSV and classify SMS
            print("INFO: Step 1: Processing CSV and classifying SMS...")
            classification_result = self.process_csv_file(file_path, customer_id)
            
            # Step 2: Process financial SMS with OpenAI
            if classification_result['financial_sms']:
                print("INFO: Step 2: Processing financial SMS...")
                processed_data = await self.process_financial_sms_parallel(classification_result['financial_sms'])
                
                # Step 3: Save to database
                print("INFO: Step 3: Saving data to database...")
                db = next(get_db())
                try:
                    await self.save_data_to_db(processed_data, customer_id, db)
                finally:
                    db.close()
                
                # Count total processed records
                total_records = sum(len(records) for records in processed_data.values())
                classification_result['processed_transactions'] = total_records
                classification_result['processed_data_summary'] = {
                    table_name: len(records) for table_name, records in processed_data.items() if records
                }
                
                print(f"INFO: Step 3 completed: {total_records} records processed and saved")
            else:
                print("INFO: No financial SMS found, skipping processing")
                classification_result['processed_transactions'] = 0
                classification_result['processed_data_summary'] = {}
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            classification_result['processing_time_seconds'] = processing_time
            
            print(f"INFO: SMS processing completed in {processing_time:.2f} seconds")
            print("INFO: Final summary:")
            print(f"INFO:   - Marketing SMS: {classification_result['marketing_count']}")
            print(f"INFO:   - Financial SMS: {classification_result['financial_count']}")
            print(f"INFO:   - Processed records: {classification_result['processed_transactions']}")
            print(f"INFO:   - Processing time: {processing_time:.2f} seconds")
            
            return classification_result
            
        except Exception as e:
            print(f"ERROR: Error in SMS processing: {str(e)}")
            raise 