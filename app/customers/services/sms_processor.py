import asyncio
import csv
import json
import os
import re
from datetime import datetime
from typing import List, Dict, Any, Tuple
from uuid import uuid4
import pandas as pd
from sqlalchemy.orm import Session
from openai import AsyncOpenAI
from ...database import get_db
from ..models.customer_models import SMSList, Customer, CustomerTransaction, CustomerEvent
from .sms_constants import FINANCIAL_KEYWORDS, CURRENCY_KEYWORDS, BANK_NAMES, FINTECH_NAMES, NBFC_NAMES

class SMSProcessor:
    """Handles SMS processing with classification and OpenAI analysis"""
    
    def __init__(self, use_openai: bool = True):
        self.use_openai = use_openai
        if self.use_openai:
            self.openai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.chunk_size = 5
        
    def classify_sms(self, sender: str, message: str) -> str:
        """
        Classify SMS into two types:
        1. Marketing/Spam/OTP messages
        2. Financial messages (debit, credit, UPI, etc.)
        """
        print(f"DEBUG: Classifying SMS - Sender: {sender[:20]}..., Message: {message[:50]}...")
        
        # Check for OTP/marketing patterns
        if self._is_otp_or_marketing(sender, message):
            print(f"DEBUG: SMS classified as MARKETING - Sender: {sender[:20]}...")
            return "marketing"
        
        # Check for financial patterns
        if self._is_financial_message(sender, message):
            print(f"DEBUG: SMS classified as FINANCIAL - Sender: {sender[:20]}...")
            return "financial"
        
        # Default to marketing if unclear
        print(f"DEBUG: SMS classified as MARKETING (default) - Sender: {sender[:20]}...")
        return "marketing"
    
    def _is_otp_or_marketing(self, sender: str, message: str) -> bool:
        """Check if SMS is OTP or marketing related"""
        text = message.lower()
        
        # OTP patterns
        otp_patterns = [
            r'\botp\b', r'\bpassword\b', r'\bverification code\b', r'\bverify\b',
            r'\bconfirm\b', r'\bactivate\b', r'\bone time password\b', r'\bsecurity code\b',
            r'\bauthentication code\b', r'\blogin code\b', r'\baccess code\b',
            r'\bpin\b.*\bcode\b', r'\bcode\b.*\bverification\b', r'\bverification\b.*\bcode\b'
        ]
        
        # Marketing patterns
        marketing_patterns = [
            r'\boffer\b', r'\bdiscount\b', r'\bsale\b', r'\bpromo\b', r'\bdeal\b',
            r'\bwin\b', r'\bprize\b', r'\bcontest\b', r'\bwinner\b', r'\bclaim\b',
            r'\bunlimited\b', r'\bfree\b', r'\bcashback\b', r'\breward\b',
            r'\bnewsletter\b', r'\bupdate\b', r'\bnotification\b', r'\balert\b'
        ]
        
        # Check patterns
        for pattern in otp_patterns + marketing_patterns:
            if re.search(pattern, text):
                return True
        
        # Check for mobile number sender
        if self._is_mobile_number(sender):
            return True
            
        return False
    
    def _is_financial_message(self, sender: str, message: str) -> bool:
        """Check if SMS contains financial information"""
        text = message.lower()
        
        # Financial keywords
        financial_patterns = FINANCIAL_KEYWORDS + CURRENCY_KEYWORDS + BANK_NAMES + FINTECH_NAMES + NBFC_NAMES
        
        # Transaction patterns
        transaction_patterns = [
            r'\bdebit\b', r'\bcredit\b', r'\bupi\b', r'\btransfer\b', r'\bpayment\b',
            r'\bwithdrawal\b', r'\bdeposit\b', r'\btransaction\b', r'\baccount\b',
            r'\bbalance\b', r'\bamount\b', r'\brs\.?\s*\d+', r'\b₹\s*\d+',
            r'\bpaid\b', r'\breceived\b', r'\bsent\b', r'\bcharged\b', r'\bdeducted\b'
        ]
        
        # Check patterns
        for pattern in financial_patterns + transaction_patterns:
            if re.search(pattern, text):
                return True
        
        return False
    
    def _is_mobile_number(self, sender: str) -> bool:
        """Check if sender is a mobile number"""
        cleaned = re.sub(r'^[+]?', '', sender)
        cleaned = re.sub(r'[^\d]', '', cleaned)
        
        patterns = [
            r'^[6-9]\d{9}$',           # 10-digit Indian mobile
            r'^91[6-9]\d{9}$',         # Indian mobile with 91
            r'^\d{10,15}$'             # Any 10-15 digit number
        ]
        
        return any(re.match(pattern, cleaned) for pattern in patterns)
    
    def process_csv_file(self, file_path: str, customer_id: str) -> Dict[str, Any]:
        """Process CSV file and classify SMS messages"""
        print(f"INFO: Processing CSV file: {file_path}")
        
        marketing_sms = []
        financial_sms = []
        total_sms = 0
        processed_sms = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row_num, row in enumerate(reader, 1):
                    total_sms += 1
                    sender = row.get('senderAddress', '')
                    message = row.get('text', '')
                    
                    if not message.strip():
                        print(f"DEBUG: Row {row_num}: Skipping empty message")
                        continue
                    
                    print(f"DEBUG: Row {row_num}: Processing SMS from {sender[:20]}...")
                    sms_type = self.classify_sms(sender, message)
                    
                    sms_data = {
                        'sender': sender,
                        'message': message,
                        'phone_number': row.get('phoneNumber', ''),
                        'sms_date': row.get('updateAt', ''),
                        'sms_id': row.get('id', ''),
                        'customer_id': customer_id
                    }
                    
                    if sms_type == "financial":
                        financial_sms.append(sms_data)
                        print(f"DEBUG: Row {row_num}: Added to financial SMS list")
                    else:
                        marketing_sms.append(sms_data)
                        print(f"DEBUG: Row {row_num}: Added to marketing SMS list")
                    
                    processed_sms += 1
                    
                    # Log progress every 100 SMS
                    if processed_sms % 100 == 0:
                        print(f"INFO: Processed {processed_sms} SMS messages...")
            
            # Save marketing SMS to CSV
            marketing_csv_path = self._save_marketing_sms_to_csv(marketing_sms, customer_id)
            
            print(f"INFO: CSV processing completed:")
            print(f"  - Total SMS processed: {processed_sms}")
            print(f"  - Marketing SMS: {len(marketing_sms)}")
            print(f"  - Financial SMS: {len(financial_sms)}")
            print(f"  - Marketing CSV saved to: {marketing_csv_path}")
            
            return {
                'marketing_count': len(marketing_sms),
                'financial_count': len(financial_sms),
                'marketing_csv_path': marketing_csv_path,
                'financial_sms': financial_sms
            }
            
        except Exception as e:
            logger.error(f"Error processing CSV file: {str(e)}")
            raise
    
    def _save_marketing_sms_to_csv(self, marketing_sms: List[Dict], customer_id: str) -> str:
        """Save marketing SMS to CSV file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"marketing_sms_{customer_id}_{timestamp}.csv"
        filepath = f"data/marketing_sms/{filename}"
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as file:
            if marketing_sms:
                writer = csv.DictWriter(file, fieldnames=marketing_sms[0].keys())
                writer.writeheader()
                writer.writerows(marketing_sms)
        
        print(f"INFO: Saved {len(marketing_sms)} marketing SMS to {filepath}")
        return filepath
    
    def chunk_sms_list(self, sms_list: List[Dict]) -> List[List[Dict]]:
        """Split SMS list into chunks of specified size"""
        return [sms_list[i:i + self.chunk_size] for i in range(0, len(sms_list), self.chunk_size)]
    
    async def process_sms_chunk_with_openai(self, sms_chunk: List[Dict]) -> List[Dict]:
        """Process a chunk of SMS messages with OpenAI with retry logic"""
        print(f"INFO: Processing SMS chunk with {len(sms_chunk)} messages using OpenAI")
        
        # Log first few SMS for debugging
        for i, sms in enumerate(sms_chunk[:3]):
            print(f"DEBUG:   SMS {i+1}: {sms['sender'][:20]}... - {sms['message'][:50]}...")
        
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            print(f"INFO: OpenAI API attempt {attempt + 1}/{max_retries}")
            
            try:
                # Prepare SMS data for OpenAI
                sms_texts = []
                for sms in sms_chunk:
                    sms_texts.append(f"Sender: {sms['sender']}\nMessage: {sms['message']}\nDate: {sms['sms_date']}")
                
                combined_text = "\n\n---\n\n".join(sms_texts)
                print(f"DEBUG: Combined SMS text length: {len(combined_text)} characters")
                
                # Create prompt for OpenAI
                prompt = self._create_openai_prompt(combined_text)
                
                # Print the prompt being sent to OpenAI
                print("=" * 80)
                print("OPENAI PROMPT:")
                print("=" * 80)
                print(prompt)
                print("=" * 80)
                
                # Call OpenAI API with timeout
                print("DEBUG: Calling OpenAI API...")
                start_time = datetime.now()
                
                # Print the system prompt
                system_prompt = self._get_system_prompt()
                print("=" * 80)
                print("OPENAI SYSTEM PROMPT:")
                print("=" * 80)
                print(system_prompt)
                print("=" * 80)
                
                response = await asyncio.wait_for(
                    self.openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",  # Faster and cheaper model
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=0,  # Deterministic output
                        max_tokens=1000,  # Reduced tokens for faster response
                        response_format={"type": "json_object"}
                    ),
                    timeout=30  # Reduced timeout for faster processing
                )
                
                api_time = (datetime.now() - start_time).total_seconds()
                print(f"INFO: OpenAI API call completed in {api_time:.2f} seconds")
                
                # Parse response
                response_content = response.choices[0].message.content
                print(f"DEBUG: OpenAI response length: {len(response_content)} characters")
                
                # Print the full OpenAI response
                print("=" * 80)
                print("OPENAI RESPONSE:")
                print("=" * 80)
                print(response_content)
                print("=" * 80)
                
                parsed_data = json.loads(response_content)
                print(f"DEBUG: Parsed JSON keys: {list(parsed_data.keys())}")
                
                # Process and validate the response
                processed_data = self._process_openai_response(parsed_data, sms_chunk)
                
                print(f"INFO: Successfully processed SMS chunk on attempt {attempt + 1}")
                return processed_data
                
            except asyncio.TimeoutError:
                print(f"WARNING: OpenAI API timeout on attempt {attempt + 1}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (attempt + 1))
                continue
                
            except Exception as e:
                error_msg = str(e)
                print(f"WARNING: OpenAI API error on attempt {attempt + 1}: {error_msg}")
                
                # Check if it's a server error (503, 502, etc.)
                if any(code in error_msg for code in ['503', '502', '500', 'server_error']):
                    if attempt < max_retries - 1:
                        wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                        print(f"INFO: Retrying in {wait_time} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                
                # For other errors, don't retry
                print(f"ERROR: Fatal error processing SMS chunk: {error_msg}")
                break
        
        # If all retries failed, use fallback processing
        print(f"WARNING: OpenAI processing failed after {max_retries} attempts, using fallback processing")
        return self._fallback_sms_processing(sms_chunk)
    
    def _get_system_prompt(self) -> str:
        """Get simplified system prompt for faster OpenAI processing"""
        return """Extract financial data from SMS messages. Return simple JSON with raw extracted values.

Structure:
{
  "sms_id": "string",
  "type": "financial|marketing",
  "amount": float|null,
  "account": "string|null",
  "sender": "string",
  "transaction_type": "debit|credit|payment|null",
  "platform": "string|null",
  "balance": float|null,
  "date": "YYYY-MM-DD HH:MM:SS|null",
  "description": "string"
}

Rules:
- Extract amounts as numbers only
- Use sender name as-is
- Keep account numbers/IDs as found
- Use transaction date from SMS or current date
- Mark as "financial" if contains money/account info, else "marketing"
- Return null for missing values"""
    
    def _create_openai_prompt(self, sms_texts: str) -> str:
        """Create simple prompt for fast OpenAI processing"""
        return f"""Extract data from these SMS messages:

{sms_texts}

Return JSON array with one object per SMS."""
    
    def _process_openai_response(self, parsed_data: Dict, original_sms: List[Dict]) -> Dict[str, List[Dict]]:
        """Process simplified OpenAI response and intelligently split into database tables"""
        print("INFO: Processing simplified OpenAI response...")

        # Initialize result structure for all database tables
        processed_data = {
            'transactions': [],
            'bank_accounts': [],
            'credit_cards': [],
            'upi_accounts': [],
            'wallets': [],
            'loans': [],
            'customer_salaries': [],
            'loan_repayments': [],
            'credit_card_repayments': [],
            'service_accounts': [],
            'customer_events': [],
            'brokerage_accounts': [],
            'fixed_deposits': []
        }

        # Handle array response (multiple SMS) or single object
        sms_data_list = parsed_data if isinstance(parsed_data, list) else [parsed_data]

        for sms_data in sms_data_list:
            if not isinstance(sms_data, dict):
                continue

            print(f"DEBUG: Processing SMS {sms_data.get('sms_id', 'unknown')}")

            # Skip non-financial messages for detailed processing
            if sms_data.get('type') != 'financial':
                print(f"DEBUG: Skipping non-financial SMS {sms_data.get('sms_id')}")
                continue

            # Use intelligent splitting logic based on extracted data
            self._intelligent_data_splitting(sms_data, processed_data, original_sms)

        # Log summary
        total_records = sum(len(records) for records in processed_data.values())
        print(f"INFO: Response processing completed: {total_records} valid records extracted")

        # Log breakdown
        for table_name, records in processed_data.items():
            if records:
                print(f"DEBUG:   {table_name}: {len(records)} records")

        return processed_data

    def _intelligent_data_splitting(self, sms_data: Dict, processed_data: Dict, original_sms: List[Dict]):
        """Intelligently split simple JSON into appropriate database tables"""
        try:
            sms_id = sms_data.get('sms_id')
            sender = sms_data.get('sender', '').upper()
            description = sms_data.get('description', '').lower()
            amount = sms_data.get('amount')
            account = sms_data.get('account')
            transaction_type = sms_data.get('transaction_type')
            platform = sms_data.get('platform')
            balance = sms_data.get('balance')
            date_str = sms_data.get('date')

            # Parse date
            transaction_date = self._parse_date(date_str) if date_str else datetime.now()

            # 1. Create transaction record if amount exists
            if amount and transaction_type:
                transaction_record = {
                    'sms_id': sms_id,
                    'source_type': 'sms_analysis',
                    'source_id': 0,
                    'amount': float(amount),
                    'currency': 'INR',
                    'transaction_date': transaction_date.isoformat(),
                    'platform': platform or sender,
                    'transaction_type': transaction_type,
                    'transaction_sub_type': self._determine_transaction_subtype(description, platform),
                    'description': description[:200] if description else None,
                    'status': 'success',
                    'txn_status': 'success',
                    'from_account_id': account if transaction_type == 'debit' else None,
                    'to_account_id': account if transaction_type == 'credit' else None,
                    'reference_id': self._extract_reference_id(description)
                }
                processed_data['transactions'].append(transaction_record)

            # 2. Create account records based on sender and account info
            if account:
                account_record = self._create_account_record(sender, account, balance, sms_id, description)
                if account_record:
                    table_name = account_record.pop('_table_type')
                    processed_data[table_name].append(account_record)

            # 3. Create specialized records based on content analysis
            self._create_specialized_records(sms_data, processed_data, description, sender, amount, transaction_date)

        except Exception as e:
            print(f"ERROR: Error in intelligent data splitting: {str(e)}")

    def _parse_date(self, date_str: str) -> datetime:
        """Parse date string to datetime object"""
        try:
            # Try common formats
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%dT%H:%M:%S',
                '%d-%b-%y %H:%M',
                '%d-%m-%Y %H:%M:%S'
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue

            # If no format matches, return current time
            return datetime.now()
        except:
            return datetime.now()

    def _determine_transaction_subtype(self, description: str, platform: str) -> str:
        """Determine transaction subtype from description and platform"""
        desc_lower = description.lower() if description else ''
        platform_lower = platform.lower() if platform else ''

        # UPI transactions
        if any(word in desc_lower for word in ['upi', 'vpa', '@']):
            return 'upi'

        # Card transactions
        if any(word in desc_lower for word in ['card', 'pos', 'atm']):
            return 'card'

        # Bank transfers
        if any(word in desc_lower for word in ['neft', 'rtgs', 'imps']):
            return 'neft'

        # EMI/Loan payments
        if any(word in desc_lower for word in ['emi', 'loan', 'repayment']):
            return 'emi'

        # Salary
        if any(word in desc_lower for word in ['salary', 'sal', 'wages']):
            return 'salary'

        # Bill payments
        if any(word in desc_lower for word in ['bill', 'utility', 'recharge']):
            return 'bill_payment'

        # Investment
        if any(word in desc_lower for word in ['invest', 'sip', 'mutual', 'stock', 'equity']):
            return 'investment'

        # Platform-based detection
        if platform_lower in ['paytm', 'gpay', 'phonepe', 'amazon pay']:
            return 'wallet'

        return 'other'

    def _extract_reference_id(self, description: str) -> str:
        """Extract reference ID from description"""
        if not description:
            return None

        import re
        # Look for common reference ID patterns
        patterns = [
            r'ref[erence]*\s*[:#]?\s*([a-z0-9]+)',
            r'txn[id]*\s*[:#]?\s*([a-z0-9]+)',
            r'transaction\s*id\s*[:#]?\s*([a-z0-9]+)',
            r'utr\s*[:#]?\s*([a-z0-9]+)',
            r'id\s*[:#]?\s*([a-z0-9]{6,})'
        ]

        for pattern in patterns:
            match = re.search(pattern, description.lower())
            if match:
                return match.group(1).upper()

        return None

    def _create_account_record(self, sender: str, account: str, balance: float, sms_id: str, description: str) -> Dict:
        """Create appropriate account record based on sender and context"""
        sender_lower = sender.lower()
        desc_lower = description.lower() if description else ''

        # Credit Card
        if any(word in sender_lower for word in ['card', 'visa', 'master', 'amex']) or 'card' in desc_lower:
            return {
                '_table_type': 'credit_cards',
                'sms_id': sms_id,
                'card_number': account,
                'issuer_name': sender,
                'last_updated_balance': balance,
                'status': 'Active'
            }

        # UPI Account
        if '@' in account or any(word in desc_lower for word in ['upi', 'vpa']):
            return {
                '_table_type': 'upi_accounts',
                'sms_id': sms_id,
                'vba': account,
                'provider_bank_name': sender,
                'app_name': self._extract_app_name(sender, desc_lower),
                'status': 'Active'
            }

        # Wallet
        if any(word in sender_lower for word in ['paytm', 'phonepe', 'gpay', 'mobikwik', 'freecharge']):
            return {
                '_table_type': 'wallets',
                'sms_id': sms_id,
                'account_id': account or f"wallet_{sms_id}",
                'app_name': sender,
                'current_balance': balance,
                'status': 'Active'
            }

        # Loan
        if any(word in sender_lower for word in ['loan', 'finance', 'bajaj', 'tata', 'mahindra']) or any(word in desc_lower for word in ['loan', 'emi']):
            return {
                '_table_type': 'loans',
                'sms_id': sms_id,
                'loan_id': account,
                'lender_name': sender,
                'loan_amount': balance,
                'status': 'Active'
            }

        # Service Account (e-commerce, food delivery, etc.)
        if any(word in sender_lower for word in ['amazon', 'flipkart', 'swiggy', 'zomato', 'uber', 'ola']):
            return {
                '_table_type': 'service_accounts',
                'sms_id': sms_id,
                'account_type': sender,
                'app_name': sender,
                'account_id': account,
                'total_amount_spent': 0.0,
                'status': 'Active'
            }

        # Default to Bank Account
        return {
            '_table_type': 'bank_accounts',
            'sms_id': sms_id,
            'account_number': account,
            'bank_name': sender,
            'last_updated_balance_amount': balance,
            'status': 'Active'
        }

    def _extract_app_name(self, sender: str, description: str) -> str:
        """Extract app name from sender or description"""
        apps = ['paytm', 'phonepe', 'gpay', 'amazon pay', 'mobikwik', 'freecharge']

        for app in apps:
            if app in sender.lower() or app in description:
                return app.title()

        return sender

    def _create_specialized_records(self, sms_data: Dict, processed_data: Dict, description: str, sender: str, amount: float, transaction_date: datetime):
        """Create specialized records based on content analysis"""
        try:
            sms_id = sms_data.get('sms_id')
            desc_lower = description.lower() if description else ''
            sender_lower = sender.lower()

            # Salary Detection
            if any(word in desc_lower for word in ['salary', 'sal', 'wages', 'pay']) and amount:
                salary_record = {
                    'sms_id': sms_id,
                    'employer_name': sender,
                    'salary_amount': float(amount),
                    'salary_date': transaction_date.isoformat(),
                    'salary_month': transaction_date.month,
                    'salary_year': transaction_date.year,
                    'salary_type': 'monthly',
                    'currency': 'INR',
                    'description': description[:200] if description else None
                }
                processed_data['customer_salaries'].append(salary_record)

            # Loan Repayment Detection
            elif any(word in desc_lower for word in ['emi', 'loan repayment', 'loan payment']) and amount:
                repayment_record = {
                    'sms_id': sms_id,
                    'repayment_amount': float(amount),
                    'repayment_date': transaction_date.isoformat(),
                    'payment_status': 'on_time',
                    'days_delayed': 0,
                    'currency': 'INR',
                    'description': description[:200] if description else None
                }
                processed_data['loan_repayments'].append(repayment_record)

            # Credit Card Repayment Detection
            elif any(word in desc_lower for word in ['card payment', 'cc payment', 'credit card']) and 'payment' in desc_lower and amount:
                cc_repayment_record = {
                    'sms_id': sms_id,
                    'repayment_amount': float(amount),
                    'repayment_date': transaction_date.isoformat(),
                    'payment_status': 'on_time',
                    'days_delayed': 0,
                    'currency': 'INR',
                    'description': description[:200] if description else None
                }
                processed_data['credit_card_repayments'].append(cc_repayment_record)

            # Investment Detection
            elif any(word in desc_lower for word in ['invest', 'sip', 'mutual fund', 'stock', 'equity', 'share']) and amount:
                brokerage_record = {
                    'sms_id': sms_id,
                    'account_id': f"investment_{sms_id}",
                    'app_name': sender,
                    'status': 'Active',
                    'stocks_amount': float(amount) if 'stock' in desc_lower or 'equity' in desc_lower else None,
                    'mutual_funds_amount': float(amount) if 'mutual' in desc_lower or 'sip' in desc_lower else None
                }
                processed_data['brokerage_accounts'].append(brokerage_record)

            # Fixed Deposit Detection
            elif any(word in desc_lower for word in ['fd', 'fixed deposit', 'deposit matured', 'maturity']) and amount:
                fd_record = {
                    'sms_id': sms_id,
                    'deposit_id': f"fd_{sms_id}",
                    'bank_name': sender,
                    'status': 'Matured' if 'matured' in desc_lower else 'Active',
                    'current_fd_amount': float(amount),
                    'initial_fd_amount': float(amount)
                }
                processed_data['fixed_deposits'].append(fd_record)

            # Risk Event Detection
            risk_flags = self._detect_risk_flags(description, amount, sender)
            if any(risk_flags.values()):
                event_record = {
                    'sms_id': sms_id,
                    'entity_type': 'risk_analysis',
                    'entity_id': 0,
                    'source': 'sms_analysis',
                    'event_type': 'risk_detected',
                    'event_source': 'sms',
                    'event_datetime': transaction_date.isoformat(),
                    'description': f"Risk flags detected: {[k for k, v in risk_flags.items() if v]}",
                    'status': 'detected',
                    'additional_details': risk_flags
                }
                processed_data['customer_events'].append(event_record)

        except Exception as e:
            print(f"ERROR: Error creating specialized records: {str(e)}")

    def _detect_risk_flags(self, description: str, amount: float, sender: str) -> Dict[str, bool]:
        """Detect risk flags from SMS content"""
        if not description:
            return {}

        desc_lower = description.lower()
        sender_lower = sender.lower()

        return {
            'high_value_transaction': amount and amount > 100000,
            'gambling_related': any(word in desc_lower for word in ['bet', 'casino', 'lottery', 'gambling', 'rummy']),
            'crypto_related': any(word in desc_lower for word in ['bitcoin', 'crypto', 'binance', 'coinbase', 'blockchain']),
            'loan_default_warning': any(word in desc_lower for word in ['overdue', 'default', 'penalty', 'late fee', 'bounce']),
            'failed_transaction': any(word in desc_lower for word in ['failed', 'declined', 'insufficient', 'blocked']),
            'suspicious_merchant': any(word in sender_lower for word in ['unknown', 'temp', 'test'])
        }

    def _process_account_entity(self, account: Dict, processed_data: Dict, sms_id: str, classification: Dict):
        """Process account entity from unified structure into appropriate table"""
        try:
            account_type = account.get('type', '').lower()

            if account_type == 'bank':
                bank_account = {
                    'sms_id': sms_id,
                    'account_number': account.get('identifier'),
                    'bank_name': account.get('provider'),
                    'last_updated_balance_amount': account.get('balance'),
                    'status': account.get('status', 'Active'),
                    'additional_details': account.get('additional_info', {})
                }
                cleaned = self._clean_bank_account_data(bank_account)
                if cleaned:
                    processed_data['bank_accounts'].append(cleaned)

            elif account_type == 'credit_card':
                credit_card = {
                    'sms_id': sms_id,
                    'card_number': account.get('identifier'),
                    'issuer_name': account.get('provider'),
                    'last_updated_balance': account.get('balance'),
                    'status': account.get('status', 'Active'),
                    'additional_details': account.get('additional_info', {})
                }
                cleaned = self._clean_credit_card_data(credit_card)
                if cleaned:
                    processed_data['credit_cards'].append(cleaned)

            elif account_type == 'upi':
                upi_account = {
                    'sms_id': sms_id,
                    'vba': account.get('identifier'),
                    'provider_bank_name': account.get('provider'),
                    'app_name': account.get('provider'),
                    'status': account.get('status', 'Active'),
                    'additional_details': account.get('additional_info', {})
                }
                cleaned = self._clean_upi_account_data(upi_account)
                if cleaned:
                    processed_data['upi_accounts'].append(cleaned)

            elif account_type == 'wallet':
                wallet = {
                    'sms_id': sms_id,
                    'account_id': account.get('identifier'),
                    'app_name': account.get('provider'),
                    'current_balance': account.get('balance'),
                    'status': account.get('status', 'Active'),
                    'additional_details': account.get('additional_info', {})
                }
                cleaned = self._clean_wallet_data(wallet)
                if cleaned:
                    processed_data['wallets'].append(cleaned)

            elif account_type == 'loan':
                loan = {
                    'sms_id': sms_id,
                    'loan_id': account.get('identifier'),
                    'lender_name': account.get('provider'),
                    'loan_amount': account.get('balance'),
                    'status': account.get('status', 'Active'),
                    'additional_details': account.get('additional_info', {})
                }
                cleaned = self._clean_loan_data(loan)
                if cleaned:
                    processed_data['loans'].append(cleaned)

            elif account_type == 'service':
                service_account = {
                    'sms_id': sms_id,
                    'account_type': account.get('provider'),
                    'app_name': account.get('provider'),
                    'account_id': account.get('identifier'),
                    'total_amount_spent': account.get('balance', 0.0),
                    'status': account.get('status', 'Active'),
                    'additional_details': account.get('additional_info', {})
                }
                cleaned = self._clean_service_account_data(service_account)
                if cleaned:
                    processed_data['service_accounts'].append(cleaned)

        except Exception as e:
            print(f"ERROR: Error processing account entity: {str(e)}")

    def _process_transaction_entity(self, transaction: Dict, processed_data: Dict, sms_id: str, classification: Dict):
        """Process transaction entity from unified structure"""
        try:
            transaction_data = {
                'sms_id': sms_id,
                'source_type': 'sms_analysis',
                'source_id': 0,
                'amount': transaction.get('amount', 0.0),
                'currency': transaction.get('currency', 'INR'),
                'transaction_date': transaction.get('date'),
                'platform': transaction.get('platform'),
                'transaction_type': transaction.get('type'),
                'transaction_sub_type': transaction.get('sub_type'),
                'from_account_id': transaction.get('from_account'),
                'to_account_id': transaction.get('to_account'),
                'reference_id': transaction.get('reference_id'),
                'description': transaction.get('description'),
                'status': transaction.get('status', 'success'),
                'txn_status': transaction.get('status', 'success')
            }

            cleaned = self._clean_transaction_data(transaction_data)
            if cleaned:
                processed_data['transactions'].append(cleaned)

        except Exception as e:
            print(f"ERROR: Error processing transaction entity: {str(e)}")

    def _process_payment_entity(self, payment: Dict, processed_data: Dict, sms_id: str, classification: Dict):
        """Process payment entity from unified structure"""
        try:
            payment_type = payment.get('payment_type', '').lower()

            if 'loan_repayment' in payment_type:
                loan_repayment = {
                    'sms_id': sms_id,
                    'repayment_amount': payment.get('amount', 0.0),
                    'repayment_date': payment.get('payment_date'),
                    'due_date': payment.get('due_date'),
                    'payment_status': payment.get('payment_status', 'on_time'),
                    'days_delayed': payment.get('days_delayed', 0),
                    'currency': 'INR',
                    'additional_details': payment.get('additional_details', {})
                }
                cleaned = self._clean_loan_repayment_data(loan_repayment)
                if cleaned:
                    processed_data['loan_repayments'].append(cleaned)

            elif 'credit_card_repayment' in payment_type:
                cc_repayment = {
                    'sms_id': sms_id,
                    'repayment_amount': payment.get('amount', 0.0),
                    'total_due': payment.get('due_amount'),
                    'repayment_date': payment.get('payment_date'),
                    'due_date': payment.get('due_date'),
                    'payment_status': payment.get('payment_status', 'on_time'),
                    'days_delayed': payment.get('days_delayed', 0),
                    'currency': 'INR',
                    'additional_details': payment.get('additional_details', {})
                }
                cleaned = self._clean_credit_card_repayment_data(cc_repayment)
                if cleaned:
                    processed_data['credit_card_repayments'].append(cleaned)

            elif 'salary' in payment_type:
                salary = {
                    'sms_id': sms_id,
                    'salary_amount': payment.get('amount', 0.0),
                    'salary_date': payment.get('payment_date'),
                    'employer_name': payment.get('linked_account'),
                    'currency': 'INR',
                    'additional_details': payment.get('additional_details', {})
                }
                cleaned = self._clean_salary_data(salary)
                if cleaned:
                    processed_data['customer_salaries'].append(cleaned)

        except Exception as e:
            print(f"ERROR: Error processing payment entity: {str(e)}")

    def _process_behavioral_indicators(self, behavioral_indicators: Dict, processed_data: Dict, sms_id: str, classification: Dict):
        """Process behavioral indicators into customer events"""
        try:
            risk_flags = behavioral_indicators.get('risk_flags', {})
            patterns = behavioral_indicators.get('patterns', {})

            # Create events for significant risk flags
            for flag_name, flag_value in risk_flags.items():
                if flag_value:
                    event = {
                        'sms_id': sms_id,
                        'entity_type': 'behavioral_analysis',
                        'entity_id': 0,
                        'source': 'sms_analysis',
                        'event_type': 'risk_flag',
                        'event_sub_type': flag_name,
                        'event_source': 'sms',
                        'event_datetime': datetime.now().isoformat(),
                        'description': f"Risk flag detected: {flag_name}",
                        'status': 'detected',
                        'additional_details': {
                            'risk_flag': flag_name,
                            'classification': classification,
                            'patterns': patterns
                        }
                    }
                    processed_data['customer_events'].append(event)

        except Exception as e:
            print(f"ERROR: Error processing behavioral indicators: {str(e)}")

    def _clean_record_data(self, record: Dict, table_name: str) -> Dict:
        """Clean and validate data for any table type"""
        try:
            # Basic validation based on table type
            if table_name == 'transactions':
                return self._clean_transaction_data(record)
            elif table_name == 'bank_accounts':
                return self._clean_bank_account_data(record)
            elif table_name == 'credit_cards':
                return self._clean_credit_card_data(record)
            elif table_name == 'upi_accounts':
                return self._clean_upi_account_data(record)
            elif table_name == 'wallets':
                return self._clean_wallet_data(record)
            elif table_name == 'loans':
                return self._clean_loan_data(record)
            elif table_name == 'customer_salaries':
                return self._clean_salary_data(record)
            elif table_name == 'loan_repayments':
                return self._clean_loan_repayment_data(record)
            elif table_name == 'credit_card_repayments':
                return self._clean_credit_card_repayment_data(record)
            elif table_name == 'service_accounts':
                return self._clean_service_account_data(record)
            else:
                print(f"WARNING: Unknown table type: {table_name}")
                return None
                
        except Exception as e:
            print(f"ERROR: Error cleaning {table_name} data: {str(e)}")
            return None
    
    def _clean_transaction_data(self, transaction: Dict) -> Dict:
        """Clean and validate transaction data"""
        try:
            # Basic validation
            required_fields = ['transaction_type', 'amount']
            for field in required_fields:
                if field not in transaction or not transaction[field]:
                    return None
            
            # Clean amount
            if isinstance(transaction['amount'], str):
                # Extract numeric value from string
                amount_match = re.search(r'[\d,]+\.?\d*', transaction['amount'])
                if amount_match:
                    transaction['amount'] = float(amount_match.group().replace(',', ''))
                else:
                    return None
            
            # Set defaults
            transaction.setdefault('currency', 'INR')
            transaction.setdefault('status', 'success')
            transaction.setdefault('txn_status', 'success')
            
            return transaction
            
        except Exception as e:
            print(f"ERROR: Error cleaning transaction data: {str(e)}")
            return None
    
    def _clean_bank_account_data(self, account: Dict) -> Dict:
        """Clean and validate bank account data"""
        try:
            # Basic validation
            if 'account_number' not in account or not account['account_number']:
                return None
            
            # Clean balance amount
            if 'last_updated_balance_amount' in account and isinstance(account['last_updated_balance_amount'], str):
                amount_match = re.search(r'[\d,]+\.?\d*', account['last_updated_balance_amount'])
                if amount_match:
                    account['last_updated_balance_amount'] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            account.setdefault('status', 'Active')
            
            return account
            
        except Exception as e:
            print(f"ERROR: Error cleaning bank account data: {str(e)}")
            return None
    
    def _clean_credit_card_data(self, card: Dict) -> Dict:
        """Clean and validate credit card data"""
        try:
            # Basic validation
            if 'card_number' not in card or not card['card_number']:
                return None
            
            # Clean amounts
            for amount_field in ['credit_line', 'last_updated_balance']:
                if amount_field in card and isinstance(card[amount_field], str):
                    amount_match = re.search(r'[\d,]+\.?\d*', card[amount_field])
                    if amount_match:
                        card[amount_field] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            card.setdefault('status', 'Active')
            
            return card
            
        except Exception as e:
            print(f"ERROR: Error cleaning credit card data: {str(e)}")
            return None
    
    def _clean_upi_account_data(self, upi: Dict) -> Dict:
        """Clean and validate UPI account data"""
        try:
            # Basic validation
            if 'vba' not in upi or not upi['vba']:
                return None
            
            # Set defaults
            upi.setdefault('status', 'Active')
            
            return upi
            
        except Exception as e:
            print(f"ERROR: Error cleaning UPI account data: {str(e)}")
            return None
    
    def _clean_wallet_data(self, wallet: Dict) -> Dict:
        """Clean and validate wallet data"""
        try:
            # Basic validation
            if 'account_id' not in wallet or not wallet['account_id']:
                return None
            
            # Clean balance
            if 'current_balance' in wallet and isinstance(wallet['current_balance'], str):
                amount_match = re.search(r'[\d,]+\.?\d*', wallet['current_balance'])
                if amount_match:
                    wallet['current_balance'] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            wallet.setdefault('status', 'Active')
            
            return wallet
            
        except Exception as e:
            print(f"ERROR: Error cleaning wallet data: {str(e)}")
            return None
    
    def _clean_loan_data(self, loan: Dict) -> Dict:
        """Clean and validate loan data"""
        try:
            # Basic validation
            if 'loan_id' not in loan or not loan['loan_id']:
                return None
            
            # Clean amounts
            for amount_field in ['loan_amount', 'pending_principal', 'pending_interest']:
                if amount_field in loan and isinstance(loan[amount_field], str):
                    amount_match = re.search(r'[\d,]+\.?\d*', loan[amount_field])
                    if amount_match:
                        loan[amount_field] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            loan.setdefault('status', 'Active')
            
            return loan
            
        except Exception as e:
            print(f"ERROR: Error cleaning loan data: {str(e)}")
            return None
    
    def _clean_salary_data(self, salary: Dict) -> Dict:
        """Clean and validate salary data"""
        try:
            # Basic validation
            required_fields = ['salary_amount', 'salary_date']
            for field in required_fields:
                if field not in salary or not salary[field]:
                    return None
            
            # Clean amount
            if isinstance(salary['salary_amount'], str):
                amount_match = re.search(r'[\d,]+\.?\d*', salary['salary_amount'])
                if amount_match:
                    salary['salary_amount'] = float(amount_match.group().replace(',', ''))
                else:
                    return None
            
            # Set defaults
            salary.setdefault('currency', 'INR')
            
            return salary
            
        except Exception as e:
            print(f"ERROR: Error cleaning salary data: {str(e)}")
            return None
    
    def _clean_loan_repayment_data(self, repayment: Dict) -> Dict:
        """Clean and validate loan repayment data"""
        try:
            # Basic validation
            required_fields = ['repayment_amount', 'repayment_date']
            for field in required_fields:
                if field not in repayment or not repayment[field]:
                    return None
            
            # Clean amounts
            for amount_field in ['repayment_amount', 'principal_amount', 'interest_amount', 'penalty_amount', 'outstanding_balance']:
                if amount_field in repayment and isinstance(repayment[amount_field], str):
                    amount_match = re.search(r'[\d,]+\.?\d*', repayment[amount_field])
                    if amount_match:
                        repayment[amount_field] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            repayment.setdefault('currency', 'INR')
            repayment.setdefault('payment_status', 'on_time')
            
            return repayment
            
        except Exception as e:
            print(f"ERROR: Error cleaning loan repayment data: {str(e)}")
            return None
    
    def _clean_credit_card_repayment_data(self, repayment: Dict) -> Dict:
        """Clean and validate credit card repayment data"""
        try:
            # Basic validation
            required_fields = ['repayment_amount', 'repayment_date']
            for field in required_fields:
                if field not in repayment or not repayment[field]:
                    return None
            
            # Clean amounts
            for amount_field in ['repayment_amount', 'minimum_due', 'total_due', 'outstanding_balance', 'late_fee_charged', 'interest_charged', 'cashback_earned']:
                if amount_field in repayment and isinstance(repayment[amount_field], str):
                    amount_match = re.search(r'[\d,]+\.?\d*', repayment[amount_field])
                    if amount_match:
                        repayment[amount_field] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            repayment.setdefault('currency', 'INR')
            repayment.setdefault('payment_status', 'on_time')
            
            return repayment
            
        except Exception as e:
            print(f"ERROR: Error cleaning credit card repayment data: {str(e)}")
            return None
    
    def _clean_service_account_data(self, account: Dict) -> Dict:
        """Clean and validate service account data"""
        try:
            # Basic validation
            if 'account_type' not in account or not account['account_type']:
                return None
            
            # Clean amount
            if 'total_amount_spent' in account and isinstance(account['total_amount_spent'], str):
                amount_match = re.search(r'[\d,]+\.?\d*', account['total_amount_spent'])
                if amount_match:
                    account['total_amount_spent'] = float(amount_match.group().replace(',', ''))
            
            # Set defaults
            account.setdefault('status', 'Active')
            account.setdefault('total_amount_spent', 0.0)
            
            return account
            
        except Exception as e:
            print(f"ERROR: Error cleaning service account data: {str(e)}")
            return None
    
    def _fallback_sms_processing(self, sms_chunk: List[Dict]) -> Dict[str, List[Dict]]:
        """Fallback processing when OpenAI fails - basic regex-based extraction"""
        print(f"INFO: Using fallback SMS processing for {len(sms_chunk)} messages")
        
        # Step 1: Create chunks for fallback processing
        print("INFO: Step 1: Creating fallback processing chunks...")
        fallback_chunks = self.chunk_sms_list(sms_chunk)
        print(f"INFO: Created {len(fallback_chunks)} fallback chunks of size {self.chunk_size}")
        
        # Log fallback chunk details
        for i, chunk in enumerate(fallback_chunks):
            print(f"INFO:   Fallback Chunk {i+1}: {len(chunk)} SMS messages")
            for j, sms in enumerate(chunk[:2]):  # Show first 2 SMS per chunk
                print(f"INFO:     SMS {j+1}: {sms.get('sender', 'unknown')[:20]}... - {sms.get('message', '')[:40]}...")
            if len(chunk) > 2:
                print(f"INFO:     ... and {len(chunk) - 2} more SMS")
        
        # Step 2: Execute fallback chunks in series
        print("INFO: Step 2: Executing fallback chunks in series...")
        result = {
            'transactions': [],
            'bank_accounts': [],
            'credit_cards': [],
            'upi_accounts': [],
            'wallets': [],
            'loans': [],
            'customer_salaries': [],
            'loan_repayments': [],
            'credit_card_repayments': [],
            'service_accounts': [],
            'customer_events': [],
            'brokerage_accounts': [],
            'fixed_deposits': []
        }
        
        for i, chunk in enumerate(fallback_chunks):
            print(f"INFO: Executing fallback chunk {i+1}/{len(fallback_chunks)} with {len(chunk)} SMS messages")
            
            for j, sms in enumerate(chunk):
                print(f"DEBUG: Fallback processing SMS {j+1}/{len(chunk)}: {sms.get('sender', 'unknown')[:20]}...")
                
                try:
                    # Basic transaction extraction
                    transaction = self._extract_basic_transaction(sms)
                    if transaction:
                        result['transactions'].append(transaction)
                        print(f"DEBUG:   Extracted transaction: {transaction.get('amount', 0)} INR")
                    
                    # Basic account extraction
                    account = self._extract_basic_account(sms)
                    if account:
                        result['bank_accounts'].append(account)
                        print(f"DEBUG:   Extracted bank account: {account.get('account_number', 'unknown')}")
                    
                    # Basic credit card extraction
                    card = self._extract_basic_credit_card(sms)
                    if card:
                        result['credit_cards'].append(card)
                        print(f"DEBUG:   Extracted credit card: {card.get('card_number', 'unknown')}")
                    
                    # Basic wallet extraction
                    wallet = self._extract_basic_wallet(sms)
                    if wallet:
                        result['wallets'].append(wallet)
                        print(f"DEBUG:   Extracted wallet: {wallet.get('app_name', 'unknown')}")
                    
                    # Basic loan extraction
                    loan = self._extract_basic_loan(sms)
                    if loan:
                        result['loans'].append(loan)
                        print(f"DEBUG:   Extracted loan: {loan.get('loan_type', 'unknown')}")
                    
                except Exception as e:
                    print(f"ERROR: Error in fallback processing for SMS {sms.get('sms_id', 'unknown')}: {str(e)}")
            
            print(f"INFO: Successfully executed fallback chunk {i+1}/{len(fallback_chunks)}")
        
        total_records = sum(len(v) for v in result.values())
        print(f"INFO: Fallback processing completed: {total_records} records extracted")
        
        # Log breakdown
        for table_name, records in result.items():
            if records:
                print(f"INFO:   {table_name}: {len(records)} records")
        
        return result
    
    def _extract_basic_transaction(self, sms: Dict) -> Dict:
        """Extract basic transaction information using regex"""
        text = sms['message'].lower()
        sender = sms['sender'].lower()
        
        # Extract amount
        amount_match = re.search(r'rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        if not amount_match:
            return None
        
        amount = float(amount_match.group(1).replace(',', ''))
        
        # Determine transaction type
        transaction_type = 'debit'
        if any(word in text for word in ['credited', 'received', 'added']):
            transaction_type = 'credit'
        elif any(word in text for word in ['payment', 'paid']):
            transaction_type = 'payment'
        
        # Determine platform
        platform = sender.upper()
        if 'upi' in text:
            platform = 'UPI'
        elif any(word in text for word in ['paytm', 'phonepe', 'gpay']):
            platform = text.split()[0].upper()
        
        return {
            'sms_id': sms.get('sms_id'),
            'source_type': 'sms_analysis',
            'source_id': 0,
            'amount': amount,
            'currency': 'INR',
            'transaction_date': sms.get('sms_date', datetime.now().isoformat()),
            'platform': platform,
            'transaction_type': transaction_type,
            'description': sms['message'][:100],
            'status': 'success',
            'txn_status': 'success'
        }
    
    def _extract_basic_account(self, sms: Dict) -> Dict:
        """Extract basic bank account information"""
        text = sms['message'].lower()
        sender = sms['sender'].lower()
        
        # Extract account number
        account_match = re.search(r'a/c\s*([a-z0-9*]+)', text, re.IGNORECASE)
        if not account_match:
            return None
        
        # Extract balance
        balance_match = re.search(r'bal[ance]*\s*:?\s*rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        balance = None
        if balance_match:
            balance = float(balance_match.group(1).replace(',', ''))
        
        return {
            'sms_id': sms.get('sms_id'),
            'account_number': account_match.group(1),
            'bank_name': sender.upper(),
            'last_updated_balance_amount': balance,
            'status': 'Active'
        }
    
    def _extract_basic_credit_card(self, sms: Dict) -> Dict:
        """Extract basic credit card information"""
        text = sms['message'].lower()
        sender = sms['sender'].lower()
        
        # Check if it's a credit card message
        if not any(word in text for word in ['credit card', 'card', 'limit']):
            return None
        
        # Extract card number
        card_match = re.search(r'card\s*([a-z0-9*]+)', text, re.IGNORECASE)
        if not card_match:
            return None
        
        # Extract credit limit
        limit_match = re.search(r'limit\s*:?\s*rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        limit = None
        if limit_match:
            limit = float(limit_match.group(1).replace(',', ''))
        
        return {
            'sms_id': sms.get('sms_id'),
            'card_number': card_match.group(1),
            'issuer_name': sender.upper(),
            'credit_line': limit,
            'status': 'Active'
        }
    
    def _extract_basic_wallet(self, sms: Dict) -> Dict:
        """Extract basic wallet information"""
        text = sms['message'].lower()
        
        # Check if it's a wallet message
        if not any(word in text for word in ['wallet', 'paytm', 'phonepe', 'gpay']):
            return None
        
        # Extract balance
        balance_match = re.search(r'balance\s*:?\s*rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        if not balance_match:
            return None
        
        balance = float(balance_match.group(1).replace(',', ''))
        
        # Determine app name
        app_name = 'Unknown'
        if 'paytm' in text:
            app_name = 'Paytm'
        elif 'phonepe' in text:
            app_name = 'PhonePe'
        elif 'gpay' in text:
            app_name = 'Google Pay'
        
        return {
            'sms_id': sms.get('sms_id'),
            'account_id': f"wallet_{sms.get('sms_id')}",
            'app_name': app_name,
            'current_balance': balance,
            'status': 'Active'
        }
    
    def _extract_basic_loan(self, sms: Dict) -> Dict:
        """Extract basic loan information"""
        text = sms['message'].lower()
        
        # Check if it's a loan message
        if not any(word in text for word in ['loan', 'emi', 'outstanding']):
            return None
        
        # Extract loan amount
        amount_match = re.search(r'rs\.?\s*([\d,]+\.?\d*)', text, re.IGNORECASE)
        if not amount_match:
            return None
        
        amount = float(amount_match.group(1).replace(',', ''))
        
        # Determine loan type
        loan_type = 'Personal'
        if 'home' in text:
            loan_type = 'Home'
        elif 'business' in text:
            loan_type = 'Business'
        elif 'auto' in text:
            loan_type = 'Auto'
        
        return {
            'sms_id': sms.get('sms_id'),
            'loan_id': f"loan_{sms.get('sms_id')}",
            'loan_type': loan_type,
            'lender_name': sms['sender'].upper(),
            'loan_amount': amount,
            'status': 'Active'
        }
    
    async def process_financial_sms_parallel(self, financial_sms: List[Dict]) -> Dict[str, List[Dict]]:
        """Process financial SMS in serial chunks for testing"""
        print(f"INFO: Processing {len(financial_sms)} financial SMS in serial mode for testing")
        
        if not self.use_openai:
            print("INFO: OpenAI processing disabled, using fallback processing")
            return self._fallback_sms_processing(financial_sms)
        
        # Step 1: Create all chunks first
        print("INFO: Step 1: Creating SMS chunks...")
        chunks = self.chunk_sms_list(financial_sms)
        print(f"INFO: Created {len(chunks)} chunks of size {self.chunk_size}")
        
        # Log chunk details
        for i, chunk in enumerate(chunks):
            print(f"INFO:   Chunk {i+1}: {len(chunk)} SMS messages")
            for j, sms in enumerate(chunk[:2]):  # Show first 2 SMS per chunk
                print(f"INFO:     SMS {j+1}: {sms['sender'][:20]}... - {sms['message'][:40]}...")
            if len(chunk) > 2:
                print(f"INFO:     ... and {len(chunk) - 2} more SMS")
        
        # Step 2: Execute chunks in series
        print("INFO: Step 2: Executing chunks in series...")
        results = []
        for i, chunk in enumerate(chunks):
            print(f"INFO: Executing chunk {i+1}/{len(chunks)} with {len(chunk)} SMS messages")
            try:
                result = await self.process_sms_chunk_with_openai(chunk)
                results.append(result)
                print(f"INFO: Successfully executed chunk {i+1}/{len(chunks)}")
            except Exception as e:
                print(f"ERROR: Error executing chunk {i+1}/{len(chunks)}: {str(e)}")
                results.append({
                    'transactions': [],
                    'bank_accounts': [],
                    'credit_cards': [],
                    'upi_accounts': [],
                    'wallets': [],
                    'loans': [],
                    'customer_salaries': [],
                    'loan_repayments': [],
                    'credit_card_repayments': [],
                    'service_accounts': [],
                    'customer_events': [],
                    'brokerage_accounts': [],
                    'fixed_deposits': []
                })

        # Combine results from all table types
        combined_data = {
            'transactions': [],
            'bank_accounts': [],
            'credit_cards': [],
            'upi_accounts': [],
            'wallets': [],
            'loans': [],
            'customer_salaries': [],
            'loan_repayments': [],
            'credit_card_repayments': [],
            'service_accounts': [],
            'customer_events': [],
            'brokerage_accounts': [],
            'fixed_deposits': []
        }
        
        for result in results:
            if isinstance(result, dict):
                for table_name, records in result.items():
                    if table_name in combined_data and isinstance(records, list):
                        combined_data[table_name].extend(records)
            else:
                print(f"ERROR: Error in chunk processing: {result}")
        
        # Log summary
        total_records = sum(len(records) for records in combined_data.values())
        print(f"INFO: Successfully processed {total_records} records across all table types")
        
        return combined_data
    
    async def save_data_to_db(self, processed_data: Dict[str, List[Dict]], customer_id: str, db: Session):
        """Save processed data to database for all table types"""
        print(f"INFO: Saving processed data to database for customer {customer_id}")

        # Prepare data in database-ready format
        db_ready_data = self._prepare_db_ready_data(processed_data, customer_id)

        # Log data summary before saving
        total_records = sum(len(records) for records in db_ready_data.values())
        print(f"INFO: Total records to save: {total_records}")

        for table_name, records in db_ready_data.items():
            if records:
                print(f"INFO:   {table_name}: {len(records)} records")

        try:
            total_saved = 0
            
            # Save transactions
            if db_ready_data.get('transactions'):
                for transaction_data in db_ready_data['transactions']:
                    transaction = CustomerTransaction(**transaction_data)
                    db.add(transaction)
                    total_saved += 1
            
            # Save bank accounts
            if db_ready_data.get('bank_accounts'):
                from ..models.customer_models import BankAccount
                for account_data in db_ready_data['bank_accounts']:
                    account = BankAccount(**account_data)
                    db.add(account)
                    total_saved += 1
            
            # Save credit cards
            if processed_data.get('credit_cards'):
                for card_data in processed_data['credit_cards']:
                    from ..models.customer_models import CreditCard
                    card = CreditCard(
                        customer_id=customer_id,
                        card_number=card_data.get('card_number'),
                        card_type=card_data.get('card_type'),
                        issuer_name=card_data.get('issuer_name'),
                        network=card_data.get('network'),
                        issuance_date=datetime.fromisoformat(card_data.get('issuance_date', datetime.now().isoformat())) if card_data.get('issuance_date') else None,
                        deactivation_date=datetime.fromisoformat(card_data.get('deactivation_date', datetime.now().isoformat())) if card_data.get('deactivation_date') else None,
                        credit_card_apr=card_data.get('credit_card_apr'),
                        status=card_data.get('status', 'Active'),
                        default_status=card_data.get('default_status'),
                        credit_line=card_data.get('credit_line'),
                        last_updated_balance=card_data.get('last_updated_balance'),
                        additional_details=card_data.get('additional_details'),
                        sms_ids=[card_data.get('sms_id')] if card_data.get('sms_id') else None
                    )
                    db.add(card)
                    total_saved += 1
            
            # Save UPI accounts
            if processed_data.get('upi_accounts'):
                for upi_data in processed_data['upi_accounts']:
                    from ..models.customer_models import UPIAccount
                    upi = UPIAccount(
                        customer_id=customer_id,
                        vba=upi_data.get('vba'),
                        provider_bank_name=upi_data.get('provider_bank_name'),
                        app_name=upi_data.get('app_name'),
                        vba_creation_date=datetime.fromisoformat(upi_data.get('vba_creation_date', datetime.now().isoformat())).date() if upi_data.get('vba_creation_date') else None,
                        vba_closing_date=datetime.fromisoformat(upi_data.get('vba_closing_date', datetime.now().isoformat())).date() if upi_data.get('vba_closing_date') else None,
                        status=upi_data.get('status', 'Active'),
                        additional_details=upi_data.get('additional_details'),
                        sms_ids=[upi_data.get('sms_id')] if upi_data.get('sms_id') else None
                    )
                    db.add(upi)
                    total_saved += 1
            
            # Save wallets
            if processed_data.get('wallets'):
                for wallet_data in processed_data['wallets']:
                    from ..models.customer_models import Wallet
                    wallet = Wallet(
                        customer_id=customer_id,
                        account_id=wallet_data.get('account_id'),
                        wallet_type=wallet_data.get('wallet_type'),
                        app_name=wallet_data.get('app_name'),
                        account_opening_date=datetime.fromisoformat(wallet_data.get('account_opening_date', datetime.now().isoformat())) if wallet_data.get('account_opening_date') else None,
                        account_closing_date=datetime.fromisoformat(wallet_data.get('account_closing_date', datetime.now().isoformat())) if wallet_data.get('account_closing_date') else None,
                        status=wallet_data.get('status', 'Active'),
                        current_balance=wallet_data.get('current_balance'),
                        currency_breakdown=wallet_data.get('currency_breakdown'),
                        additional_details=wallet_data.get('additional_details'),
                        sms_ids=[wallet_data.get('sms_id')] if wallet_data.get('sms_id') else None
                    )
                    db.add(wallet)
                    total_saved += 1
            
            # Save loans
            if processed_data.get('loans'):
                for loan_data in processed_data['loans']:
                    from ..models.customer_models import Loan
                    loan = Loan(
                        customer_id=customer_id,
                        loan_id=loan_data.get('loan_id'),
                        loan_type=loan_data.get('loan_type'),
                        lender_name=loan_data.get('lender_name'),
                        issuance_date=datetime.fromisoformat(loan_data.get('issuance_date', datetime.now().isoformat())) if loan_data.get('issuance_date') else None,
                        closure_date=datetime.fromisoformat(loan_data.get('closure_date', datetime.now().isoformat())) if loan_data.get('closure_date') else None,
                        loan_interest_rate=loan_data.get('loan_interest_rate'),
                        status=loan_data.get('status', 'Active'),
                        default_status=loan_data.get('default_status'),
                        loan_tenure=loan_data.get('loan_tenure'),
                        loan_amount=loan_data.get('loan_amount'),
                        pending_principal=loan_data.get('pending_principal'),
                        pending_interest=loan_data.get('pending_interest'),
                        additional_details=loan_data.get('additional_details'),
                        sms_ids=[loan_data.get('sms_id')] if loan_data.get('sms_id') else None
                    )
                    db.add(loan)
                    total_saved += 1
            
            # Save salaries
            if processed_data.get('customer_salaries'):
                for salary_data in processed_data['customer_salaries']:
                    from ..models.customer_models import CustomerSalary
                    salary = CustomerSalary(
                        customer_id=customer_id,
                        bank_account_id=salary_data.get('bank_account_id', 0),  # Default to 0 if not specified
                        employer_name=salary_data.get('employer_name'),
                        salary_amount=salary_data.get('salary_amount', 0.0),
                        salary_date=datetime.fromisoformat(salary_data.get('salary_date', datetime.now().isoformat())),
                        salary_month=salary_data.get('salary_month'),
                        salary_year=salary_data.get('salary_year'),
                        salary_type=salary_data.get('salary_type'),
                        reference_id=salary_data.get('reference_id'),
                        description=salary_data.get('description'),
                        currency=salary_data.get('currency', 'INR'),
                        additional_details=salary_data.get('additional_details'),
                        sms_id=salary_data.get('sms_id')
                    )
                    db.add(salary)
                    total_saved += 1
            
            # Save loan repayments
            if processed_data.get('loan_repayments'):
                for repayment_data in processed_data['loan_repayments']:
                    from ..models.customer_models import LoanRepayment
                    repayment = LoanRepayment(
                        customer_id=customer_id,
                        loan_id=repayment_data.get('loan_id', 0),  # Default to 0 if not specified
                        bank_account_id=repayment_data.get('bank_account_id'),
                        repayment_amount=repayment_data.get('repayment_amount', 0.0),
                        principal_amount=repayment_data.get('principal_amount'),
                        interest_amount=repayment_data.get('interest_amount'),
                        penalty_amount=repayment_data.get('penalty_amount'),
                        repayment_date=datetime.fromisoformat(repayment_data.get('repayment_date', datetime.now().isoformat())),
                        due_date=datetime.fromisoformat(repayment_data.get('due_date', datetime.now().isoformat())) if repayment_data.get('due_date') else None,
                        emi_number=repayment_data.get('emi_number'),
                        repayment_type=repayment_data.get('repayment_type'),
                        payment_status=repayment_data.get('payment_status', 'on_time'),
                        days_delayed=repayment_data.get('days_delayed', 0),
                        outstanding_balance=repayment_data.get('outstanding_balance'),
                        reference_id=repayment_data.get('reference_id'),
                        description=repayment_data.get('description'),
                        currency=repayment_data.get('currency', 'INR'),
                        additional_details=repayment_data.get('additional_details'),
                        sms_id=repayment_data.get('sms_id')
                    )
                    db.add(repayment)
                    total_saved += 1
            
            # Save credit card repayments
            if processed_data.get('credit_card_repayments'):
                for repayment_data in processed_data['credit_card_repayments']:
                    from ..models.customer_models import CreditCardRepayment
                    repayment = CreditCardRepayment(
                        customer_id=customer_id,
                        credit_card_id=repayment_data.get('credit_card_id', 0),  # Default to 0 if not specified
                        bank_account_id=repayment_data.get('bank_account_id'),
                        repayment_amount=repayment_data.get('repayment_amount', 0.0),
                        minimum_due=repayment_data.get('minimum_due'),
                        total_due=repayment_data.get('total_due'),
                        repayment_date=datetime.fromisoformat(repayment_data.get('repayment_date', datetime.now().isoformat())),
                        due_date=datetime.fromisoformat(repayment_data.get('due_date', datetime.now().isoformat())) if repayment_data.get('due_date') else None,
                        billing_cycle_start=datetime.fromisoformat(repayment_data.get('billing_cycle_start', datetime.now().isoformat())).date() if repayment_data.get('billing_cycle_start') else None,
                        billing_cycle_end=datetime.fromisoformat(repayment_data.get('billing_cycle_end', datetime.now().isoformat())).date() if repayment_data.get('billing_cycle_end') else None,
                        repayment_type=repayment_data.get('repayment_type'),
                        payment_status=repayment_data.get('payment_status', 'on_time'),
                        days_delayed=repayment_data.get('days_delayed', 0),
                        outstanding_balance=repayment_data.get('outstanding_balance'),
                        late_fee_charged=repayment_data.get('late_fee_charged'),
                        interest_charged=repayment_data.get('interest_charged'),
                        cashback_earned=repayment_data.get('cashback_earned'),
                        reward_points_earned=repayment_data.get('reward_points_earned'),
                        reference_id=repayment_data.get('reference_id'),
                        description=repayment_data.get('description'),
                        currency=repayment_data.get('currency', 'INR'),
                        additional_details=repayment_data.get('additional_details'),
                        sms_id=repayment_data.get('sms_id')
                    )
                    db.add(repayment)
                    total_saved += 1
            
            # Save service accounts
            if processed_data.get('service_accounts'):
                for account_data in processed_data['service_accounts']:
                    from ..models.customer_models import ServiceAccount
                    account = ServiceAccount(
                        customer_id=customer_id,
                        account_type=account_data.get('account_type'),
                        app_name=account_data.get('app_name'),
                        account_id=account_data.get('account_id'),
                        account_opening_date=datetime.fromisoformat(account_data.get('account_opening_date', datetime.now().isoformat())) if account_data.get('account_opening_date') else None,
                        status=account_data.get('status', 'Active'),
                        total_amount_spent=account_data.get('total_amount_spent', 0.0),
                        last_transaction_date=datetime.fromisoformat(account_data.get('last_transaction_date', datetime.now().isoformat())) if account_data.get('last_transaction_date') else None,
                        additional_details=account_data.get('additional_details'),
                        sms_ids=[account_data.get('sms_id')] if account_data.get('sms_id') else None
                    )
                    db.add(account)
                    total_saved += 1

            # Save customer events
            if processed_data.get('customer_events'):
                for event_data in processed_data['customer_events']:
                    event = CustomerEvent(
                        customer_id=customer_id,
                        entity_type=event_data.get('entity_type', 'sms_analysis'),
                        entity_id=event_data.get('entity_id', 0),
                        source=event_data.get('source', 'sms_analysis'),
                        event_type=event_data.get('event_type'),
                        information_type=event_data.get('information_type'),
                        event_sub_type=event_data.get('event_sub_type'),
                        event_source=event_data.get('event_source', 'sms'),
                        event_datetime=datetime.fromisoformat(event_data.get('event_datetime', datetime.now().isoformat())),
                        platform=event_data.get('platform'),
                        status=event_data.get('status'),
                        description=event_data.get('description'),
                        reference_id=event_data.get('reference_id'),
                        additional_details=event_data.get('additional_details'),
                        sms_id=event_data.get('sms_id')
                    )
                    db.add(event)
                    total_saved += 1

            # Save brokerage accounts
            if processed_data.get('brokerage_accounts'):
                for brokerage_data in processed_data['brokerage_accounts']:
                    from ..models.customer_models import BrokerageAccount
                    brokerage = BrokerageAccount(
                        customer_id=customer_id,
                        account_id=brokerage_data.get('account_id'),
                        app_name=brokerage_data.get('app_name'),
                        account_opening_date=datetime.fromisoformat(brokerage_data.get('account_opening_date', datetime.now().isoformat())) if brokerage_data.get('account_opening_date') else None,
                        account_closing_date=datetime.fromisoformat(brokerage_data.get('account_closing_date', datetime.now().isoformat())) if brokerage_data.get('account_closing_date') else None,
                        status=brokerage_data.get('status', 'Active'),
                        stocks_amount=brokerage_data.get('stocks_amount'),
                        mutual_funds_amount=brokerage_data.get('mutual_funds_amount'),
                        additional_details=brokerage_data.get('additional_details'),
                        sms_ids=[brokerage_data.get('sms_id')] if brokerage_data.get('sms_id') else None
                    )
                    db.add(brokerage)
                    total_saved += 1

            # Save fixed deposits
            if processed_data.get('fixed_deposits'):
                for fd_data in processed_data['fixed_deposits']:
                    from ..models.customer_models import FixedDeposit
                    fd = FixedDeposit(
                        customer_id=customer_id,
                        deposit_id=fd_data.get('deposit_id'),
                        deposit_type=fd_data.get('deposit_type'),
                        partner_name=fd_data.get('partner_name'),
                        bank_name=fd_data.get('bank_name'),
                        opening_date=datetime.fromisoformat(fd_data.get('opening_date', datetime.now().isoformat())) if fd_data.get('opening_date') else None,
                        maturity_date=datetime.fromisoformat(fd_data.get('maturity_date', datetime.now().isoformat())) if fd_data.get('maturity_date') else None,
                        fd_tenure=fd_data.get('fd_tenure'),
                        fd_interest_rate=fd_data.get('fd_interest_rate'),
                        status=fd_data.get('status', 'Active'),
                        initial_fd_amount=fd_data.get('initial_fd_amount'),
                        current_fd_amount=fd_data.get('current_fd_amount'),
                        additional_details=fd_data.get('additional_details'),
                        sms_ids=[fd_data.get('sms_id')] if fd_data.get('sms_id') else None
                    )
                    db.add(fd)
                    total_saved += 1

            db.commit()
            print(f"INFO: Successfully saved {total_saved} records to database across all table types")
            
            # Log final breakdown
            print("INFO: Database save breakdown:")
            for table_name, records in processed_data.items():
                if records:
                    print(f"INFO:   {table_name}: {len(records)} records saved")
            
        except Exception as e:
            db.rollback()
            print(f"ERROR: Error saving data to database: {str(e)}")
            raise
    
    async def process_sms_file(self, file_path: str, customer_id: str) -> Dict[str, Any]:
        """Main method to process SMS file end-to-end"""
        start_time = datetime.now()
        print(f"INFO: Starting SMS processing for customer {customer_id}")
        print(f"INFO: File path: {file_path}")
        print(f"INFO: OpenAI processing enabled: {self.use_openai}")
        
        try:
            # Step 1: Process CSV and classify SMS
            print("INFO: Step 1: Processing CSV and classifying SMS...")
            classification_result = self.process_csv_file(file_path, customer_id)
            
            # Step 2: Process financial SMS with OpenAI
            if classification_result['financial_sms']:
                print("INFO: Step 2: Processing financial SMS...")
                processed_data = await self.process_financial_sms_parallel(classification_result['financial_sms'])
                
                # Step 3: Save to database
                print("INFO: Step 3: Saving data to database...")
                db = next(get_db())
                try:
                    await self.save_data_to_db(processed_data, customer_id, db)
                finally:
                    db.close()
                
                # Count total processed records
                total_records = sum(len(records) for records in processed_data.values())
                classification_result['processed_transactions'] = total_records
                classification_result['processed_data_summary'] = {
                    table_name: len(records) for table_name, records in processed_data.items() if records
                }
                
                print(f"INFO: Step 3 completed: {total_records} records processed and saved")
            else:
                print("INFO: No financial SMS found, skipping processing")
                classification_result['processed_transactions'] = 0
                classification_result['processed_data_summary'] = {}
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            classification_result['processing_time_seconds'] = processing_time
            
            print(f"INFO: SMS processing completed in {processing_time:.2f} seconds")
            print("INFO: Final summary:")
            print(f"INFO:   - Marketing SMS: {classification_result['marketing_count']}")
            print(f"INFO:   - Financial SMS: {classification_result['financial_count']}")
            print(f"INFO:   - Processed records: {classification_result['processed_transactions']}")
            print(f"INFO:   - Processing time: {processing_time:.2f} seconds")
            
            return classification_result
            
        except Exception as e:
            print(f"ERROR: Error in SMS processing: {str(e)}")
            raise

    def _prepare_db_ready_data(self, processed_data: Dict[str, List[Dict]], customer_id: str) -> Dict[str, List[Dict]]:
        """Prepare data in exact format needed for database insertion"""
        print("INFO: Preparing database-ready data...")

        db_ready_data = {}

        for table_name, records in processed_data.items():
            if not records:
                continue

            db_ready_data[table_name] = []

            for record in records:
                try:
                    # Add customer_id to all records
                    record['customer_id'] = customer_id

                    # Prepare record based on table type
                    if table_name == 'transactions':
                        db_record = self._prepare_transaction_record(record)
                    elif table_name == 'bank_accounts':
                        db_record = self._prepare_bank_account_record(record)
                    elif table_name == 'credit_cards':
                        db_record = self._prepare_credit_card_record(record)
                    elif table_name == 'upi_accounts':
                        db_record = self._prepare_upi_account_record(record)
                    elif table_name == 'wallets':
                        db_record = self._prepare_wallet_record(record)
                    elif table_name == 'loans':
                        db_record = self._prepare_loan_record(record)
                    elif table_name == 'customer_salaries':
                        db_record = self._prepare_salary_record(record)
                    elif table_name == 'loan_repayments':
                        db_record = self._prepare_loan_repayment_record(record)
                    elif table_name == 'credit_card_repayments':
                        db_record = self._prepare_cc_repayment_record(record)
                    elif table_name == 'service_accounts':
                        db_record = self._prepare_service_account_record(record)
                    elif table_name == 'customer_events':
                        db_record = self._prepare_customer_event_record(record)
                    elif table_name == 'brokerage_accounts':
                        db_record = self._prepare_brokerage_record(record)
                    elif table_name == 'fixed_deposits':
                        db_record = self._prepare_fd_record(record)
                    else:
                        db_record = record  # Fallback

                    if db_record:
                        db_ready_data[table_name].append(db_record)

                except Exception as e:
                    print(f"ERROR: Error preparing {table_name} record: {str(e)}")
                    continue

        total_records = sum(len(records) for records in db_ready_data.values())
        print(f"INFO: Prepared {total_records} database-ready records")

        return db_ready_data

    def _prepare_transaction_record(self, record: Dict) -> Dict:
        """Prepare transaction record for database insertion"""
        return {
            'customer_id': record['customer_id'],
            'source_type': record.get('source_type', 'sms_analysis'),
            'source_id': record.get('source_id', 0),
            'from_account_id': record.get('from_account_id'),
            'to_account_id': record.get('to_account_id'),
            'amount': float(record.get('amount', 0.0)),
            'currency': record.get('currency', 'INR'),
            'transaction_date': self._parse_datetime(record.get('transaction_date')),
            'platform': record.get('platform'),
            'txn_status': record.get('txn_status', 'success'),
            'information_type': record.get('information_type'),
            'transaction_type': record.get('transaction_type'),
            'transaction_sub_type': record.get('transaction_sub_type'),
            'description': record.get('description'),
            'status': record.get('status', 'success'),
            'reference_id': record.get('reference_id'),
            'additional_details': record.get('additional_details'),
            'sms_id': record.get('sms_id')
        }

    def _prepare_bank_account_record(self, record: Dict) -> Dict:
        """Prepare bank account record for database insertion"""
        return {
            'customer_id': record['customer_id'],
            'account_number': record.get('account_number'),
            'ifsc_code': record.get('ifsc_code'),
            'account_sub_type': record.get('account_sub_type'),
            'bank_name': record.get('bank_name'),
            'account_opening_date': self._parse_datetime(record.get('account_opening_date')),
            'account_closing_date': self._parse_datetime(record.get('account_closing_date')),
            'savings_interest_rate': record.get('savings_interest_rate'),
            'status': record.get('status', 'Active'),
            'last_updated_balance_amount': float(record.get('last_updated_balance_amount', 0.0)) if record.get('last_updated_balance_amount') else None,
            'linked_vbas': record.get('linked_vbas'),
            'additional_details': record.get('additional_details'),
            'sms_ids': [record.get('sms_id')] if record.get('sms_id') else None
        }

    def _parse_datetime(self, date_str) -> datetime:
        """Parse datetime string safely"""
        if not date_str:
            return None

        if isinstance(date_str, datetime):
            return date_str

        try:
            if 'T' in str(date_str):
                return datetime.fromisoformat(str(date_str).replace('Z', '+00:00'))
            else:
                return datetime.fromisoformat(str(date_str))
        except:
            return None