import re
from .sms_constants import FINANCIAL_KEYWORDS, CURRENCY_KEYWORDS, BANK_NAMES, FINTECH_NAMES, NBFC_NAMES, INCLUDE_SENDER_PATTERNS


def is_mobile_number(sender: str) -> bool:
    """Check if sender is a mobile number (with or without country code)"""
    # Remove any non-digit characters except + at the beginning
    cleaned = re.sub(r'^[+]?', '', sender)
    cleaned = re.sub(r'[^\d]', '', cleaned)
    
    # Check for various mobile number patterns:
    # 10-digit Indian mobile: [6-9]xxxxxxxxx
    # With 91 country code: 91[6-9]xxxxxxxxx
    # With other country codes: any 10-15 digit number
    patterns = [
        r'^[6-9]\d{9}$',           # 10-digit Indian mobile
        r'^91[6-9]\d{9}$',         # Indian mobile with 91
        r'^\d{10,15}$'             # Any 10-15 digit number (international)
    ]
    
    return any(re.match(pattern, cleaned) for pattern in patterns)


def contains_otp(message: str) -> bool:
    """Check if message contains OTP keywords"""
    text = message.lower()
    
    # OTP keywords and patterns
    otp_keywords = [
        r'\botp\b',
        r'\bpassword\b',
        r'\bverification code\b',
        r'\bverify\b',
        r'\bconfirm\b',
        r'\bactivate\b',
        r'\bone time password\b',
        r'\bsecurity code\b',
        r'\bauthentication code\b',
        r'\blogin code\b',
        r'\baccess code\b',
        r'\bpin\b.*\bcode\b',
        r'\bcode\b.*\bverification\b',
        r'\bverification\b.*\bcode\b'
    ]
    
    # Check for OTP patterns (4-8 digit codes)
    otp_patterns = [
        r'\b\d{4,8}\b.*(?:otp|code|password|verify|confirm)',
        r'(?:otp|code|password|verify|confirm).*\b\d{4,8}\b',
        r'is\s+\d{4,8}',  # "Your OTP is 1234"
        r'\d{4,8}\s+is.*(?:otp|code|password)',  # "1234 is your OTP"
    ]
    
    # Check for OTP keywords
    for keyword in otp_keywords:
        if re.search(keyword, text):
            return True
    
    # Check for OTP patterns
    for pattern in otp_patterns:
        if re.search(pattern, text):
            return True
    
    return False
def should_include_sms(sender: str, message: str) -> bool:
    """
    Determine if SMS should be included based on sender and message content
    """
    print(f"[SMS_FILTER] Filtering SMS - Sender: {sender}")
    
    # 1. Remove: sender is a mobile number (with or without country code)
    if is_mobile_number(sender):
        print(f"[SMS_FILTER] SMS rejected - sender is mobile number")
        return False
    
    # 2. Remove: message contains OTP
    if contains_otp(message):
        print(f"[SMS_FILTER] SMS rejected - message contains OTP")
        return False
        
    # 3. Include: sender has specific character sequence
    for pat in INCLUDE_SENDER_PATTERNS:
        if re.search(pat, sender):
            print(f"[SMS_FILTER] SMS accepted - sender matches include pattern: {pat}")
            return True
            
    # 4. Include: messages with financial keywords/context
    text = message.lower()
    for pat in FINANCIAL_KEYWORDS + CURRENCY_KEYWORDS + BANK_NAMES + FINTECH_NAMES + NBFC_NAMES:
        if re.search(pat, text):
            print(f"[SMS_FILTER] SMS accepted - message contains financial keyword: {pat}")
            return True
            
    print(f"[SMS_FILTER] SMS rejected - no financial keywords found")
    return False


def validate_sms_data(sms_row: dict) -> bool:
    """
    Validate SMS data structure
    """
    required_fields = ['text', 'senderAddress', 'phoneNumber', 'id', 'updateAt']
    
    for field in required_fields:
        if field not in sms_row:
            print(f"[SMS_FILTER] SMS validation failed - missing field: {field}")
            return False
    
    if not sms_row['text'] or not sms_row['text'].strip():
        print(f"[SMS_FILTER] SMS validation failed - empty message text")
        return False
        
    return True


def filter_financial_sms(sms_list: list) -> list:
    """
    Filter list of SMS messages to only include financial ones
    """
    print(f"[SMS_FILTER] Starting to filter {len(sms_list)} SMS messages")
    
    filtered_sms = []
    
    for idx, sms in enumerate(sms_list):
        sms_row = sms.dict() if hasattr(sms, 'dict') else sms
        
        # Validate SMS data structure
        if not validate_sms_data(sms_row):
            print(f"[SMS_FILTER] SMS {idx+1} skipped - validation failed")
            continue
            
        sender = sms_row.get('senderAddress', '')
        message = sms_row.get('text', '')
        
        # Apply filtering logic
        if should_include_sms(sender, message):
            filtered_sms.append(sms)
            print(f"[SMS_FILTER] SMS {idx+1} included in filtered list")
        else:
            print(f"[SMS_FILTER] SMS {idx+1} excluded from filtered list")
    
    print(f"[SMS_FILTER] Filtering complete - {len(filtered_sms)}/{len(sms_list)} SMS messages included")
    return filtered_sms
