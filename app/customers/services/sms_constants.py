"""
SMS parsing constants and patterns - Cleaned version
Contains only the constants used by the current SMS pipeline parser
"""
import re

# =============================================================================
# BASIC KEYWORDS AND IDENTIFIERS
# =============================================================================

# Financial keywords for identifying SMS types
FINANCIAL_KEYWORDS = [
    r"credit", r"credited", r"debit", r"debited", r"paid", r"spent",
    r"balance", r"amount", r"transaction", r"txn", r"refund",
    r"payment", r"repayment", r"pay", r"repay", r"prepaid", r"postpaid", r"recharge", r"purchase",
    r"investment", r"invested", r"securities", r"broking", r"broker", r"fd", r"fixed deposit", r"forex",
    r"epf", r"epfo", r"cibil", r"equifax", r"visa", r"mastercard",
    r"card", r"loan", r"bill", r"bank", r"finance", r"limit", r"mandate", r"wallet", r"statement"
]

# Currency keywords
CURRENCY_KEYWORDS = [
    r"rs", r"rs\.", r"rupees", r"₹", r"inr", r"usd", r"$", r"btc", r"bitcoin", r"eth", r"sol", r"gold",
    r"lakh", r"crore", r"cr", r"cr\."
]

# Bank names
BANK_NAMES = [
    r"hdfc", r"icici", r"bob", r"sbi", r"axis", r"kotak", r"yes bank", r"indusind", r"federal", 
    r"canara", r"pnb", r"idfc", r"union bank", r"rbl", r"equitas", r"au small finance", r"bandhan", 
    r"uco", r"central bank", r"dcb", r"karur vysya", r"south indian bank", r"city union", 
    r"dhanlaxmi", r"idbi", r"j&k bank", r"karnataka bank", r"tmb", r"suryoday", r"nsdl", r"nabard"
]

# Fintech company names
FINTECH_NAMES = [
    r"google ?pay", r"phone ?pe", r"bharat ?pe", r"paytm", r"amazon ?pay", r"mobikwik", r"freecharge", 
    r"airtel ?payments? bank", r"jio ?payments? bank", r"onecard", r"slice", r"cred", r"neupay", 
    r"razorpay", r"pine labs", r"cashfree", r"instamojo", r"payu", r"payzapp", r"olamoney", r"mpesa", 
    r"paypal", r"simpl", r"zestmoney", r"lazy ?pay", r"flexmoney", r"open", r"fi money", r"navi", 
    r"groww", r"zerodha", r"upstox", r"angel ?one", r"angelone"
]

# NBFC names
NBFC_NAMES = [
    r"bajaj finance", r"muthoot", r"manappuram", r"shriram", r"mahindra finance", r"l&t finance", 
    r"tata capital", r"cholamandalam", r"hdb", r"indostar", r"piramal", r"aditya birla", r"fullerton", 
    r"hero fincorp", r"capital first", r"motilal oswal", r"edelweiss", r"iifl", r"dhfl", 
    r"reliance capital", r"sundaram finance", r"magma", r"srei", r"indiabulls", r"pnb housing", 
    r"canfin homes", r"lic housing", r"gruh finance", r"repco home", r"aptus value", r"aum capital", 
    r"ashv finance", r"five star", r"smc finance"
]

# Include sender patterns
INCLUDE_SENDER_PATTERNS = [
    r"JX-ICICIT-S", r"VM-HDFCBN-P", r"AX-HDFCBN-S", r"CP-EQUTAS-S", r"VM-CIBILP-S", r"VA-ONESCR-S"
]

# =============================================================================
# ACCOUNT AND TRANSACTION PATTERNS
# =============================================================================

# Account number patterns
ACCOUNT_PATTERNS = [
    # UPI and bank account patterns with alphanumeric support
    r"(?:A/C|a/c|account)\s*(?:no\.?|number)?\s*([A-Z]?\d{4,18})",  # Handles X4884 format
    r"(?:A/C|a/c|account)\s*(?:no\.?|number)?\s*(\d{8,18})",       # Original numeric format
    r"(?:A/C|a/c|account)\s*(?:no\.?|number)?\s*(XX\d{4})",        # Handles XX1234 format
    r"(?:A/C|a/c|account)\s*(?:no\.?|number)?\s*(X{3,}[\dX]{4,})",  # Handles XXXXX724884 format (masked accounts)
    r"(?:A/C|a/c|account)\s*\*(\d{4})",                           # Handles a/c *1234 format
    r"(?:A/C|a/c|account)\s*(xxx\d{3,})",                         # Handles "A/c xxx884" format
    r"account[:\s]*(\d{8,18})",
    r"(?:credited to|debited from|in (?:A/C|a/c|account))[:\s]*([A-Z]?\d{4,18})",  # UPI format support
    r"(?:credited to|debited from|in (?:A/C|a/c|account))[:\s]*(\d{8,18})",
    r"(?:from|to)\s*(?:A/C|a/c|account)?[:\s]*([A-Z]?\d{4,18})",   # UPI format support
    r"(?:from|to)\s*(?:A/C|a/c|account)?[:\s]*(\d{8,18})",
    r"(?:account|A/C|a/c)\s+ending\s+with[:\s]*(\d{4})",  # Fix: "ending with" pattern for A/C too
    r"account\s+ending\s+with[:\s]*(\d{4})",              # Original: "account ending with" pattern
    r"account\s+ending[:\s]*(\d{4})",                     # Original "ending" pattern
    r"(?:account|A/C|a/c)\s*(?:ending|xxxx)[:\s]*(\d{4})",
    r"in your account[:\s]*(\d{8,18})",
    r"(?:credited|debited)[:\s]*(\d{8,18})"
]

# =============================================================================
# MARKETING AND CLASSIFICATION PATTERNS
# =============================================================================

# Marketing patterns for identifying promotional messages
MARKETING_PATTERNS = [
    # Promotional offers and discounts
    r"(?:upto|up to)\s*\d+%\s*(?:off|discount|cashback)",
    r"(?:save|get|earn|win)\s*(?:upto|up to)?\s*(?:rs\.?|₹|inr)?\s*\d+",
    r"(?:offer|deal|sale)\s*(?:valid|ends|expires)",
    r"(?:limited time|hurry|act fast|don't miss)",
    r"(?:free|complimentary|zero cost)",
    r"(?:shop now|order now|buy now|download now)",
    r"(?:click|tap|visit|goto)\s*(?:here|link|url|http)",
    r"(?:terms|t&c|conditions)\s*apply",
    r"(?:cashback|rewards|points)\s*(?:upto|up to)?\s*\d+",
    r"(?:minimum|min)\s*(?:purchase|order|transaction)",
    
    # Subscription and plan offers
    r"(?:plan|subscription|package)\s*(?:starting|from)\s*(?:rs\.?|₹|inr)?\s*\d+",
    r"(?:upgrade|renew|extend)\s*(?:plan|subscription)",
    r"(?:validity|expires?)\s*(?:in|on)\s*\d+\s*(?:days?|months?)",
    
    # Investment and financial product marketing
    r"(?:invest|investment)\s*(?:starting|from)\s*(?:rs\.?|₹|inr)?\s*\d+",
    r"(?:returns?|profit)\s*(?:upto|up to)?\s*\d+%",
    r"(?:risk free|guaranteed|assured)\s*(?:returns?|profit)",
    
    # Generic marketing keywords
    r"congratulations?\s*(?:you|u)\s*(?:have|are|won)",
    r"(?:lucky|winner|selected|chosen)",
    r"(?:claim|redeem|collect)\s*(?:your|ur)\s*(?:reward|prize|gift)",
    r"(?:new|latest|trending|popular)\s*(?:offers?|deals?)",
    r"(?:flash|mega|super|grand)\s*(?:sale|offer|deal)",
    r"(?:last|final)\s*(?:chance|day|hours?)",
    
    # App download and registration
    r"(?:download|install)\s*(?:app|application)",
    r"(?:register|signup|sign up|join)\s*(?:now|today|free)",
    r"(?:available|live)\s*on\s*(?:playstore|appstore|play store|app store)",
    
    # URLs and links
    r"http[s]?://",
    r"www\.",
    r"\.com",
    r"bit\.ly",
    r"tinyurl",
    
    # Lottery and gaming
    r"(?:lottery|lotto|draw|contest|quiz)",
    r"(?:spin|scratch|play)\s*(?:and|to|&)\s*win",
    r"(?:jackpot|bumper|mega)\s*(?:prize|amount)",
    
    # Credit offers
    r"(?:pre.?approved|instant|quick)\s*(?:loan|credit|card)",
    r"(?:credit limit|loan amount)\s*(?:upto|up to)\s*(?:rs\.?|₹|inr)?\s*\d+",
    r"(?:low|lowest|best)\s*(?:interest|rate)",
    
    # Recharge and bill payment offers
    r"(?:recharge|bill payment)\s*(?:offer|cashback)",
    r"(?:mobile|dth|electricity|gas)\s*(?:recharge|bill)\s*(?:offer|discount)",
    
    # E-commerce offers
    r"(?:amazon|flipkart|myntra|ajio)\s*(?:sale|offer|deal)",
    r"(?:fashion|electronics|mobile|laptop)\s*(?:sale|offer)",
    r"(?:free|same day|next day)\s*(?:delivery|shipping)",
    
    # Insurance and protection plans
    r"(?:insurance|cover|protection)\s*(?:starting|from)\s*(?:rs\.?|₹|inr)?\s*\d+",
    r"(?:health|life|motor|travel)\s*(?:insurance|cover)",
    
    # Mutual funds and investment
    r"(?:mutual fund|mf|sip)\s*(?:investment|returns?)",
    r"(?:portfolio|wealth)\s*(?:management|creation)",
    
    # Generic spam indicators
    r"(?:act now|limited period|exclusive|special)",
    r"(?:100%|percent)\s*(?:genuine|authentic|real)",
    r"(?:no|zero)\s*(?:hidden|extra)\s*(?:charges?|fees?|cost)",
    r"(?:call|sms|whatsapp)\s*(?:now|us|immediately)",
]

# =============================================================================
# WALLET AND UPI PATTERNS
# =============================================================================

# Wallet patterns
WALLET_PATTERNS = [
    r"(?:paytm|phonepe|google pay|amazon pay|mobikwik|freecharge)",
    r"(?:airtel money|jio money|ola money|uber money)",
    r"(?:payzapp|yono|bhim|tez)",
    r"(?:digital wallet|e.?wallet|mobile wallet)",
    r"(?:wallet balance|wallet amount|wallet limit)"
]

# UPI VPA Pattern
UPI_VPA_PATTERN = r"([a-zA-Z0-9.\-_]+@[a-zA-Z0-9]+)"

# =============================================================================
# VALIDATION CONSTANTS
# =============================================================================

# Valid event types
VALID_EVENT_TYPES = {
    'Purchase', 'Money Transfer', 'Payment', 'Deposit & Withdrawal', 
    'Investment', 'Accounts', 'Others'
}

# Valid information types
VALID_INFORMATION_TYPES = {
    'Inflow', 'Outflow', 'Application', 'Account Status', 'Balance Update'
}

# Valid event subtypes
VALID_EVENT_SUBTYPES = {
    # Purchase subtypes
    'Credit Card', 'Debit Card', 'UPI', 'NEFT', 'RTGS', 'Check',
    # Money Transfer subtypes
    'Wallet Deposit', 'Crypto Wallet', 'Money Wallet', 'Brokerage Fund/Margin', 'Remittance',
    # Payment subtypes - enhanced for loan and credit card repayments
    'EMI Payment', 'Bill Payment', 'Recharge Payment', 'Loan Repayment', 'Credit Card Repayment',
    # Deposit & Withdrawal subtypes
    'Monthly Salary Credit', 'Annual Bonus', 'Cash Deposit', 'Check Deposit', 'Loan Disbursal', 'Cash Withdrawal',
    # Investment subtypes
    'FD', 'Stocks', 'MF', 'Gold', 'PPF', 'Post-Office', 'Insurance', 'LIC/ULIP Schemes', 'Crypto',
    # Accounts subtypes
    'Bank Account', 'Loan', 'Wallet', 'Brokerage Account', 'Fixed Deposit', 'PF Account', 'Insurance Account', 'Credit Report'
}

# =============================================================================
# MAPPING CONSTANTS
# =============================================================================

# Information type mapping
INFORMATION_TYPE_MAPPING = {
    'inflow': 'Inflow',
    'outflow': 'Outflow', 
    'application': 'Application',
    'account_status': 'Account Status',
    'balance_update': 'Balance Update'
}

# Event type mapping
EVENT_TYPE_MAPPING = {
    'purchase': 'Purchase',
    'money_transfer': 'Money Transfer',
    'payment': 'Payment',
    'deposit_withdrawal': 'Deposit & Withdrawal',
    'investment': 'Investment',
    'accounts': 'Accounts',
    'others': 'Others'
}
