from pydantic import BaseModel
from typing import Dict, Any, Optional, List

class SMSProcessingRequest(BaseModel):
    customer_id: str
    file_path: Optional[str] = None

class SMSProcessingResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class SMSClassificationResult(BaseModel):
    marketing_count: int
    financial_count: int
    marketing_csv_path: str
    processed_transactions: int
    processing_time_seconds: float

class TransactionData(BaseModel):
    sms_id: str
    transaction_type: str
    amount: float
    currency: str = "INR"
    from_account: Optional[str] = None
    to_account: Optional[str] = None
    transaction_date: str
    platform: Optional[str] = None
    status: str = "success"
    description: Optional[str] = None
    reference_id: Optional[str] = None

class ProcessingStatus(BaseModel):
    customer_id: str
    processed_transactions: int
    marketing_sms_files: int
    latest_marketing_file: Optional[str] = None 