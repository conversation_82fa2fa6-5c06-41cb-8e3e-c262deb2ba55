from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class SMSType(str, Enum):
    # Event type values from DB
    MONEY_TRANSFER = "Money Transfer"
    DEPOSIT_WITHDRAWAL = "Deposit & Withdrawal"
    PAYMENT = "Payment"
    PURCHASE = "Purchase"
    INVESTMENT = "Investment"
    ACCOUNTS = "Accounts"
    OTHERS = "Others"

class SMSInfoType(str, Enum):
    # Information type DB values
    INFLOW = "Inflow"
    OUTFLOW = "Outflow"
    APPLICATION = "Application"
    ACCOUNT_STATUS = "Account Status"
    BALANCE_UPDATE = "Balance Update"

class SMSEventSubtype(str, Enum):
    # Event sub type values from DB
    # Purchase subtypes
    CREDIT_CARD = "Credit Card"
    DEBIT_CARD = "Debit Card"
    UPI = "UPI"
    NEFT = "NEFT"
    RTGS = "RTGS"
    CHECK = "Check"
    
    # Money Transfer subtypes
    WALLET_DEPOSIT = "Wallet Deposit"
    CRYPTO_WALLET = "Crypto Wallet"
    MONEY_WALLET = "Money Wallet"
    BROKERAGE_FUND = "Brokerage Fund/Margin"
    REMITTANCE = "Remittance"
    
    # Payment subtypes
    EMI_PAYMENT = "EMI Payment"
    BILL_PAYMENT = "Bill Payment"
    RECHARGE_PAYMENT = "Recharge Payment"
    
    # Deposit & Withdrawal subtypes
    MONTHLY_SALARY_CREDIT = "Monthly Salary Credit"
    ANNUAL_BONUS = "Annual Bonus"
    CASH_DEPOSIT = "Cash Deposit"
    CHECK_DEPOSIT = "Check Deposit"
    LOAN_DISBURSAL = "Loan Disbursal"
    CASH_WITHDRAWAL = "Cash Withdrawal"
    
    # Investment subtypes
    FD = "FD"
    STOCKS = "Stocks"
    MF = "MF"
    GOLD = "Gold"
    PPF = "PPF"
    POST_OFFICE = "Post-Office"
    INSURANCE = "Insurance"
    LIC_ULIP = "LIC/ULIP Schemes"
    CRYPTO = "Crypto"
    
    # Accounts subtypes
    BANK_ACCOUNT = "Bank Account"
    LOAN = "Loan"
    WALLET = "Wallet"
    BROKERAGE_ACCOUNT = "Brokerage Account"
    FIXED_DEPOSIT = "Fixed Deposit"
    PF_ACCOUNT = "PF Account"
    INSURANCE_ACCOUNT = "Insurance Account"
    CREDIT_REPORT = "Credit Report"

class TransactionDetails(BaseModel):
    amount: Optional[float] = None
    currency: Optional[str] = None
    transactionId: Optional[str] = Field(None, alias="txn_ref")
    referenceNumber: Optional[str] = Field(None, alias="txn_ref")
    merchantName: Optional[str] = Field(None, alias="merchant")
    merchantCategory: Optional[str] = None
    location: Optional[str] = None
    fee: Optional[float] = None
    tax: Optional[float] = None
    exchangeRate: Optional[float] = None
    balanceAfter: Optional[float] = None
    balanceBefore: Optional[float] = None
    description: Optional[str] = None

class AccountDetails(BaseModel):
    account_type: Optional[str] = Field(None, alias="Account Type")
    account_id: Optional[str] = Field(None, alias="Account ID")
    account_sub_type: Optional[str] = Field(None, alias="Account Sub-Type")
    opening_closing_date: Optional[str] = Field(None, alias="Opening/Closing Date")
    company: Optional[str] = Field(None, alias="Company")
    network: Optional[str] = Field(None, alias="Network")
    tenure: Optional[str] = Field(None, alias="Tenure")
    interest_rate: Optional[str] = Field(None, alias="Interest Rate")
    current_status: Optional[str] = Field(None, alias="Current Status")
    default_status: Optional[str] = Field(None, alias="Default Status")
    starting_amount: Optional[str] = Field(None, alias="Starting Amount")
    current_amount: Optional[str] = Field(None, alias="Current Amount")
    linked_account: Optional[str] = Field(None, alias="Linked Account")
    last_updated: Optional[str] = Field(None, alias="Last Updated")
    additional_details: Optional[str] = Field(None, alias="Additional Details")

class SMSEvent(BaseModel):
    id: str
    smsType: SMSType
    infoType: SMSInfoType
    eventSubtype: Optional[SMSEventSubtype] = None
    sender: str
    recipient: str
    message: str
    timestamp: str
    transactionDetails: Optional[TransactionDetails] = None
    accountDetails: Optional[AccountDetails] = None

    class Config:
        populate_by_name = True

class SMSEventsResponse(BaseModel):
    events: List[SMSEvent]
    total: int
    page: int
    limit: int
    hasMore: bool

class SMSEventsRequest(BaseModel):
    page: int = Field(default=1, ge=1)
    limit: int = Field(default=20, le=100)
    customer_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    event_types: Optional[List[str]] = None
