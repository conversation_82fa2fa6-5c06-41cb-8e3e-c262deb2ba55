import os
from typing import Dict, Any
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from ...database import get_db
from ..services.sms_processor import SMSProcessor
from ..schemas.sms_processor_schema import SMSProcessingResponse, SMSProcessingRequest
router = APIRouter(prefix="/sms", tags=["SMS Processing"])

# Initialize SMS processor with OpenAI enabled by default
# Set to False to disable OpenAI and use fallback processing only
use_openai = os.getenv("SMS_USE_OPENAI", "true").lower() == "true"
sms_processor = SMSProcessor(use_openai=use_openai)

@router.post("/process-file", response_model=SMSProcessingResponse)
async def process_sms_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    customer_id: str = None,
    db: Session = Depends(get_db)
):
    """
    Process SMS file and classify messages into marketing and financial categories.
    Financial messages are processed with OpenAI and saved to database.
    """
    print(f"INFO: Received SMS processing request for customer: {customer_id}")
    print(f"INFO: File: {file.filename}, Size: {file.size if hasattr(file, 'size') else 'unknown'}")
    
    try:
        # Validate file
        if not file.filename.endswith('.csv'):
            print(f"ERROR: Invalid file type: {file.filename}")
            raise HTTPException(status_code=400, detail="Only CSV files are supported")
        
        # Create upload directory
        upload_dir = "data/sms_uploads"
        os.makedirs(upload_dir, exist_ok=True)
        print(f"DEBUG: Upload directory: {upload_dir}")
        
        # Save uploaded file
        file_path = os.path.join(upload_dir, f"{customer_id}_{file.filename}")
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        print(f"INFO: File uploaded successfully: {file_path}")
        print(f"INFO: File size: {len(content)} bytes")
        
        # Process the file
        print("INFO: Starting SMS processing...")
        result = await sms_processor.process_sms_file(file_path, customer_id)
        
        # Clean up uploaded file
        os.remove(file_path)
        print(f"DEBUG: Cleaned up uploaded file: {file_path}")
        
        print("INFO: SMS processing completed successfully")
        print(f"INFO: Results: {result.get('marketing_count', 0)} marketing, {result.get('financial_count', 0)} financial, {result.get('processed_transactions', 0)} processed")
        
        return SMSProcessingResponse(
            success=True,
            message="SMS processing completed successfully",
            data=result
        )
        
    except Exception as e:
        print(f"ERROR: Error processing SMS file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing SMS file: {str(e)}")

@router.get("/download-marketing-sms/{customer_id}")
async def download_marketing_sms(customer_id: str):
    """
    Download marketing SMS CSV file for a customer
    """
    try:
        # Find the most recent marketing SMS file for the customer
        marketing_dir = "data/marketing_sms"
        if not os.path.exists(marketing_dir):
            raise HTTPException(status_code=404, detail="No marketing SMS files found")
        
        # Find files for this customer
        customer_files = [f for f in os.listdir(marketing_dir) if f.startswith(f"marketing_sms_{customer_id}_")]
        
        if not customer_files:
            raise HTTPException(status_code=404, detail="No marketing SMS files found for this customer")
        
        # Get the most recent file
        latest_file = sorted(customer_files)[-1]
        file_path = os.path.join(marketing_dir, latest_file)
        
        return FileResponse(
            path=file_path,
            filename=latest_file,
            media_type='text/csv'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"ERROR: Error downloading marketing SMS file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error downloading file: {str(e)}")

@router.get("/processing-status/{customer_id}")
async def get_processing_status(customer_id: str, db: Session = Depends(get_db)):
    """
    Get SMS processing status for a customer
    """
    try:
        from ..models.customer_models import CustomerTransaction
        
        # Get transaction count for customer
        transaction_count = db.query(CustomerTransaction).filter(
            CustomerTransaction.customer_id == customer_id
        ).count()
        
        # Check for marketing SMS files
        marketing_dir = "data/marketing_sms"
        marketing_files = []
        if os.path.exists(marketing_dir):
            marketing_files = [f for f in os.listdir(marketing_dir) if f.startswith(f"marketing_sms_{customer_id}_")]
        
        return {
            "customer_id": customer_id,
            "processed_transactions": transaction_count,
            "marketing_sms_files": len(marketing_files),
            "latest_marketing_file": marketing_files[-1] if marketing_files else None
        }
        
    except Exception as e:
        print(f"ERROR: Error getting processing status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting status: {str(e)}")

@router.delete("/clear-data/{customer_id}")
async def clear_customer_data(customer_id: str, db: Session = Depends(get_db)):
    """
    Clear all SMS processing data for a customer
    """
    try:
        from ..models.customer_models import CustomerTransaction
        
        # Delete transactions
        deleted_transactions = db.query(CustomerTransaction).filter(
            CustomerTransaction.customer_id == customer_id
        ).delete()
        
        # Delete marketing SMS files
        marketing_dir = "data/marketing_sms"
        deleted_files = 0
        if os.path.exists(marketing_dir):
            customer_files = [f for f in os.listdir(marketing_dir) if f.startswith(f"marketing_sms_{customer_id}_")]
            for file in customer_files:
                os.remove(os.path.join(marketing_dir, file))
                deleted_files += 1
        
        db.commit()
        
        return {
            "message": "Customer data cleared successfully",
            "deleted_transactions": deleted_transactions,
            "deleted_files": deleted_files
        }
        
    except Exception as e:
        db.rollback()
        print(f"ERROR: Error clearing customer data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing data: {str(e)}") 