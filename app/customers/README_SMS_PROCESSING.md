# SMS Processing Implementation

## Overview

This implementation provides a complete SMS processing pipeline that:

1. **Accepts SMS data from CSV files**
2. **Classifies SMS into two categories:**
   - Marketing/Spam/OTP messages
   - Financial messages (debit, credit, UPI, etc.)
3. **Saves marketing SMS to CSV files**
4. **Processes financial SMS with OpenAI API in parallel chunks**
5. **Extracts structured data for multiple database tables**
6. **Saves extracted data to appropriate database tables**

## Architecture

### Files Structure

```
app/customers/
├── services/
│   ├── sms_processor.py          # Main SMS processing logic
│   └── sms_constants.py          # Keywords and patterns for classification
├── routers/
│   └── sms_processor_router.py   # API endpoints
├── schemas/
│   └── sms_processor_schema.py   # Pydantic models
└── models/
    └── customer_models.py        # Database models
```

### Key Components

#### 1. SMSProcessor Class
- **Classification Logic**: Uses regex patterns to classify SMS
- **CSV Processing**: Handles file upload and parsing
- **OpenAI Integration**: Processes financial SMS in chunks
- **Database Operations**: Saves extracted transactions

#### 2. API Endpoints
- `POST /api/v1/customers/sms/process-file`: Upload and process SMS file
- `GET /api/v1/customers/sms/download-marketing-sms/{customer_id}`: Download marketing SMS
- `GET /api/v1/customers/sms/processing-status/{customer_id}`: Get processing status
- `DELETE /api/v1/customers/sms/clear-data/{customer_id}`: Clear customer data

## SMS Classification

### Marketing/Spam/OTP Messages
- **OTP Patterns**: `otp`, `password`, `verification code`, `verify`, `confirm`, etc.
- **Marketing Patterns**: `offer`, `discount`, `sale`, `promo`, `deal`, `win`, `prize`, etc.
- **Mobile Numbers**: Messages from mobile numbers are classified as marketing

### Financial Messages
- **Financial Keywords**: Bank names, fintech names, currency keywords
- **Transaction Patterns**: `debit`, `credit`, `upi`, `transfer`, `payment`, `withdrawal`, etc.
- **Amount Patterns**: `Rs.`, `₹`, numeric amounts

## Processing Flow

### 1. File Upload
```python
# Upload CSV file with SMS data
POST /api/v1/customers/sms/process-file
Content-Type: multipart/form-data
file: sms_data.csv
customer_id: "customer-123"
```

### 2. Classification
- Parse CSV file
- Classify each SMS as marketing or financial
- Save marketing SMS to CSV file
- Prepare financial SMS for OpenAI processing

### 3. OpenAI Processing
- Split financial SMS into chunks of 50
- Process each chunk in parallel with OpenAI API
- Extract structured transaction data
- Validate and clean extracted data

### 4. Database Storage
- Save extracted data to multiple tables based on SMS content:
  - `customer_transactions` - Financial transactions
  - `bank_accounts` - Bank account information
  - `credit_cards` - Credit card details
  - `upi_accounts` - UPI account information
  - `wallets` - Digital wallet details
  - `loans` - Loan information
  - `customer_salaries` - Salary credits
  - `loan_repayments` - Loan repayment records
  - `credit_card_repayments` - Credit card payment records
  - `service_accounts` - Service provider accounts
- Link all records to original SMS via `sms_id` field

## Performance Optimization

### Chunk Creation and Serial Execution
- **Step 1: Create all chunks first** - All SMS chunks are created upfront
- **Step 2: Execute chunks in series** - Each chunk is processed sequentially
- **Configurable chunk size** - Default 50 SMS per chunk (adjustable for testing)
- **Detailed chunk logging** - Each chunk is logged with SMS preview
- **Serial execution tracking** - Clear visibility into chunk-by-chunk processing
- **Easy debugging and monitoring** - Step-by-step execution flow

### Enhanced Logging
- **Step-by-step processing**: Detailed logs for each stage
- **SMS classification**: Individual SMS classification results
- **CSV processing**: Progress tracking and summary
- **OpenAI API timing**: Call duration and retry attempts
- **Chunk creation**: All chunks created upfront with detailed logging
- **Chunk execution**: Serial execution with step-by-step tracking
- **Fallback processing**: Regex extraction details
- **Record extraction**: Breakdown by table type
- **Error handling**: Retry logic and error recovery

### Expected Performance
- Processing time: < 10 seconds for 1000 SMS
- Chunk creation: Instant (all chunks created upfront)
- Serial execution: Sequential processing for better debugging
- Efficient database operations with bulk inserts
- Comprehensive logging for troubleshooting

## API Usage Examples

### Process SMS File
```bash
curl -X POST "http://localhost:8000/api/v1/customers/sms/process-file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@sms_data.csv" \
  -F "customer_id=customer-123"
```

### Download Marketing SMS
```bash
curl -X GET "http://localhost:8000/api/v1/customers/sms/download-marketing-sms/customer-123" \
  --output marketing_sms.csv
```

### Get Processing Status
```bash
curl -X GET "http://localhost:8000/api/v1/customers/sms/processing-status/customer-123"
```

## CSV Format

The input CSV should have the following columns:
- `senderAddress`: SMS sender (bank name, app name, etc.)
- `text`: SMS message content
- `phoneNumber`: Customer phone number
- `updateAt`: SMS timestamp
- `id`: Unique SMS identifier

## Error Handling & Reliability

### OpenAI API Error Handling
- **Retry Logic**: Automatic retry with exponential backoff for server errors (503, 502, 500)
- **Timeout Protection**: 60-second timeout per API call
- **Fallback Processing**: Regex-based extraction when OpenAI fails
- **Configurable**: Can disable OpenAI entirely via environment variable

### Fallback Processing
When OpenAI API is unavailable or fails, the system automatically falls back to regex-based extraction:
- **Transaction Extraction**: Basic amount, type, and platform detection
- **Account Information**: Bank account numbers and balances
- **Credit Card Data**: Card numbers and credit limits
- **Wallet Information**: Balance and app detection
- **Loan Data**: Loan amounts and types

### Configuration Options
```bash
# Disable OpenAI processing (use fallback only)
export SMS_USE_OPENAI=false

# Enable OpenAI processing (default)
export SMS_USE_OPENAI=true
```

### Error Recovery
- **File Validation**: Only CSV files are accepted
- **Database Errors**: Transaction rollback on failure
- **Data Validation**: Clean and validate extracted data
- **Graceful Degradation**: System continues processing even with API failures

## Testing

Run the test scripts to verify the implementation:

### Basic Functionality Test
```bash
python test_sms_processor.py
```

### Fallback Processing Test
```bash
python test_fallback_processing.py
```

### Testing with OpenAI Disabled
```bash
SMS_USE_OPENAI=false python test_sms_processor.py
```

### Logging Demonstration
The system provides comprehensive logging for debugging and monitoring:
- **Console output**: Real-time processing information
- **File logging**: Detailed logs saved to files
- **Performance metrics**: Processing time and record counts
- **Error tracking**: Detailed error messages and stack traces

## Configuration

### Environment Variables
- `OPENAI_API_KEY`: Required for OpenAI API access

### Database
- Uses existing customer models
- Data saved to multiple tables based on SMS content
- Links to original SMS via `sms_id` field
- Supports all customer financial data types

## Monitoring

### Enhanced Logging Features
- **Step-by-step processing**: Detailed logs for each processing stage
- **SMS classification**: Individual SMS classification with sender and message preview
- **CSV processing**: Progress tracking with row-by-row processing
- **OpenAI API timing**: Call duration, retry attempts, and response processing
- **Chunk processing**: Serial execution with detailed chunk information
- **Fallback processing**: Regex extraction with record breakdown
- **Database operations**: Save operations with record counts and table breakdown
- **Error handling**: Comprehensive error logging with retry logic

### Performance Metrics
- Processing time tracking per stage
- Success/failure logging with detailed error messages
- Transaction count monitoring across all table types
- File storage management with cleanup operations
- API call timing and retry statistics 