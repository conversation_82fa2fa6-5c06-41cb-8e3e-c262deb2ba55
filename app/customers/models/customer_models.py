from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Date, Text, DECIMAL, ForeignKey, Table
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from uuid import uuid4
from ...database import Base

class Customer(Base):
    __tablename__ = 'customer'
    __table_args__ = {'schema': 'customers'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    full_name = Column(String(255), nullable=False)
    phone_number = Column(String(50), nullable=True)
    email = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class CustomerPartnerAssociation(Base):
    __tablename__ = 'customer_partner_association'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id'), nullable=False)
    partner = Column(String(100), nullable=False)
    partner_id = Column(String(100), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class BankAccount(Base):
    __tablename__ = 'bank_accounts'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id'), nullable=False)
    account_number = Column(String(50), nullable=False)
    ifsc_code = Column(String(20), nullable=True)
    account_sub_type = Column(String(50), nullable=True)
    bank_name = Column(String(255), nullable=True)
    account_opening_date = Column(DateTime, nullable=True)
    account_closing_date = Column(DateTime, nullable=True)
    savings_interest_rate = Column(Float, nullable=True)
    status = Column(String(50), nullable=True)
    last_updated_balance_amount = Column(Float, nullable=True)
    linked_vbas = Column(String(255), nullable=True)
    last_processed_sms_date = Column(DateTime, nullable=True)
    additional_details = Column(JSONB, nullable=True)
    sms_ids = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class CreditCard(Base):
    __tablename__ = 'credit_cards'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id'), nullable=False)
    card_number = Column(String(50), nullable=False)
    card_type = Column(String(50), nullable=True)
    issuer_name = Column(String(100), nullable=True)
    network = Column(String(50), nullable=True)
    issuance_date = Column(DateTime, nullable=True)
    deactivation_date = Column(DateTime, nullable=True)
    credit_card_apr = Column(Float, nullable=True)
    status = Column(String(50), nullable=True)
    default_status = Column(Boolean, nullable=True)
    credit_line = Column(Float, nullable=True)
    last_updated_balance = Column(Float, nullable=True)
    linked_bank_account_id = Column(Integer, ForeignKey('customers.bank_accounts.id'), nullable=True)
    last_sms_date = Column(DateTime, nullable=True)
    sms_ids = Column(JSONB, nullable=True)
    additional_details = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Loan(Base):
    __tablename__ = 'loans'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    loan_id = Column(String(50), nullable=True)
    loan_type = Column(String(50), nullable=True)  # Personal, Home, Business, Auto, Gold
    lender_name = Column(String(100), nullable=True)
    issuance_date = Column(DateTime, nullable=True)
    closure_date = Column(DateTime, nullable=True)
    loan_interest_rate = Column(Float, nullable=True)
    status = Column(String(10), nullable=True)  # Active/Inactive
    default_status = Column(String(50), nullable=True)  # X Days DPD / Delinquent
    loan_tenure = Column(String(50), nullable=True)  # Store as string for INTERVAL
    loan_amount = Column(Float, nullable=True)
    pending_principal = Column(Float, nullable=True)
    pending_interest = Column(Float, nullable=True)
    linked_bank_account_id = Column(Integer, ForeignKey('customers.bank_accounts.id', ondelete='SET NULL'), nullable=True)
    last_sms_date = Column(DateTime, nullable=True)
    sms_ids = Column(JSONB, nullable=True)
    additional_details = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class UPIAccount(Base):
    __tablename__ = 'upi_accounts'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    vba = Column(String(100), nullable=True)
    provider_bank_name = Column(String(100), nullable=True)
    app_name = Column(String(100), nullable=True)
    vba_creation_date = Column(Date, nullable=True)
    vba_closing_date = Column(Date, nullable=True)
    status = Column(String(10), nullable=True)  # Active/Inactive
    linked_bank_account_id = Column(Integer, ForeignKey('customers.bank_accounts.id', ondelete='SET NULL'), nullable=True)
    last_sms_date = Column(DateTime, nullable=True)
    sms_ids = Column(JSONB, nullable=True)
    additional_details = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Wallet(Base):
    __tablename__ = 'wallets'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    account_id = Column(String(100), nullable=True)
    wallet_type = Column(String(50), nullable=True)
    app_name = Column(String(100), nullable=True)
    account_opening_date = Column(DateTime, nullable=True)
    account_closing_date = Column(DateTime, nullable=True)
    status = Column(String(50), nullable=True)
    current_balance = Column(Float, nullable=True)
    currency_breakdown = Column(JSONB, nullable=True)
    last_sms_date = Column(DateTime, nullable=True)
    sms_ids = Column(JSONB, nullable=True)
    additional_details = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class BrokerageAccount(Base):
    __tablename__ = 'brokerage_accounts'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    account_id = Column(String(100), nullable=True)
    app_name = Column(String(100), nullable=True)
    account_opening_date = Column(DateTime, nullable=True)
    account_closing_date = Column(DateTime, nullable=True)
    status = Column(String(50), nullable=True)
    stocks_amount = Column(Float, nullable=True)
    mutual_funds_amount = Column(Float, nullable=True)
    last_sms_date = Column(DateTime, nullable=True)
    sms_ids = Column(JSONB, nullable=True)
    additional_details = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class FixedDeposit(Base):
    __tablename__ = 'fixed_deposits'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    deposit_id = Column(String(100), nullable=True)
    deposit_type = Column(String(50), nullable=True)
    partner_name = Column(String(100), nullable=True)
    bank_name = Column(String(100), nullable=True)
    opening_date = Column(DateTime, nullable=True)
    maturity_date = Column(DateTime, nullable=True)
    fd_tenure = Column(String(50), nullable=True)
    fd_interest_rate = Column(Float, nullable=True)
    status = Column(String(50), nullable=True)
    initial_fd_amount = Column(Float, nullable=True)
    current_fd_amount = Column(Float, nullable=True)
    linked_bank_account_id = Column(Integer, ForeignKey('customers.bank_accounts.id', ondelete='SET NULL'), nullable=True)
    last_sms_date = Column(DateTime, nullable=True)
    sms_ids = Column(JSONB, nullable=True)
    additional_details = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class CustomerTransaction(Base):
    __tablename__ = 'customer_transactions'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    source_type = Column(String(50), nullable=False)  # e.g., bank_account, credit_card, loan, upi_account, wallet, brokerage_account, fixed_deposit, service_account
    source_id = Column(Integer, nullable=False)  # ID from the respective table
    from_account_id = Column(String(100), nullable=True)
    to_account_id = Column(String(100), nullable=True)
    amount = Column(Float, nullable=False)
    currency = Column(String(10), nullable=True)
    transaction_date = Column(DateTime, nullable=False)
    platform = Column(String(100), nullable=True)
    txn_status = Column(String(50), nullable=True)
    information_type = Column(String(100), nullable=True)
    transaction_type = Column(String(50), nullable=False)  # e.g., debit, credit, payment, investment
    transaction_sub_type = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    status = Column(String(50), nullable=True)
    reference_id = Column(String(100), nullable=True)
    additional_details = Column(JSONB, nullable=True)
    sms_id = Column(Integer, ForeignKey('customers.sms_list.id', ondelete='SET NULL'), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class CustomerEvent(Base):
    __tablename__ = 'customer_events'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    entity_type = Column(String(50), nullable=False)  # e.g., bank_account, credit_card, loan, upi_account, wallet, brokerage_account, fixed_deposit, service_account, transaction
    entity_id = Column(Integer, nullable=False)  # ID from the respective table
    source = Column(String(100), nullable=True)  # Source of the event (e.g., system, user, api, sms, etc.)
    event_type = Column(String(100), nullable=False)  # e.g., created, updated, deleted, alert, etc.
    information_type = Column(String(100), nullable=True)
    event_sub_type = Column(String(100), nullable=True)  # Reconcile with Transaction Sub-Types
    event_source = Column(String(100), nullable=True)  # e.g., web, mobile, api, sms, etc.
    event_datetime = Column(DateTime, nullable=False, default=datetime.now)
    platform = Column(String(100), nullable=True)
    status = Column(String(50), nullable=True)
    description = Column(Text, nullable=True)
    reference_id = Column(String(100), nullable=True)
    additional_details = Column(JSONB, nullable=True)
    sms_id = Column(Integer, ForeignKey('customers.sms_list.id', ondelete='SET NULL'), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class SMSList(Base):
    __tablename__ = 'sms_list'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    sms = Column(Text, nullable=False)
    data = Column(JSONB, nullable=True)
    sms_date = Column(DateTime, nullable=True)
    sms_ref_id = Column(String(100), nullable=True)
    partner = Column(String(100), nullable=True)
    service_area = Column(String(100), nullable=True)
    service_provider = Column(String(100), nullable=True)
    entity_name = Column(String(100), nullable=True)
    sms_header = Column(String(100), nullable=True)
    direction = Column(String(20), nullable=True)  # incoming/outgoing
    status = Column(String(50), nullable=True)
    processed = Column(Boolean, default=False)
    reason = Column(String(255), nullable=True)  # Reason if SMS was not processed
    error_code = Column(String(50), nullable=True)  # Optional error code
    error_details = Column(JSONB, nullable=True)  # Optional error details in JSONB
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class ServiceProviderList(Base):
    __tablename__ = 'service_provider_list'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    service_provider = Column(String(500), nullable=False)
    code = Column(String(5), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class ServiceAreaList(Base):
    __tablename__ = 'service_area_list'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    service_area = Column(String(500), nullable=False)
    code = Column(String(5), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class ServiceEntities(Base):
    __tablename__ = 'service_entities_list'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    service_entity = Column(String(500), nullable=False)
    code = Column(String(20), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class ServiceAccount(Base):
    __tablename__ = 'service_accounts'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    account_type = Column(String(100), nullable=False)  # e.g., 'Zomato', 'Swiggy', 'Ola', 'Uber', 'Amazon', 'Flipkart', etc.
    app_name = Column(String(100), nullable=True)
    account_id = Column(String(100), nullable=True)  # Account ID/username if available
    account_opening_date = Column(DateTime, nullable=True)
    status = Column(String(50), nullable=True)
    total_amount_spent = Column(Float, nullable=True, default=0.0)
    last_transaction_date = Column(DateTime, nullable=True)
    last_sms_date = Column(DateTime, nullable=True)
    sms_ids = Column(JSONB, nullable=True)
    additional_details = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class CustomerSalary(Base):
    __tablename__ = 'customer_salaries'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    bank_account_id = Column(Integer, ForeignKey('customers.bank_accounts.id', ondelete='CASCADE'), nullable=False)
    employer_name = Column(String(255), nullable=True)
    salary_amount = Column(Float, nullable=False)
    salary_date = Column(DateTime, nullable=False)
    salary_month = Column(Integer, nullable=True)  # 1-12 for month
    salary_year = Column(Integer, nullable=True)
    salary_type = Column(String(50), nullable=True)  # e.g., 'monthly', 'bonus', 'increment', 'arrears'
    reference_id = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    currency = Column(String(10), nullable=True, default='INR')
    additional_details = Column(JSONB, nullable=True)
    sms_id = Column(Integer, ForeignKey('customers.sms_list.id', ondelete='SET NULL'), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class LoanRepayment(Base):
    __tablename__ = 'loan_repayments'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    loan_id = Column(Integer, ForeignKey('customers.loans.id', ondelete='CASCADE'), nullable=False)
    bank_account_id = Column(Integer, ForeignKey('customers.bank_accounts.id', ondelete='SET NULL'), nullable=True)
    repayment_amount = Column(Float, nullable=False)
    principal_amount = Column(Float, nullable=True)
    interest_amount = Column(Float, nullable=True)
    penalty_amount = Column(Float, nullable=True)
    repayment_date = Column(DateTime, nullable=False)
    due_date = Column(DateTime, nullable=True)
    emi_number = Column(Integer, nullable=True)
    repayment_type = Column(String(50), nullable=True)  # e.g., 'emi', 'prepayment', 'partial', 'full_closure'
    payment_status = Column(String(50), nullable=True)  # e.g., 'on_time', 'late', 'partial', 'failed'
    days_delayed = Column(Integer, nullable=True, default=0)
    outstanding_balance = Column(Float, nullable=True)
    reference_id = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    currency = Column(String(10), nullable=True, default='INR')
    additional_details = Column(JSONB, nullable=True)
    sms_id = Column(Integer, ForeignKey('customers.sms_list.id', ondelete='SET NULL'), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class CreditCardRepayment(Base):
    __tablename__ = 'credit_card_repayments'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    credit_card_id = Column(Integer, ForeignKey('customers.credit_cards.id', ondelete='CASCADE'), nullable=False)
    bank_account_id = Column(Integer, ForeignKey('customers.bank_accounts.id', ondelete='SET NULL'), nullable=True)
    repayment_amount = Column(Float, nullable=False)
    minimum_due = Column(Float, nullable=True)
    total_due = Column(Float, nullable=True)
    repayment_date = Column(DateTime, nullable=False)
    due_date = Column(DateTime, nullable=True)
    billing_cycle_start = Column(Date, nullable=True)
    billing_cycle_end = Column(Date, nullable=True)
    repayment_type = Column(String(50), nullable=True)  # e.g., 'minimum', 'full', 'partial', 'auto_debit'
    payment_status = Column(String(50), nullable=True)  # e.g., 'on_time', 'late', 'partial', 'failed', 'missed'
    days_delayed = Column(Integer, nullable=True, default=0)
    outstanding_balance = Column(Float, nullable=True)
    late_fee_charged = Column(Float, nullable=True)
    interest_charged = Column(Float, nullable=True)
    cashback_earned = Column(Float, nullable=True)
    reward_points_earned = Column(Integer, nullable=True)
    reference_id = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    currency = Column(String(10), nullable=True, default='INR')
    additional_details = Column(JSONB, nullable=True)
    sms_id = Column(Integer, ForeignKey('customers.sms_list.id', ondelete='SET NULL'), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class CustomerFinancialHealth(Base):
    __tablename__ = 'customer_financial_health'
    __table_args__ = {'schema': 'customers'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey('customers.customer.id', ondelete='CASCADE'), nullable=False)
    assessment_date = Column(DateTime, nullable=False, default=datetime.now)
    salary_consistency_score = Column(Float, nullable=True)  # 0-100 score
    loan_repayment_score = Column(Float, nullable=True)  # 0-100 score
    credit_card_repayment_score = Column(Float, nullable=True)  # 0-100 score
    overall_financial_health_score = Column(Float, nullable=True)  # 0-100 score
    avg_monthly_salary = Column(Float, nullable=True)
    total_loan_amount = Column(Float, nullable=True)
    total_credit_card_debt = Column(Float, nullable=True)
    debt_to_income_ratio = Column(Float, nullable=True)
    missed_loan_payments_count = Column(Integer, nullable=True, default=0)
    missed_cc_payments_count = Column(Integer, nullable=True, default=0)
    late_loan_payments_count = Column(Integer, nullable=True, default=0)
    late_cc_payments_count = Column(Integer, nullable=True, default=0)
    last_salary_date = Column(DateTime, nullable=True)
    risk_category = Column(String(50), nullable=True)  # e.g., 'low', 'medium', 'high', 'very_high'
    additional_details = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

