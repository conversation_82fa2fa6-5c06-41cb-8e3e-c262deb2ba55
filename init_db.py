from app.database import engine, Base
from app.models import models
from app.ipo.models.listing_models import *  # Import IPO models directly
from app.ipo.models.document_models import *  # Import IPO document models
from app.utils.seed_merchant import seed_merchant_data
import logging
from sqlalchemy import text

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

def init_database(reset_db: bool = False):
    try:
        if reset_db:
            logger.info("Resetting the database...")
            with engine.connect() as connection:
                connection.execute(text("COMMIT"))
                
                # Drop and recreate public schema
                result = connection.execute(text("SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'public'"))
                if result.fetchone():
                    connection.execute(text("DROP SCHEMA public CASCADE"))
                connection.execute(text("CREATE SCHEMA public"))
                connection.execute(text("GRANT ALL ON SCHEMA public TO public"))
                
                # Drop and recreate ipo schema
                result = connection.execute(text("SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'ipo'"))
                if result.fetchone():
                    connection.execute(text("DROP SCHEMA ipo CASCADE"))
                connection.execute(text("CREATE SCHEMA ipo"))
                connection.execute(text("GRANT ALL ON SCHEMA ipo TO public"))
                
                # Drop and recreate customers schema
                result = connection.execute(text("SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'customers'"))
                if result.fetchone():
                    connection.execute(text("DROP SCHEMA customers CASCADE"))
                connection.execute(text("CREATE SCHEMA customers"))
                connection.execute(text("GRANT ALL ON SCHEMA customers TO public"))
                
                connection.commit()
        else:
            # Ensure ipo schema exists even when not resetting
            logger.info("Ensuring ipo schema exists...")
            with engine.connect() as connection:
                result = connection.execute(text("SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'ipo'"))
                if not result.fetchone():
                    connection.execute(text("CREATE SCHEMA ipo"))
                    connection.execute(text("GRANT ALL ON SCHEMA ipo TO public"))
                    connection.commit()
            
            # Ensure customers schema exists even when not resetting
            logger.info("Ensuring customers schema exists...")
            with engine.connect() as connection:
                result = connection.execute(text("SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'customers'"))
                if not result.fetchone():
                    connection.execute(text("CREATE SCHEMA customers"))
                    connection.execute(text("GRANT ALL ON SCHEMA customers TO public"))
                connection.commit()
        
        logger.info("Creating database tables if they don't exist...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialization completed successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise

if __name__ == "__main__":
    init_database()