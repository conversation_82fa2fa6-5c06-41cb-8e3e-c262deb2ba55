import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.database import get_db, Base
import os

# Test database URL - use SQLite for testing
SQLITE_DATABASE_URL = "sqlite:///./test_sms_events.db"

engine = create_engine(
    SQLITE_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)

@pytest.fixture(scope="module")
def setup_database():
    # Create the database tables
    Base.metadata.create_all(bind=engine)
    yield
    # Clean up
    Base.metadata.drop_all(bind=engine)
    if os.path.exists("./test_sms_events.db"):
        os.remove("./test_sms_events.db")

def test_get_sms_events_endpoint(setup_database):
    """Test the GET /api/v1/customers/sms-events/ endpoint"""
    response = client.get("/api/v1/customers/sms-events/")
    assert response.status_code == 200
    
    data = response.json()
    assert "events" in data
    assert "total" in data
    assert "page" in data
    assert "limit" in data
    assert "hasMore" in data
    assert isinstance(data["events"], list)

def test_get_sms_events_pagination(setup_database):
    """Test pagination parameters"""
    response = client.get("/api/v1/customers/sms-events/?page=1&limit=10")
    assert response.status_code == 200
    
    data = response.json()
    assert data["page"] == 1
    assert data["limit"] == 10

def test_get_sms_events_summary(setup_database):
    """Test the GET /api/v1/customers/sms-events/summary endpoint"""
    response = client.get("/api/v1/customers/sms-events/summary")
    assert response.status_code == 200
    
    data = response.json()
    assert "summary" in data
    assert "total_events" in data
    assert isinstance(data["summary"], dict)

def test_search_sms_events(setup_database):
    """Test the POST /api/v1/customers/sms-events/search endpoint"""
    search_data = {
        "page": 1,
        "limit": 20
    }
    
    response = client.post("/api/v1/customers/sms-events/search", json=search_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "events" in data
    assert "total" in data

def test_invalid_pagination_params():
    """Test invalid pagination parameters"""
    # Test page < 1
    response = client.get("/api/v1/customers/sms-events/?page=0")
    assert response.status_code == 422
    
    # Test limit > 100
    response = client.get("/api/v1/customers/sms-events/?limit=101")
    assert response.status_code == 422

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
